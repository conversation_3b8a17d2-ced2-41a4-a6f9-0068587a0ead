# Managing API Keys Securely

This document outlines the best practices for managing API keys in the Wodaabe Stays application.

## Overview

API keys and other sensitive credentials should never be hardcoded in the source code or committed to version control. This guide explains how to properly manage API keys in this project.

## Environment Variables

We use environment variables to store sensitive information like API keys. These are managed in several ways:

1. **Local Development**: Using `.env` files with `react-native-dotenv`
2. **EAS Builds**: Using environment variables in `eas.json`
3. **Centralized Configuration**: Using `src/config/env.ts` to access environment variables with fallbacks

## Setting Up API Keys

### For Local Development

1. Create a `.env` file in the root of the project (this file should be in `.gitignore`)
2. Add your API keys:

```env
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
API_BASE_URL=your_api_base_url
```

3. Make sure `.env` is listed in your `.gitignore` file to prevent it from being committed

### For EAS Builds

#### Option 1: Using eas.json (Less Secure)

Replace the placeholders in `eas.json` with your actual API keys:

```json
{
  "build": {
    "development": {
      "env": {
        "GOOGLE_MAPS_API_KEY": "your_google_maps_api_key"
      }
    }
  }
}
```

**Warning**: This approach is less secure as the API keys will be visible in your repository.

#### Option 2: Using EAS Secrets (Recommended)

Store your API keys as EAS secrets:

```bash
# Create a secret for the Google Maps API key
eas secret:create --name GOOGLE_MAPS_API_KEY --value your_google_maps_api_key --scope project
```

Then in `eas.json`, reference the secret by name:

```json
{
  "build": {
    "production": {
      "env": {
        "GOOGLE_MAPS_API_KEY": "${GOOGLE_MAPS_API_KEY}"
      }
    }
  }
}
```

#### Option 3: Using a Private eas.json (Alternative)

1. Rename the existing `eas.json` to `eas.template.json` and commit that to the repository
2. Add `eas.json` to your `.gitignore` file
3. Create a local copy of `eas.json` with your actual API keys
4. Document this process for other developers

## Using API Keys in Code

Always import API keys from the centralized configuration:

```typescript
import { GOOGLE_MAPS_API_KEY } from '@/config/env';
```

The `env.ts` file provides fallback mechanisms if the environment variables are missing, but it's best to ensure they are properly set.

## Native Configuration

For native modules that require API keys (like Google Maps), the keys are configured in:

- iOS: `app.config.ts` under `ios.config.googleMapsApiKey`
- Android: `app.config.ts` under `extra.googleMapsApiKey`

These values are populated from environment variables at build time.

## Security Best Practices

1. **Never hardcode API keys** in source code
2. **Use API key restrictions** in the Google Cloud Console to limit usage to your app:
   - Restrict by application (Android package name or iOS bundle ID)
   - Restrict by API (only enable the specific APIs you need)
   - Set up quotas to limit usage
3. **Use different API keys** for development and production
4. **Regularly rotate API keys** for enhanced security
5. **Monitor API key usage** for unusual patterns
6. **Use EAS Secrets** for production builds

## Troubleshooting

If API keys are missing in builds:

1. Check that the key is properly defined in `.env` for local development
2. Check that the key is properly defined in `eas.json` for EAS builds
3. Verify that the key is being properly accessed in `src/config/env.ts`
4. Check the build logs for any environment variable issues
5. For EAS builds with secrets, ensure the secrets are properly created and referenced

## What to Commit vs. Not Commit

### DO Commit

- `src/config/env.ts` (without any actual API keys)
- `eas.template.json` (with placeholders for API keys)
- `app.config.ts` (with environment variable references)
- `API_KEYS.md` (this documentation)

### DO NOT Commit

- `.env` file with actual API keys
- `eas.json` with actual API keys (if using Option 3)
- Any file with hardcoded API keys
