import 'react-native-gesture-handler';
import 'react-native-reanimated';
import { SystemBars } from 'react-native-edge-to-edge';
import { NavigationContainer } from '@react-navigation/native';
import Navigation from './src/navigation/index';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as Notifications from 'expo-notifications';
import { View } from 'react-native';
import { EdgeToEdgeDebugInfo } from './src/utils/edgeToEdgeDebug';
import { AutoUpdateChecker } from './src/components/AutoUpdateChecker';

import './ReactotronConfig';
import { NotificationProvider } from '@/context/NotificationContext';

const queryClient = new QueryClient();

// Configure notification handler to show alerts, play sounds, and set badges
Notifications.setNotificationHandler({
  handleNotification: async () => {
    console.log('Notification handler called');
    return {
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    };
  },
});

// Edge-to-edge wrapper component for consistent behavior
function EdgeToEdgeWrapper({ children }: { children: React.ReactNode }) {
  // Use 'dark' style for consistent dark text on light backgrounds
  // This ensures status bar content is always visible on white/light backgrounds
  return (
    <View style={{ flex: 1 }}>
      <SystemBars
        style="dark"
        hidden={false}
      />
      {children}
    </View>
  );
}

export default function App() {
  // Log edge-to-edge debug info on app start
  EdgeToEdgeDebugInfo.logDebugInfo();

  return (
    <NotificationProvider>
      <SafeAreaProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <KeyboardProvider>
            <QueryClientProvider client={queryClient}>
              <EdgeToEdgeWrapper>
                <NavigationContainer>
                  <Navigation />
                </NavigationContainer>
                <AutoUpdateChecker />
              </EdgeToEdgeWrapper>
            </QueryClientProvider>
          </KeyboardProvider>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </NotificationProvider>
  );
}