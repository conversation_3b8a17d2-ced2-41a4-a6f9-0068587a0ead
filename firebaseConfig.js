// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { API_BASE_URL } from './src/config/env';
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Base Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const baseFirebaseConfig = {
  apiKey: 'AIzaSyBv6Cr15e77_iBxZLUuk9ep35UslBH5G9E',
  authDomain: 'wodaabe-3d883.firebaseapp.com',
  databaseURL:
    'https://wodaabe-3d883-default-rtdb.europe-west1.firebasedatabase.app',
  projectId: 'wodaabe-3d883',
  messagingSenderId: '240061113680',
  appId: '1:240061113680:web:79781dcdb31bdc94edb855',
  measurementId: 'G-HR898X3GVT',
};

// Determine if we're in production based on API URL
const isProduction =
  API_BASE_URL.includes('api.wodaabe-stays.com') &&
  !API_BASE_URL.includes('staging');

// Override __DEV__ if API URL indicates we're in production
const isDevelopmentMode = isProduction ? false : __DEV__;

// Set environment-specific configurations
const firebaseConfig = {
  ...baseFirebaseConfig,
  storageBucket: isDevelopmentMode
    ? 'wodaabe-3d883.firebasestorage.app'
    : 'wodaabe-3d883-prod',
};

// Log which environment is being used
console.log(`API URL: ${API_BASE_URL}`);
console.log(
  `Using Firebase ${
    isDevelopmentMode ? 'development' : 'production'
  } environment`
);
console.log(`Storage bucket: ${firebaseConfig.storageBucket}`);

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const firebaseStorage = getStorage(app);

// Initialize Firestore
let firestoreConfig = {};

// In production mode, specify the database ID
if (!isDevelopmentMode) {
  // This is the correct way to specify a non-default database
  firestoreConfig = 'wodaabe-prod';
}

// Initialize Firestore with the appropriate configuration
export const firestoreDatabase = getFirestore(app, firestoreConfig);

// Log the database being used
console.log(
  `Using Firestore database: ${isDevelopmentMode ? 'default' : 'wodaabe-prod'}`
);
console.log('Firestore config:', JSON.stringify(firestoreConfig));
