// Custom plugin to remove unwanted permissions
const { withAndroidManifest } = require('@expo/config-plugins');

const withRemovePermissions = (config) => {
  return withAndroidManifest(config, async (config) => {
    const androidManifest = config.modResults;
    
    // Define permissions to remove
    const permissionsToRemove = [
      'android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK',
      'android.permission.FOREGROUND_SERVICE',
      'android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION'
    ];
    
    // Get the manifest's uses-permission elements
    const manifestPermissions = androidManifest.manifest['uses-permission'] || [];
    
    // Filter out the permissions we want to remove
    androidManifest.manifest['uses-permission'] = manifestPermissions.filter(
      (permission) => {
        const name = permission.$?.['android:name'];
        return !permissionsToRemove.includes(name);
      }
    );
    
    // Add tools:node="remove" for each permission we want to remove
    permissionsToRemove.forEach(permission => {
      androidManifest.manifest['uses-permission'].push({
        $: {
          'android:name': permission,
          'tools:node': 'remove'
        }
      });
    });
    
    return config;
  });
};

module.exports = withRemovePermissions;
