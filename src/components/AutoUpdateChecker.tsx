import { useEffect } from 'react';
import { useUpdateCheck } from '../hooks/useUpdateCheck';

export function AutoUpdateChecker() {
  const { checkForUpdates } = useUpdateCheck();
  
  useEffect(() => {
    // Check for updates automatically on app launch
    // Delay by 3 seconds to allow app to fully initialize
    const timer = setTimeout(() => {
      checkForUpdates(true); // showAlert = true for automatic checks
    }, 3000);

    return () => clearTimeout(timer);
  }, []); // Remove checkForUpdates from dependency array

  // This component doesn't render anything
  return null;
}