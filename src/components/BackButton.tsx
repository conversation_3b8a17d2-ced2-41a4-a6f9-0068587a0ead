import React, { useEffect, useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
  ViewStyle,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface BackButtonProps {
  color?: string;
  size?: number;
  style?: ViewStyle;
  onPress?: () => void;
}

const BackButton: React.FC<BackButtonProps> = ({
  color = '#333',
  size = 24,
  style,
  onPress,
}) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateX = useRef(new Animated.Value(-20)).current;
  const wiggleAnim = useRef(new Animated.Value(0)).current;

  // Calculate the top position based on safe area insets
  const topPosition = Math.max(insets.top, 20);

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateX, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Start wiggle animation after entrance animation completes
      startWiggleAnimation();
    });
  }, []);

  const startWiggleAnimation = () => {
    // Wait a bit before starting the wiggle
    setTimeout(() => {
      Animated.sequence([
        Animated.timing(wiggleAnim, {
          toValue: -3,
          duration: 150,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(wiggleAnim, {
          toValue: 3,
          duration: 150,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(wiggleAnim, {
          toValue: -2,
          duration: 150,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(wiggleAnim, {
          toValue: 2,
          duration: 150,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(wiggleAnim, {
          toValue: 0,
          duration: 150,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ]).start();
    }, 1000);
  };

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (onPress) {
      onPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        style,
        {
          opacity: fadeAnim,
          transform: [
            { translateX },
            { translateX: wiggleAnim },
          ],
          // Apply dynamic top position based on safe area insets
          top: topPosition,
          // Adjust left position to avoid collision with status bar clock
          left: Platform.OS === 'ios' ? Math.max(insets.left + 16, 24) : 16,
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          // Make the touch target larger on iOS
          Platform.OS === 'ios' && styles.buttonIOS
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons name="arrow-back" size={size} color={color} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 10,
  },
  button: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  // Larger button for iOS to make it easier to tap
  buttonIOS: {
    width: 44,
    height: 44,
    borderRadius: 22,
    // Move the icon slightly to the right to avoid the notch/clock
    paddingLeft: 2,
  },
});

export default BackButton;
