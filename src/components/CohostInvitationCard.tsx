import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { httpClient } from '@/utils/http';

interface CohostInvitation {
  uuid: string;
  status: 'pending' | 'accepted' | 'rejected' | 'cancelled';
  coHost: {
    uuid: string;
    displayName: string;
    email: string;
  };
  createdAt: string;
}

interface CohostInvitationCardProps {
  invitation: CohostInvitation;
  onDelete: (uuid: string) => void;
}

const CohostInvitationCard: React.FC<CohostInvitationCardProps> = ({ invitation, onDelete }) => {
  const { t, i18n } = useTranslation();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  // Format the date based on current language
  const formatDate = (dateString: string) => {
    try {
      // Use French locale if language is French, otherwise use default (English)
      const locale = i18n.language === 'fr' ? fr : undefined;

      // Format pattern based on language
      const pattern = i18n.language === 'fr' ? 'd MMM yyyy' : 'MMM d, yyyy';

      return format(parseISO(dateString), pattern, { locale });
    } catch (error) {
      return dateString;
    }
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: '#FFC107', // Amber
          backgroundColor: '#FFF8E1', // Light amber
          icon: 'schedule' as const,
          text: t('common.invitation_status.pending')
        };
      case 'accepted':
        return {
          color: '#4CAF50', // Green
          backgroundColor: '#E8F5E9', // Light green
          icon: 'check-circle' as const,
          text: t('common.invitation_status.accepted')
        };
      case 'rejected':
        return {
          color: '#F44336', // Red
          backgroundColor: '#FFEBEE', // Light red
          icon: 'cancel' as const,
          text: t('common.invitation_status.rejected')
        };
      case 'cancelled':
        return {
          color: '#757575', // Grey
          backgroundColor: '#EEEEEE', // Light grey
          icon: 'block' as const,
          text: t('common.invitation_status.cancelled')
        };
      default:
        return {
          color: '#9E9E9E', // Grey
          backgroundColor: '#F5F5F5', // Light grey
          icon: 'help' as const,
          text: status
        };
    }
  };

  const statusInfo = getStatusInfo(invitation.status);

  // Handle delete invitation
  const handleDelete = async () => {
    Alert.alert(
      t('common.invitation_card.delete_title'),
      t('common.invitation_card.delete_message'),
      [
        {
          text: t('common.buttons.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.buttons.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeleting(true);
              await httpClient.delete(`/housings/cohost-invitation/${invitation.uuid}`);
              onDelete(invitation.uuid);
            } catch (error) {
              console.error('Error deleting invitation:', error);
              Alert.alert(
                t('common.errors.title'),
                t('common.invitation_card.delete_error')
              );
            } finally {
              setIsDeleting(false);
            }
          }
        }
      ]
    );
  };

  // Handle cancel invitation (only for pending status)
  const handleCancel = async () => {
    Alert.alert(
      t('common.invitation_card.cancel_title'),
      t('common.invitation_card.cancel_message'),
      [
        {
          text: t('common.buttons.back'),
          style: 'cancel'
        },
        {
          text: t('common.buttons.confirm'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsCancelling(true);
              await httpClient.delete(`/housings/cohost-invitation/${invitation.uuid}/cancel`);
              onDelete(invitation.uuid); // Use the same callback to refresh the list
            } catch (error) {
              console.error('Error canceling invitation:', error);
              Alert.alert(
                t('common.errors.title'),
                t('common.invitation_card.cancel_error')
              );
            } finally {
              setIsCancelling(false);
            }
          }
        }
      ]
    );
  };

  return (
    <View style={[styles.card, { borderLeftColor: statusInfo.color }]}>
      <View style={styles.cardContent}>
        <View style={styles.userInfo}>
          <View style={[styles.avatarCircle, { backgroundColor: statusInfo.color }]}>
            <Text style={styles.avatarText}>
              {invitation.coHost.displayName.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{invitation.coHost.displayName}</Text>
            <Text style={styles.userEmail}>{invitation.coHost.email}</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.backgroundColor }]}>
            <MaterialIcons name={statusInfo.icon} size={14} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.text}
            </Text>
          </View>
          <Text style={styles.dateText}>
            {formatDate(invitation.createdAt)}
          </Text>
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        {invitation.status === 'pending' && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCancel}
            disabled={isCancelling || isDeleting}
          >
            {isCancelling ? (
              <ActivityIndicator size="small" color="#FF9800" />
            ) : (
              <MaterialIcons name={"block" as const} size={20} color="#FF9800" />
            )}
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDelete}
          disabled={isDeleting || isCancelling}
        >
          {isDeleting ? (
            <ActivityIndicator size="small" color="#F44336" />
          ) : (
            <MaterialIcons name={"delete" as const} size={20} color="#F44336" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    overflow: 'hidden',
  },
  cardContent: {
    padding: 16,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  buttonsContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CohostInvitationCard;
