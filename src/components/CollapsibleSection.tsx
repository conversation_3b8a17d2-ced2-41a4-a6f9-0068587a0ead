import React, { useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const CollapsibleSection = ({ title, children }) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <View>
      <TouchableOpacity onPress={() => setIsCollapsed(!isCollapsed)}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', margin: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: '600' }}>{title}</Text>
          <MaterialIcons name={isCollapsed ? 'keyboard-arrow-down' : 'keyboard-arrow-up'} size={24} color="black" />
        </View>
      </TouchableOpacity>
      {!isCollapsed && children}
    </View>
  );
};

export default CollapsibleSection;