import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolate,
  Layout,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

interface FAQ {
  id?: number;
  uuid: string;
  question: string;
  answer: string;
}

interface FAQDisplaySectionProps {
  faqs: FAQ[];
  title?: string;
}

const FAQDisplaySection: React.FC<FAQDisplaySectionProps> = ({ 
  faqs, 
  title 
}) => {
  const { t } = useTranslation();
  const [expandedFAQs, setExpandedFAQs] = useState<string[]>([]);

  // Toggle FAQ expansion
  const toggleFAQ = (uuid: string) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    
    setExpandedFAQs(prev => {
      if (prev.includes(uuid)) {
        return prev.filter(id => id !== uuid);
      } else {
        return [...prev, uuid];
      }
    });
  };

  // Check if a FAQ is expanded
  const isFAQExpanded = (uuid: string) => expandedFAQs.includes(uuid);

  // Render empty state
  if (!faqs || faqs.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{title || t('common.faq_section.title')}</Text>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="help-outline" size={48} color="#ccc" />
          <Text style={styles.emptyText}>{t('common.faq_section.no_faqs')}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title || t('common.faq_section.title')}</Text>
      
      {faqs.map((faq) => (
        <FAQItem 
          key={faq.uuid} 
          faq={faq} 
          isExpanded={isFAQExpanded(faq.uuid)} 
          onToggle={() => toggleFAQ(faq.uuid)} 
        />
      ))}
    </View>
  );
};

interface FAQItemProps {
  faq: FAQ;
  isExpanded: boolean;
  onToggle: () => void;
}

const FAQItem: React.FC<FAQItemProps> = ({ faq, isExpanded, onToggle }) => {
  // Animation values
  const rotateValue = useSharedValue(0);
  
  // Update rotation when expanded state changes
  React.useEffect(() => {
    rotateValue.value = withTiming(isExpanded ? 1 : 0, { duration: 300 });
  }, [isExpanded]);
  
  // Animated styles
  const iconStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      rotateValue.value,
      [0, 1],
      [0, 180],
      Extrapolate.CLAMP
    );
    
    return {
      transform: [{ rotate: `${rotation}deg` }]
    };
  });

  return (
    <Animated.View 
      style={styles.faqItem}
      layout={Layout.springify()}
      entering={FadeIn.duration(300)}
    >
      <TouchableOpacity 
        style={[
          styles.faqHeader,
          isExpanded && styles.faqHeaderExpanded
        ]} 
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <Text style={styles.faqQuestion}>{faq.question}</Text>
        <Animated.View style={iconStyle}>
          <MaterialIcons 
            name="keyboard-arrow-down" 
            size={24} 
            color="#4682B4" 
          />
        </Animated.View>
      </TouchableOpacity>
      
      {isExpanded && (
        <Animated.View 
          style={styles.faqAnswerContainer}
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(200)}
        >
          <Text style={styles.faqAnswer}>{faq.answer}</Text>
        </Animated.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  faqItem: {
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  faqHeaderExpanded: {
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  faqAnswerContainer: {
    padding: 16,
    backgroundColor: '#fff',
  },
  faqAnswer: {
    fontSize: 15,
    lineHeight: 22,
    color: '#666',
  },
});

export default FAQDisplaySection;
