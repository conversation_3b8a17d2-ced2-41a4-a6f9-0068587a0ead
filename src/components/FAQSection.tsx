import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Keyboard,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions
} from 'react-native';
import { storage } from '@/utils/storage';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withSpring,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import { httpClient } from '@/utils/http';

// Define interfaces for better type safety
interface FAQ {
  uuid: string;
  question: string;
  answer: string;
  isEditing?: boolean;
  isNew?: boolean;
}

interface FAQSectionProps {
  housingUuid: string;
  initialFaqs?: FAQ[];
  onFaqsChange?: (faqs: FAQ[]) => void;
}

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

const FAQSection: React.FC<FAQSectionProps> = ({ housingUuid, initialFaqs = [], onFaqsChange }) => {
  const { t } = useTranslation();
  const [faqs, setFaqs] = useState<FAQ[]>(initialFaqs);
  const [newQuestion, setNewQuestion] = useState('');
  const [newAnswer, setNewAnswer] = useState('');
  const [isAddingFaq, setIsAddingFaq] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // Use refs to persist form data across re-renders/remounts
  const persistedFormData = useRef({
    question: '',
    answer: ''
  });

  // MMKV key for persisting form data
  const getStorageKey = () => `faq_form_data_${housingUuid}`;

  // Load persisted form data from MMKV
  const loadPersistedFormData = () => {
    try {
      const storageKey = getStorageKey();
      const persistedData = storage.getString(storageKey);
      if (persistedData) {
        const { question, answer } = JSON.parse(persistedData);
        persistedFormData.current = { question, answer };
        setNewQuestion(question);
        setNewAnswer(answer);
      }
    } catch (error) {
      console.error('Error loading persisted form data:', error);
    }
  };

  // Save form data to MMKV
  const saveFormDataToStorage = (question: string, answer: string) => {
    try {
      const storageKey = getStorageKey();
      const dataToSave = { question, answer };
      storage.set(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving form data to storage:', error);
    }
  };

  // Clear persisted form data from MMKV
  const clearPersistedFormData = () => {
    try {
      const storageKey = getStorageKey();
      storage.delete(storageKey);
    } catch (error) {
      console.error('Error clearing persisted form data:', error);
    }
  };

  // Get screen dimensions for responsive design
  const screenHeight = Dimensions.get('window').height;

  // Animation values
  const addButtonScale = useSharedValue(1);

  // Initialize faqs from initialFaqs prop and sync when it changes
  useEffect(() => {
    setFaqs(initialFaqs);
  }, [initialFaqs]);

  // Initialize form data from MMKV on mount
  useEffect(() => {
    loadPersistedFormData();
  }, []);

  // Keyboard event listeners for better UX
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (event) => {
      setKeyboardHeight(event.endCoordinates.height);
      // Scroll to bottom when keyboard appears
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 150);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Update parent component when FAQs change, but only for user actions
  const updateParentFaqs = useCallback((updatedFaqs: FAQ[]) => {
    if (onFaqsChange) {
      onFaqsChange(updatedFaqs);
    }
  }, [onFaqsChange]);

  // Animated styles
  const addButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: addButtonScale.value }]
    };
  });

  // Update form data and persist it
  const updateNewQuestion = (text: string) => {
    setNewQuestion(text);
    persistedFormData.current.question = text;
    saveFormDataToStorage(text, persistedFormData.current.answer);
  };

  const updateNewAnswer = (text: string) => {
    setNewAnswer(text);
    persistedFormData.current.answer = text;
    saveFormDataToStorage(persistedFormData.current.question, text);
  };

  // Check if there's unsaved data
  const hasUnsavedData = () => {
    return newQuestion.trim() !== '' || newAnswer.trim() !== '';
  };

  // Clear form data
  const clearFormData = () => {
    setNewQuestion('');
    setNewAnswer('');
    persistedFormData.current.question = '';
    persistedFormData.current.answer = '';
    clearPersistedFormData();
  };

  // Toggle add FAQ form
  const toggleAddForm = () => {
    addButtonScale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    if (!isAddingFaq) {
      setIsAddingFaq(true);

      // Scroll to the bottom after the form appears
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 350);
    } else {
      // Check if there's unsaved data before closing
      if (hasUnsavedData()) {
        Alert.alert(
          t('common.faq_section.unsaved_changes_title'),
          t('common.faq_section.unsaved_changes_message'),
          [
            {
              text: t('common.buttons.keep_editing'),
              style: 'cancel'
            },
            {
              text: t('common.buttons.discard'),
              style: 'destructive',
              onPress: () => {
                clearFormData();
                Keyboard.dismiss();
                setIsAddingFaq(false);
              }
            }
          ]
        );
      } else {
        // No unsaved data, just close the form
        Keyboard.dismiss();
        setIsAddingFaq(false);
      }
    }
  };

  // Generate a temporary UUID for new FAQs
  const generateTempUuid = () => `temp-${Math.random().toString(36).substring(2)}-${Date.now()}`;

  // Add a new FAQ
  const handleAddFaq = async () => {
    if (!newQuestion.trim() || !newAnswer.trim()) {
      Alert.alert(
        t('common.errors.title'),
        t('common.faq_section.fields_required')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if we're in creation mode (temporary housing UUID)
      if (housingUuid === 'temp') {
        // In creation mode, just add to local state with a temporary UUID
        const newFaq: FAQ = {
          uuid: generateTempUuid(),
          question: newQuestion,
          answer: newAnswer,
          isNew: true // Mark as new for later API submission
        };

        // Add to state with animation
        const updatedFaqs = [...faqs, newFaq];
        setFaqs(updatedFaqs);
        updateParentFaqs(updatedFaqs);

        // Reset form and close it
        clearFormData();
        setIsAddingFaq(false);
      } else {
        // In update mode, call the API
        const response = await httpClient.post(`/housings/${housingUuid}/faq/add`, {
          question: newQuestion,
          answer: newAnswer
        });

        if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
          // Add the new FAQ to the list with the UUID from the response
          const newFaq: FAQ = {
            uuid: response.data && typeof response.data === 'object' ? (response.data as any).uuid : generateTempUuid(),
            question: newQuestion,
            answer: newAnswer
          };

          // Add to state with animation
          const updatedFaqs = [...faqs, newFaq];
          setFaqs(updatedFaqs);
          updateParentFaqs(updatedFaqs);

          // Reset form and close it
          clearFormData();
          setIsAddingFaq(false);

          // Show success message
          Alert.alert(
            t('common.success.title'),
            t('common.faq_section.faq_added')
          );
        } else {
          throw new Error('Failed to add FAQ');
        }
      }
    } catch (error) {
      console.error('Error adding FAQ:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.faq_section.add_error')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update an existing FAQ
  const handleUpdateFaq = async (faq: FAQ) => {
    if (!faq.question.trim() || !faq.answer.trim()) {
      Alert.alert(
        t('common.errors.title'),
        t('common.faq_section.fields_required')
      );
      return;
    }

    // Find the FAQ in the list
    const faqIndex = faqs.findIndex(f => f.uuid === faq.uuid);
    if (faqIndex === -1) return;

    // Create a copy of the FAQs array
    const updatedFaqs = [...faqs];

    // Set loading state for this FAQ
    updatedFaqs[faqIndex] = { ...updatedFaqs[faqIndex], isEditing: true };
    setFaqs(updatedFaqs);
    // Don't call updateParentFaqs here as this is just setting the loading state

    try {
      // Check if we're in creation mode (temporary housing UUID) or if this is a new FAQ
      if (housingUuid === 'temp' || faq.isNew) {
        // In creation mode, just update the local state
        updatedFaqs[faqIndex] = {
          ...faq,
          isEditing: false,
          isNew: true // Ensure it's marked as new
        };
        setFaqs(updatedFaqs);
        updateParentFaqs(updatedFaqs);
      } else {
        // In update mode, call the API
        const response = await httpClient.put(`/housings/faq/update/${faq.uuid}`, {
          question: faq.question,
          answer: faq.answer
        });

        if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
          // Update the FAQ in the list
          updatedFaqs[faqIndex] = {
            ...faq,
            isEditing: false
          };
          setFaqs(updatedFaqs);
          updateParentFaqs(updatedFaqs);

          // Show success message
          Alert.alert(
            t('common.success.title'),
            t('common.faq_section.faq_updated')
          );
        } else {
          throw new Error('Failed to update FAQ');
        }
      }
    } catch (error) {
      console.error('Error updating FAQ:', error);

      // Reset editing state
      updatedFaqs[faqIndex] = {
        ...faq,
        isEditing: false
      };
      setFaqs(updatedFaqs);
      updateParentFaqs(updatedFaqs);

      Alert.alert(
        t('common.errors.title'),
        t('common.faq_section.update_error')
      );
    }
  };

  // Delete an FAQ
  const handleDeleteFaq = (uuid: string) => {
    Alert.alert(
      t('common.alerts.confirm_title'),
      t('common.faq_section.confirm_delete'),
      [
        {
          text: t('common.buttons.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.buttons.delete'),
          style: 'destructive',
          onPress: async () => {
            // Find the FAQ to check if it's a new one
            const faqToDelete = faqs.find(faq => faq.uuid === uuid);

            // Check if we're in creation mode or if this is a new FAQ
            if (housingUuid === 'temp' || (faqToDelete && faqToDelete.isNew)) {
              // In creation mode, just remove from local state
              const updatedFaqs = faqs.filter(faq => faq.uuid !== uuid);
              setFaqs(updatedFaqs);
              updateParentFaqs(updatedFaqs);
            } else {
              try {
                // Call API to delete FAQ
                const response = await httpClient.delete(`/housings/faq/delete/${uuid}`);

                if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
                  // Remove the FAQ from the list with animation
                  const updatedFaqs = faqs.filter(faq => faq.uuid !== uuid);
                  setFaqs(updatedFaqs);
                  updateParentFaqs(updatedFaqs);

                  // Show success message
                  Alert.alert(
                    t('common.success.title'),
                    t('common.faq_section.faq_deleted')
                  );
                } else {
                  throw new Error('Failed to delete FAQ');
                }
              } catch (error) {
                console.error('Error deleting FAQ:', error);
                Alert.alert(
                  t('common.errors.title'),
                  t('common.faq_section.delete_error')
                );
              }
            }
          }
        }
      ]
    );
  };

  // Toggle edit mode for an FAQ
  const toggleEditMode = (uuid: string) => {
    const updatedFaqs = faqs.map(faq =>
      faq.uuid === uuid
        ? { ...faq, isEditing: !faq.isEditing }
        : faq
    );
    setFaqs(updatedFaqs);
    // Don't call updateParentFaqs here as this is just toggling UI state
  };

  // Update FAQ field (question or answer)
  const updateFaqField = (uuid: string, field: 'question' | 'answer', value: string) => {
    const updatedFaqs = faqs.map(faq =>
      faq.uuid === uuid
        ? { ...faq, [field]: value }
        : faq
    );
    setFaqs(updatedFaqs);
    // Don't call updateParentFaqs here as this is just updating the field during editing
  };

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 20}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={true}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="interactive"
          nestedScrollEnabled={true}
          scrollEnabled={true}
        >
        {/* FAQ List */}
        {faqs.length > 0 ? (
          faqs.map((faq, index) => (
            <Animated.View
              key={faq.uuid}
              entering={FadeIn.duration(300)}
              exiting={FadeOut.duration(300)}
              style={styles.faqItem}
            >
              {faq.isEditing ? (
                // Edit mode
                <View style={styles.editContainer}>
                  <TextInput
                    style={[styles.input, styles.questionInput]}
                    value={faq.question}
                    onChangeText={(text) => updateFaqField(faq.uuid, 'question', text)}
                    placeholder={t('common.faq_section.question_placeholder')}
                    multiline
                    maxLength={500}
                    textAlignVertical="top"
                    scrollEnabled={true}
                  />
                  <TextInput
                    style={[styles.input, styles.answerInput]}
                    value={faq.answer}
                    onChangeText={(text) => updateFaqField(faq.uuid, 'answer', text)}
                    placeholder={t('common.faq_section.answer_placeholder')}
                    multiline
                    maxLength={1000}
                    textAlignVertical="top"
                    scrollEnabled={true}
                  />
                  <View style={styles.editActions}>
                    <TouchableOpacity
                      style={[styles.editButton, styles.cancelButton]}
                      onPress={() => toggleEditMode(faq.uuid)}
                    >
                      <MaterialIcons name="close" size={18} color="#fff" />
                      <Text style={styles.buttonText}>{t('common.buttons.cancel')}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.editButton}
                      onPress={() => handleUpdateFaq(faq)}
                      disabled={faq.isEditing && !faq.question.trim() && !faq.answer.trim()}
                    >
                      <MaterialIcons name="check" size={18} color="#fff" />
                      <Text style={styles.buttonText}>{t('common.buttons.save')}</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                // View mode
                <View>
                  <View style={styles.faqHeader}>
                    <Text style={styles.questionText}>{faq.question}</Text>
                    <View style={styles.actionButtons}>
                      <TouchableOpacity
                        style={[styles.iconButton, styles.editIconButton]}
                        onPress={() => toggleEditMode(faq.uuid)}
                      >
                        <MaterialIcons name="edit" size={20} color="#4682B4" />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.iconButton, styles.deleteIconButton]}
                        onPress={() => handleDeleteFaq(faq.uuid)}
                      >
                        <MaterialIcons name="delete" size={20} color="#F44336" />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <Text style={styles.answerText}>{faq.answer}</Text>
                </View>
              )}
              {index < faqs.length - 1 && <View style={styles.divider} />}
            </Animated.View>
          ))
        ) : (
          <Text style={styles.emptyText}>{t('common.faq_section.no_faqs')}</Text>
        )}

        {/* Add FAQ Form */}
        {isAddingFaq && (
          <Animated.View
            style={styles.addFormContainer}
            entering={FadeIn.duration(300)}
            exiting={FadeOut.duration(300)}
          >
            <TextInput
              style={[styles.input, styles.questionInput]}
              value={newQuestion}
              onChangeText={updateNewQuestion}
              placeholder={t('common.faq_section.question_placeholder')}
              multiline
              maxLength={500}
              textAlignVertical="top"
              scrollEnabled={true}
              onFocus={() => {
                // Scroll to form when focused
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true });
                }, 200);
              }}
              onContentSizeChange={() => {
                // Auto-scroll when content grows
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true });
                }, 100);
              }}
            />
            <TextInput
              style={[styles.input, styles.answerInput]}
              value={newAnswer}
              onChangeText={updateNewAnswer}
              placeholder={t('common.faq_section.answer_placeholder')}
              multiline
              maxLength={1000}
              textAlignVertical="top"
              scrollEnabled={true}
              onFocus={() => {
                // Scroll to form when focused
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true });
                }, 200);
              }}
              onContentSizeChange={() => {
                // Auto-scroll when content grows
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({ animated: true });
                }, 100);
              }}
            />
            <TouchableOpacity
              style={[styles.addButton, isSubmitting && styles.disabledButton]}
              onPress={handleAddFaq}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialIcons name="add" size={18} color="#fff" />
                  <Text style={styles.buttonText}>{t('common.faq_section.add_faq')}</Text>
                </>
              )}
            </TouchableOpacity>
          </Animated.View>
        )}
      </ScrollView>
      </KeyboardAvoidingView>

      {/* Add FAQ Button */}
      <AnimatedTouchable
        style={[styles.floatingButton, addButtonAnimatedStyle]}
        onPress={toggleAddForm}
      >
        <MaterialIcons
          name={isAddingFaq ? "close" : "add"}
          size={24}
          color="#fff"
        />
      </AnimatedTouchable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    position: 'relative',
    minHeight: 200, // Minimum height to ensure usability
  },
  keyboardAvoidingView: {
    flexGrow: 1,
  },
  scrollView: {
    flexGrow: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 80, // Space for floating button
    flexGrow: 1,
  },
  faqItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  answerText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 6,
    borderRadius: 20,
    marginLeft: 8,
  },
  editIconButton: {
    backgroundColor: '#E3F2FD',
  },
  deleteIconButton: {
    backgroundColor: '#FFEBEE',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginTop: 12,
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    fontSize: 16,
    marginVertical: 20,
  },
  floatingButton: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 34 : 16, // Account for safe area on iOS
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4682B4',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
    zIndex: 1000, // Ensure it stays on top
  },
  addFormContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 12,
    minHeight: 48,
  },
  questionInput: {
    minHeight: 48,
  },
  answerInput: {
    minHeight: 80,
  },
  addButton: {
    backgroundColor: '#4682B4',
    borderRadius: 8,
    padding: 14,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 48, // Ensure minimum touch target
    marginTop: 8,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 8,
  },
  disabledButton: {
    backgroundColor: '#B0BEC5',
  },
  editContainer: {
    marginBottom: 8,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    flexWrap: 'wrap', // Allow wrapping on small screens
  },
  editButton: {
    backgroundColor: '#4682B4',
    borderRadius: 8,
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    marginTop: 4, // Add top margin for wrapped buttons
    minWidth: 80,
    minHeight: 40, // Ensure minimum touch target
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
});

export default FAQSection;
