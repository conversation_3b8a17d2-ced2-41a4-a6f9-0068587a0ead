import React from 'react';
import { Text, View, TextInput, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';

interface FormData {
  title: string;
  description: string;
  capacity: string;
}

interface GeneralInformationSectionProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
}

const GeneralInformationSection: React.FC<GeneralInformationSectionProps> = ({ formData, setFormData }) => {
  const { t } = useTranslation();

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionNumberContainer}>
          <Text style={styles.sectionNumber}>1</Text>
        </View>
        <Text style={styles.sectionTitle}>{t('common.general_info.title')}</Text>
      </View>
      <View style={styles.formGroup}>
        <TextInput
          style={styles.input}
          placeholder={t('common.general_info.title_placeholder')}
          value={formData.title}
          onChangeText={(text) => setFormData({ ...formData, title: text })}
        />
      </View>
      <View style={styles.formGroup}>
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder={t('common.general_info.description_placeholder')}
          multiline
          numberOfLines={4}
          value={formData.description}
          onChangeText={(text) => setFormData({ ...formData, description: text })}
        />
      </View>
      <View style={styles.formGroup}>
        <TextInput
          style={styles.input}
          placeholder={t('common.general_info.capacity_placeholder')}
          keyboardType="numeric"
          value={formData.capacity}
          onChangeText={(text) => setFormData({ ...formData, capacity: text })}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    backgroundColor: 'white',
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  sectionNumber: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 16,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
});

export default GeneralInformationSection;