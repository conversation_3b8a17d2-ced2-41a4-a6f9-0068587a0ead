import { useVideoPlayer, VideoView } from 'expo-video';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import Animated, { SlideInDown, SlideOutDown } from 'react-native-reanimated';

export default function InlinePlayer ({ media, onClose }) {
  console.log("Media: " , media);
  // Create a player instance using the URL as the source.
  const player = useVideoPlayer(media.url, (player) => {
    // Optionally adjust the player – for example, auto-play.
    player.play();
  });

  return (
    <Animated.View
      entering={SlideInDown}
      exiting={SlideOutDown}
      style={styles.inlinePlayerWrapper}
    >
      <View style={styles.inlinePlayerContainer}>
        <View style={styles.inlinePlayerHeader}>
          <Text style={styles.inlinePlayerTitle}>{media.title}</Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.inlinePlayerClose}>×</Text>
          </TouchableOpacity>
        </View>
        <VideoView
          player={player}
          nativeControls
          style={
            media.type === 'video'
              ? styles.inlineVideoPlayer
              : styles.inlineAudioPlayer
          }
        />
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
    // Inline Player Styles
    inlinePlayerWrapper: { marginHorizontal: 16, marginVertical: 8 },
    inlinePlayerContainer: {
      backgroundColor: '#fff',
      borderRadius: 16,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
    inlinePlayerHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 },
    inlinePlayerTitle: { fontSize: 16, fontWeight: '600', color: '#333' },
    inlinePlayerClose: { fontSize: 24, color: '#333' },
    inlineVideoPlayer: { width: '100%', height: 200, borderRadius: 16 },
    inlineAudioPlayer: { width: '100%', height: 80, borderRadius: 16 }
});