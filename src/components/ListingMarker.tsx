import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Marker } from 'react-native-maps';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
} from 'react-native-reanimated';

interface Listing {
  title: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface ListingMarkerProps {
  listing: Listing;
  onPress: (listing: Listing) => void;
}

const ListingMarker: React.FC<ListingMarkerProps> = ({ listing, onPress }) => {
  // Animation setup
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  // Initial animation when component mounts
  useEffect(() => {
    scale.value = withSequence(
      withTiming(1.1, { duration: 300 }),
      withTiming(1, { duration: 200 })
    );
  }, []);

  if (!listing || !listing.latitude || !listing.longitude) {
    return null;
  }

  const handlePress = () => {
    // Animate on press
    scale.value = withSequence(
      withTiming(1.2, { duration: 150 }),
      withTiming(1, { duration: 150 })
    );

    // Call the onPress handler
    onPress(listing);
  };

  // Wrap in try-catch to handle potential errors
  try {
    return (
      <Marker
        coordinate={{
          latitude: listing.latitude,
          longitude: listing.longitude
        }}
        onPress={handlePress}
        tracksViewChanges={false} // Improve performance
      >
        <Animated.View style={[styles.markerContainer, animatedStyle]}>
          <MaterialIcons name="home" size={20} color="white" />
        </Animated.View>
      </Marker>
    );
  } catch (error) {
    console.error('Error rendering ListingMarker:', error);
    return null; // Return null if there's an error
  }
};

const styles = StyleSheet.create({
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#D1987C',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default ListingMarker;
