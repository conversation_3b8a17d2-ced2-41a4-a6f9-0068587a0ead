import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { useNotification } from '@/context/NotificationContext';
import { MaterialIcons, Feather } from '@expo/vector-icons';
import * as Notifications from 'expo-notifications';

const NotificationTestPanel: React.FC = () => {
  const {
    expoPushToken,
    isRegistered,
    error,
    sendTestNotification,
    cancelAllNotifications
  } = useNotification();

  const [title, setTitle] = useState('Test Notification');
  const [body, setBody] = useState('This is a test notification from Wodaabe Stays');
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <MaterialIcons name="notifications" size={20} color="#3498DB" />
            <Text style={styles.headerTitle}>Test Notifications</Text>
          </View>
          <Feather
            name={isExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#3498DB"
          />
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <ScrollView style={styles.content}>
          <View style={styles.statusContainer}>
            <Text style={styles.label}>Registration Status:</Text>
            <Text style={[
              styles.statusText,
              { color: isRegistered ? '#4CAF50' : '#F44336' }
            ]}>
              {isRegistered ? 'Registered' : 'Not Registered'}
            </Text>
          </View>

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Error: {error}</Text>
            </View>
          )}

          <View style={styles.tokenContainer}>
            <Text style={styles.label}>Push Token:</Text>
            <Text style={styles.tokenText} numberOfLines={2} ellipsizeMode="middle">
              {expoPushToken || 'No token available'}
            </Text>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Notification Title:</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter notification title"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Notification Body:</Text>
            <TextInput
              style={[styles.input, styles.bodyInput]}
              value={body}
              onChangeText={setBody}
              placeholder="Enter notification message"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.sendButton]}
              onPress={() => sendTestNotification(title, body)}
              disabled={!isRegistered}
            >
              <MaterialIcons name="notifications-active" size={20} color="#fff" />
              <Text style={styles.buttonText}>Send Test Notification</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#4CAF50' }]}
              onPress={async () => {
                try {
                  // For Android, ensure we have a notification channel
                  if (Platform.OS === 'android') {
                    await Notifications.setNotificationChannelAsync('direct-test', {
                      name: 'Direct Test Channel',
                      importance: Notifications.AndroidImportance.MAX,
                      vibrationPattern: [0, 250, 250, 250],
                      lightColor: '#FF231F7C',
                    });
                  }

                  const id = await Notifications.scheduleNotificationAsync({
                    content: {
                      title: 'Direct Test',
                      body: 'This is a direct test notification',
                      sound: true,
                    },
                    trigger: Platform.OS === 'android' ? {
                      type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
                      seconds: 1,
                      channelId: 'direct-test',
                    } : null,
                  });
                  console.log(`Direct test notification scheduled with ID: ${id}`);
                  Alert.alert('Success', 'Direct test notification sent!');
                } catch (error) {
                  console.error('Error sending direct test notification:', error);
                  Alert.alert('Error', `Failed to send direct test: ${error}`);
                }
              }}
            >
              <MaterialIcons name="notification-important" size={20} color="#fff" />
              <Text style={styles.buttonText}>Direct Test</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={cancelAllNotifications}
            >
              <MaterialIcons name="notifications-off" size={20} color="#fff" />
              <Text style={styles.buttonText}>Cancel All Notifications</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginVertical: 8,
  },
  header: {
    paddingVertical: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  content: {
    maxHeight: 400,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
    color: '#555',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
  },
  tokenContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  tokenText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
    marginTop: 4,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 10,
    fontSize: 14,
    backgroundColor: '#fafafa',
  },
  bodyInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  sendButton: {
    backgroundColor: '#3498DB',
  },
  cancelButton: {
    backgroundColor: '#D1987C',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NotificationTestPanel;
