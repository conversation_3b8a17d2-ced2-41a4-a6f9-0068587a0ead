import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

interface PhotoSectionProps {
  formData: {
    mainPhoto?: string;
  };
  pickMainPhoto: () => void;
}

const PhotoSection: React.FC<PhotoSectionProps> = ({ formData, pickMainPhoto }) => {
  const { t } = useTranslation();

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionNumberContainer}>
          <Text style={styles.sectionNumber}>2</Text>
        </View>
        <Text style={styles.sectionTitle}>{t('common.photo.title')}</Text>
      </View>
      <TouchableOpacity style={styles.photoContainer} onPress={pickMainPhoto}>
        {formData.mainPhoto ? (
          <Image source={{ uri: formData.mainPhoto }} style={styles.mainPhoto} />
        ) : (
          <View style={styles.addPhotoPlaceholder}>
            <Text style={styles.placeholderText}>{t('common.photo.add_photo_placeholder')}</Text>
            <View style={styles.addButton}>
              <MaterialIcons name="add" size={24} color="white" />
            </View>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    backgroundColor: 'white',
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 16 },
  sectionNumberContainer: {
    width: 24, height: 24, borderRadius: 12, backgroundColor: '#4A90E2',
    justifyContent: 'center', alignItems: 'center', marginRight: 8,
  },
  sectionNumber: { color: 'white', fontWeight: 'bold', fontSize: 14 },
  sectionTitle: { fontSize: 16, fontWeight: '600' },
  photoContainer: {
    height: 200, backgroundColor: '#f1f1f1', borderRadius: 8,
    overflow: 'hidden', justifyContent: 'center', alignItems: 'center', marginBottom: 16,
  },
  mainPhoto: { width: '100%', height: '100%' },
  addPhotoPlaceholder: { justifyContent: 'center', alignItems: 'center' },
  placeholderText: { color: '#888', marginBottom: 8 },
  addButton: {
    width: 36, height: 36, borderRadius: 18, backgroundColor: '#4A90E2',
    justifyContent: 'center', alignItems: 'center',
  },
});

export default PhotoSection;