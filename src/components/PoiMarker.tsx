import React, { useState, useEffect } from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { Marker } from 'react-native-maps';
import { MaterialIcons } from '@expo/vector-icons';
import { GOOGLE_MAPS_API_KEY } from '@/config/env';

interface POI {
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  placeId?: string;
}

interface PlaceIcon {
  uri: string;
  backgroundColor: string;
}

interface PoiMarkerProps {
  poi: POI;
  onPress: (poi: POI) => void;
  markerId: string;
}

const PoiMarker: React.FC<PoiMarkerProps> = ({ poi, onPress, markerId }) => {
  console.log('Rendering PoiMarker for:', JSON.stringify(poi));

  // Enhanced validation and debugging
  if (!poi) {
    console.error('PoiMarker: poi is null or undefined');
    return null;
  }

  if (!poi.latitude) {
    console.error('PoiMarker: poi.latitude is missing for', poi.name);
    return null;
  }

  if (!poi.longitude) {
    console.error('PoiMarker: poi.longitude is missing for', poi.name);
    return null;
  }

  // Ensure latitude and longitude are numbers
  const lat = typeof poi.latitude === 'string' ? parseFloat(poi.latitude) : poi.latitude;
  const lng = typeof poi.longitude === 'string' ? parseFloat(poi.longitude) : poi.longitude;

  console.log(`PoiMarker: Validated coordinates for ${poi.name}: ${lat}, ${lng}`);

  // Check if coordinates are valid numbers
  if (isNaN(lat) || isNaN(lng)) {
    console.error('PoiMarker: Invalid coordinates for', poi.name, lat, lng);
    return null;
  }

  const [placeIcon, setPlaceIcon] = useState<PlaceIcon | null>(null);

  useEffect(() => {
    const fetchPlaceIcon = async () => {
      try {
        // Construct text query from name and address
        const textQuery = `${poi.name}, ${poi.address}`;

        // Perform text search to get place details including icon
        const textSearchResponse = await fetch('https://places.googleapis.com/v1/places:searchText', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
            'X-Goog-FieldMask': 'places.iconMaskBaseUri,places.iconBackgroundColor'
          },
          body: JSON.stringify({
            textQuery,
            maxResultCount: 1
          })
        });

        const data = await textSearchResponse.json();
        console.log("Place icon: ", data);

        // Check if we got a valid place result
        if (data.places && data.places.length > 0) {
          const place = data.places[0];

          // Construct full icon URL (SVG in this case)
          const iconUrl = `${place.iconMaskBaseUri}.png`;

          setPlaceIcon({
            uri: iconUrl,
            backgroundColor: place.iconBackgroundColor
          });
        }
      } catch (error) {
        console.error('Error fetching place icon:', error);
        // Fallback to default icon if fetch fails
        setPlaceIcon(null);
      }
    };

    fetchPlaceIcon();
  }, [poi]);

  // Render marker with either custom Places API icon or fallback icon
  // Wrap the onPress handler in a try-catch to prevent errors
  const handlePress = () => {
    try {
      onPress(poi);
    } catch (error) {
      console.error('Error in marker press handler:', error);
    }
  };

  // Use the validated coordinates
  const validCoordinate = {
    latitude: lat,
    longitude: lng
  };

  console.log('PoiMarker: Using coordinate', validCoordinate);

  return (
    <Marker
      coordinate={validCoordinate}
      pinColor="#D1987C"
      onPress={handlePress}
      identifier={markerId}
      tracksViewChanges={true} // Set to true for debugging
    >
      <View style={styles.listingMarkerContainer}>
        {placeIcon ? (
          <View
            style={[
              styles.placeIconContainer,
              { backgroundColor: placeIcon.backgroundColor }
            ]}
          >
            <Image
              source={{ uri: placeIcon.uri }}
              style={styles.placeIcon}
              resizeMode="contain"
            />
          </View>
        ) : (
          <View style={styles.debugMarker}>
            <MaterialIcons name="place" size={24} color="white" />
          </View>
        )}
      </View>
    </Marker>
  );
};

// Example styles (adjust as needed)
const styles = StyleSheet.create({
    listingMarkerContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    placeIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white'
    },
    placeIcon: {
      width: 20,
      height: 20,
    },
    debugMarker: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#FF5722', // Bright orange for visibility
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'white',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.8,
      shadowRadius: 2,
      elevation: 5,
    }
  });

export default PoiMarker;