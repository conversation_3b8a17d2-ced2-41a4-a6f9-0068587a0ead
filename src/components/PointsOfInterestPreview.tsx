import React, { useState, useEffect } from 'react';
import { View, Text, Image, ScrollView, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { GOOGLE_MAPS_API_KEY } from '@/config/env';

interface POI {
  name: string;
  address: string;
  placeId?: string;
  types?: string[];
  photo?: string;
}

interface Listing {
  pointsOfInterest: POI[];
}

interface PointsOfInterestPreviewProps {
  listing: Listing;
  onPOIPress: (poi: POI) => void;
}

const PointsOfInterestPreview: React.FC<PointsOfInterestPreviewProps> = ({ listing, onPOIPress }) => {
  const { t } = useTranslation();
  const [poiWithPhotos, setPoiWithPhotos] = useState<POI[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPhotos = async () => {
      if (!listing.pointsOfInterest || listing.pointsOfInterest.length === 0) {
        setLoading(false);
        return;
      }

      try {
        const updatedPoi = await Promise.all(
          listing.pointsOfInterest.map(async (poi) => {
            console.log('POI:', poi);
            try {
              // Only fetch if we have a placeId
              if (poi.placeId) {
                const response = await fetch(`https://places.googleapis.com/v1/places/${poi.placeId}`, {
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
                    'X-Goog-FieldMask': 'photos,types'
                  }
                });

                // Check if the response is not OK
                if (!response.ok) {
                  const errorText = await response.text();
                  console.log('Place details error response:', errorText);

                  // Construct text query from name and address
                  const textQuery = `${poi.name}, ${poi.address}`;

                  // Perform text search to get updated place ID
                  const textSearchResponse = await fetch('https://places.googleapis.com/v1/places:searchText', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
                      'X-Goog-FieldMask': 'places.id'
                    },
                    body: JSON.stringify({ textQuery })
                  });

                  if (!textSearchResponse.ok) {
                    const textSearchErrorText = await textSearchResponse.text();
                    console.error('Text search error response:', textSearchErrorText);
                    return poi;
                  }

                  const textSearchResult = await textSearchResponse.json();

                  // If we found a new place ID, retry the original request
                  if (textSearchResult.places && textSearchResult.places.length > 0) {
                    const newPlaceId = textSearchResult.places[0].id;

                    const retryResponse = await fetch(`https://places.googleapis.com/v1/places/${newPlaceId}`, {
                      method: 'GET',
                      headers: {
                        'Content-Type': 'application/json',
                        'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
                        'X-Goog-FieldMask': 'photos,types'
                      }
                    });

                    if (!retryResponse.ok) {
                      const retryErrorText = await retryResponse.text();
                      console.error('Retry place details error response:', retryErrorText);
                      return poi;
                    }

                    const retryPlaceDetails = await retryResponse.json();

                    // Check if we have photos and get the first one
                    const photo = retryPlaceDetails.photos && retryPlaceDetails.photos.length > 0
                      ? retryPlaceDetails.photos[0].name
                      : null;

                    return {
                      ...poi,
                      placeId: newPlaceId,
                      photo,
                      types: retryPlaceDetails.types || []
                    };
                  }

                  return poi;
                }

                const placeDetails = await response.json();
                console.log('Place details:', placeDetails);

                // Check if we have photos and get the first one
                const photo = placeDetails.photos && placeDetails.photos.length > 0
                  ? placeDetails.photos[0].name
                  : null;

                // Add the photo and types to the POI object
                return {
                  ...poi,
                  photo,
                  types: placeDetails.types || []
                };
              }
              return poi;
            } catch (error) {
              console.error(`Error processing POI ${poi.name}:`, error);
              return poi;
            }
          })
        );

        setPoiWithPhotos(updatedPoi);
      } catch (error) {
        console.error('Error fetching POI photos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPhotos();
  }, [listing.pointsOfInterest]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4A90E2" />
        <Text style={styles.loadingText}>{t('common.poi_preview.loading')}</Text>
      </View>
    );
  }

  if (!poiWithPhotos.length) {
    return null;
  }

  return (
    <View style={styles.poiListContainer}>
      <Text style={styles.poiListTitle}>{t('common.poi_preview.title')}</Text>
      <ScrollView
        style={styles.poiScrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {poiWithPhotos.map((poi, index) => (
          <TouchableOpacity
            key={index}
            style={styles.poiItem}
            onPress={() => onPOIPress(poi)}
          >
            {poi.photo && (
              <View style={styles.poiImageContainer}>
                <Image
                  source={{
                    uri: `https://places.googleapis.com/v1/${poi.photo}/media?key=${GOOGLE_MAPS_API_KEY}&maxHeightPx=200&maxWidthPx=200`
                  }}
                  style={styles.poiImage}
                  resizeMode="cover"
                />
              </View>
            )}
            <View style={styles.poiItemContent}>
              <Text style={styles.poiName}>{poi.name}</Text>
              <Text style={styles.poiAddress}>{poi.address}</Text>
              {poi.types && poi.types.length > 0 && (
                <View style={styles.poiTypesContainer}>
                  {poi.types.slice(0, 2).map((type, typeIndex) => (
                    <Text key={typeIndex} style={styles.poiType}>
                      {type.replace(/_/g, ' ')}
                    </Text>
                  ))}
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  poiListContainer: {
    marginTop: 15,
    borderRadius: 8,
  },
  poiScrollContainer: {
    //maxHeight: 300,
  },
  poiListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  poiItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginBottom: 8,
    padding: 2,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  poiImageContainer: {
    width: 120,
    height: 120,
  },
  poiImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10
  },
  poiItemContent: {
    flex: 1,
    padding: 12,
  },
  poiName: {
    fontSize: 15,
    fontWeight: '600',
  },
  poiAddress: {
    fontSize: 13,
    color: '#666',
    marginTop: 3,
  },
  poiTypesContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  poiType: {
    fontSize: 12,
    color: '#4A90E2',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 4,
    borderRadius: 4,
  },
  placePhoto: {
    width: '100%',
    height: 200,
  },
});

export default PointsOfInterestPreview;