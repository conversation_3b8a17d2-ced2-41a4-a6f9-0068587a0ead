import React, { useState, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  Image,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useTranslation } from 'react-i18next';
import { GOOGLE_MAPS_API_KEY } from '@/config/env';

interface Location {
  latitude: number;
  longitude: number;
}

interface PlacePredictionData {
  place: string;
  placeId: string;
  text: {
    text: string;
    matches: Array<{ startOffset?: number; endOffset: number }>;
  };
  structuredFormat: {
    mainText: {
      text: string;
      matches: Array<{ startOffset?: number; endOffset: number }>;
    };
    secondaryText: {
      text: string;
    };
  };
  types: string[];
  photos?: Array<{
    name: string;
    widthPx: number;
    heightPx: number;
    authorAttributions: Array<{
      displayName: string;
      uri: string;
      photoUri: string;
    }>;
  }>;
  location?: Location;
}

interface PlaceDetailsData {
  id: string;
  types: string[];
  formattedAddress: string;
  location: Location;
  displayName: {
    text: string;
    languageCode: string;
  };
  photos?: Array<{
    name: string;
    widthPx: number;
    heightPx: number;
    authorAttributions: Array<{
      displayName: string;
      uri: string;
      photoUri: string;
    }>;
    flagContentUri: string;
    googleMapsUri: string;
  }>;
}

interface PlacePrediction {
  placePrediction: PlacePredictionData;
  placeDetails?: PlaceDetailsData;
}

interface PlaceDetails {
  name: string;
  address: string;
  location: Location;
  // Add latitude and longitude at the root level for API compatibility
  latitude?: number;
  longitude?: number;
  placeId?: string;
  types?: string[];
  photo?: string;
}

interface FormData {
  pointsOfInterest: PlaceDetails[];
}

interface PointsOfInterestSectionProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  handleRemovePoi: (index: number) => void;
  poiForm: {
    name: string;
    address: string;
    latitude: string;
    longitude: string;
  };
  setPoiForm: (form: {
    name: string;
    address: string;
    latitude: string;
    longitude: string;
  }) => void;
  isPoiGeocoding?: boolean;
  handleAddPoi: () => void;
}

const PointsOfInterestSection: React.FC<PointsOfInterestSectionProps> = ({
  formData,
  setFormData,
  handleRemovePoi,
  poiForm,
  setPoiForm,
  isPoiGeocoding,
  handleAddPoi,
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [suggestions, setSuggestions] = useState<PlacePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState<PlacePrediction | null>(null);
  const [userLocation, setUserLocation] = useState<Location | null>(null);
  // Log formData and poiForm for debugging
  // console.log("formData:", formData);
  // console.log("poiForm:", poiForm);
  // console.log("isPoiGeocoding:", isPoiGeocoding);

  // Get user's location on component mount
  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        console.log(t('common.poi.location_permission_denied'));
        return;
      }

      try {
        let location = await Location.getCurrentPositionAsync({});
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        });
      } catch (error) {
        console.error(t('common.poi.location_error'), error);
      }
    })();
  }, [t]);

  // Fetch autocomplete suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (searchText.length < 3 || !userLocation) return;

      setIsLoading(true);
      try {
        const response = await fetch('https://places.googleapis.com/v1/places:autocomplete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY
          },
          body: JSON.stringify({
            input: searchText,
            locationBias: {
              circle: {
                center: {
                  latitude: userLocation.latitude,
                  longitude: userLocation.longitude
                },
                radius: 5000.0
              }
            }
          })
        });

        const data = await response.json();
        console.log('Suggestions:', data);
        setSuggestions(data.suggestions || []);
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce the API call
    const timeoutId = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(timeoutId);
  }, [searchText, userLocation]);

  // Fetch place details when a suggestion is selected
  const handleSelectPlace = async (placePrediction: PlacePrediction) => {
    try {
      setIsLoading(true);
      setSearchText('');
      setSuggestions([]);

      console.log('handleSelectPlace: Processing place prediction:', placePrediction);

      // Validate input
      if (!placePrediction || !placePrediction.placePrediction) {
        console.error('handleSelectPlace: Invalid placePrediction structure');
        Alert.alert(
          t('common.errors.title'),
          t('common.poi.errors.invalid_place_data')
        );
        return;
      }

      const placeId = placePrediction.placePrediction.placeId;
      if (!placeId) {
        console.error('handleSelectPlace: No placeId found');
        Alert.alert(
          t('common.errors.title'),
          t('common.poi.errors.missing_place_id')
        );
        return;
      }

      console.log('Fetching place details for placeId:', placeId);

      const response = await fetch(`https://places.googleapis.com/v1/places/${placeId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
          'X-Goog-FieldMask': 'id,displayName,photos,formattedAddress,location,types'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const placeDetails = await response.json();
      console.log('Place details received:', placeDetails);

      // Validate place details response
      if (!placeDetails) {
        throw new Error('Empty place details response');
      }

      // Combine place prediction and place details
      setSelectedPlace({
        placePrediction: placePrediction.placePrediction,
        placeDetails: placeDetails
      });

    } catch (error) {
      console.error('Error fetching place details:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.poi.errors.api_error')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Add selected place to formData
  const handleAddSelectedPlace = () => {
    try {
      if (!selectedPlace) {
        console.warn('handleAddSelectedPlace: No selected place');
        return;
      }

      console.log('handleAddSelectedPlace: Processing selected place:', selectedPlace);

      // Safely extract place data with fallbacks
      const placePrediction = selectedPlace.placePrediction;
      if (!placePrediction) {
        console.error('handleAddSelectedPlace: No placePrediction found');
        return;
      }

      // Safely get main text
      const mainText = placePrediction.structuredFormat?.mainText?.text ||
                      placePrediction.displayName?.text ||
                      'Unknown Place';

      // Safely get secondary text
      const secondaryText = placePrediction.structuredFormat?.secondaryText?.text ||
                           placePrediction.formattedAddress ||
                           'Unknown Address';

      // Get location from place details or prediction with safe fallbacks
      const location = selectedPlace.placeDetails?.location ||
                      selectedPlace.placePrediction?.location ||
                      { latitude: 0, longitude: 0 };

      // Validate coordinates
      if (!location || typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {
        console.error('handleAddSelectedPlace: Invalid location data:', location);
        Alert.alert(
          t('common.errors.title'),
          t('common.poi.errors.invalid_coordinates')
        );
        return;
      }

      // Create the POI data
      const poiData = {
        name: mainText,
        address: secondaryText,
        latitude: location.latitude.toString(),
        longitude: location.longitude.toString()
      };

      console.log('Setting POI form with coordinates:', location);

      // Update the poiForm with the selected place data
      setPoiForm(poiData);

      // Create new POI object with safe property access
      const newPoi: PlaceDetails = {
        name: mainText,
        address: secondaryText,
        location: location,
        // Add latitude and longitude at the root level for API compatibility
        latitude: location.latitude,
        longitude: location.longitude,
        placeId: placePrediction.placeId || `poi-${Date.now()}`,
        types: selectedPlace.placeDetails?.types || placePrediction.types || [],
        photo: selectedPlace.placeDetails?.photos?.[0]?.name || placePrediction.photos?.[0]?.name
      };

      console.log('Adding new POI:', newPoi);

      // Update the formData directly
      setFormData({
        ...formData,
        pointsOfInterest: [...formData.pointsOfInterest, newPoi]
      });

      setSelectedPlace(null);

      // Show success message
      Alert.alert(
        t('common.success.title'),
        t('common.success.poi_added')
      );

    } catch (error) {
      console.error('Error in handleAddSelectedPlace:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.poi.errors.add_failed')
      );
    }
  };

  // Render photo from Google Places API
  const renderPhoto = (photoName: string | undefined) => {
    if (!photoName) return null;
    console.log('Rendering photo:', photoName);
    const photoUrl = `https://places.googleapis.com/v1/${photoName}/media?key=${GOOGLE_MAPS_API_KEY}&maxHeightPx=400&maxWidthPx=400`;

    return (
      <Image
        source={{ uri: photoUrl }}
        style={styles.placePhoto}
        resizeMode="cover"
      />
    );
  };

  // Render suggestion items without using FlatList
  const renderSuggestions = () => {
    if (isLoading) {
      return (
        <View style={styles.suggestionItem}>
          <ActivityIndicator size="small" color="#0000ff" />
          <Text style={styles.suggestionMainText}>{t('common.poi.loading')}</Text>
        </View>
      );
    }

    if (suggestions.length === 0 && searchText.length > 0) {
      return (
        <View style={styles.suggestionItem}>
          <Text style={styles.suggestionMainText}>{t('common.poi.no_suggestions')}</Text>
        </View>
      );
    }

    return (
      <View>
        {suggestions.map((suggestion, index) => {
          // Safe access to suggestion properties
          const placePrediction = suggestion?.placePrediction;
          if (!placePrediction) {
            console.warn(`renderSuggestions: Invalid suggestion at index ${index}`, suggestion);
            return null;
          }

          const mainText = placePrediction.structuredFormat?.mainText?.text ||
                          placePrediction.displayName?.text ||
                          t('common.poi.fallbacks.unknown_place');

          const secondaryText = placePrediction.structuredFormat?.secondaryText?.text ||
                               placePrediction.formattedAddress ||
                               t('common.poi.fallbacks.unknown_address');

          const placeId = placePrediction.placeId || `suggestion-${index}`;

          return (
            <TouchableOpacity
              key={placeId}
              style={styles.suggestionItem}
              onPress={() => {
                try {
                  handleSelectPlace(suggestion);
                } catch (error) {
                  console.error('Error selecting place:', error);
                  Alert.alert(
                    t('common.errors.title'),
                    t('common.poi.errors.select_failed')
                  );
                }
              }}
            >
              <MaterialIcons name="location-on" size={24} color="#666" />
              <View style={styles.suggestionTextContainer}>
                <Text style={styles.suggestionMainText}>
                  {mainText}
                </Text>
                <Text style={styles.suggestionSecondaryText}>
                  {secondaryText}
                </Text>
              </View>
            </TouchableOpacity>
          );
        }).filter(Boolean)}
      </View>
    );
  };

  // Render added POIs without using FlatList
  const renderAddedPlaces = () => {
    try {
      if (!formData || !formData.pointsOfInterest || formData.pointsOfInterest.length === 0) {
        return (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>{t('common.poi.no_places_added')}</Text>
          </View>
        );
      }

      return formData.pointsOfInterest.map((poi, index) => {
        // Validate POI data
        if (!poi) {
          console.warn(`renderAddedPlaces: POI at index ${index} is null/undefined`);
          return null;
        }

        const poiName = poi.name || t('common.poi.fallbacks.unnamed_place');
        const poiAddress = poi.address || t('common.poi.fallbacks.unknown_address');
        const poiKey = poi.placeId || `poi-${index}-${Date.now()}`;

        return (
          <View key={poiKey} style={styles.poiItem}>
            {poi.photo && (
              <View style={styles.poiImageContainer}>
                <Image
                  source={{
                    uri: `https://places.googleapis.com/v1/${poi.photo}/media?key=${GOOGLE_MAPS_API_KEY}&maxHeightPx=200&maxWidthPx=200`
                  }}
                  style={styles.poiImage}
                  resizeMode="cover"
                  onError={(error) => {
                    console.warn('Error loading POI image:', error);
                  }}
                />
              </View>
            )}
            <View style={styles.poiItemContent}>
              <Text style={styles.poiName}>{poiName}</Text>
              <Text style={styles.poiAddress}>{poiAddress}</Text>
              {poi.types && Array.isArray(poi.types) && poi.types.length > 0 && (
                <View style={styles.poiTypesContainer}>
                  {poi.types.slice(0, 2).map((type, typeIndex) => {
                    if (!type || typeof type !== 'string') return null;
                    return (
                      <Text key={typeIndex} style={styles.poiType}>
                        {type.replace(/_/g, ' ')}
                      </Text>
                    );
                  })}
                </View>
              )}
              <TouchableOpacity
                onPress={() => {
                  try {
                    handleRemovePoi(index);
                  } catch (error) {
                    console.error('Error removing POI:', error);
                    Alert.alert(
                      t('common.errors.title'),
                      t('common.poi.errors.remove_failed')
                    );
                  }
                }}
                style={styles.removeButton}
              >
                <MaterialIcons name="close" size={18} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        );
      }).filter(Boolean); // Remove null entries

    } catch (error) {
      console.error('Error in renderAddedPlaces:', error);
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>Erreur lors de l'affichage des points d'intérêt</Text>
        </View>
      );
    }
  };

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionNumberContainer}>
          <Text style={styles.sectionNumber}>6</Text>
        </View>
        <View>
          <Text style={styles.sectionTitle}>{t('common.accommodation_update_screen.points_of_interest')}</Text>
          <Text style={styles.sectionSubtitle}>{t('common.accommodation_update_screen.poi_subtitle')}</Text>
        </View>
      </View>

      {/* Search input */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MaterialIcons name="search" size={24} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder={t('common.poi.search_placeholder')}
            value={searchText}
            onChangeText={setSearchText}
            onFocus={() => setSuggestions([])}
          />
        </View>
      </View>

      {/* Autocomplete suggestions */}
      {searchText.length > 0 && (
        <View style={styles.suggestionsContainer}>
          {renderSuggestions()}
        </View>
      )}

      {/* Selected place preview */}
      {selectedPlace && (
        <View style={styles.selectedPlaceContainer}>
          <View style={styles.selectedPlaceHeader}>
            <Text style={styles.selectedPlaceTitle}>
              {selectedPlace.placePrediction?.structuredFormat?.mainText?.text ||
               selectedPlace.placePrediction?.displayName?.text ||
               t('common.poi.fallbacks.selected_place')}
            </Text>
            <TouchableOpacity onPress={() => setSelectedPlace(null)}>
              <MaterialIcons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Use photos from place details if available, otherwise fallback to place prediction */}
          {selectedPlace.placeDetails?.photos && selectedPlace.placeDetails.photos.length > 0 ? (
            renderPhoto(selectedPlace.placeDetails.photos[0].name)
          ) : selectedPlace.placePrediction.photos && selectedPlace.placePrediction.photos.length > 0 && (
            renderPhoto(selectedPlace.placePrediction.photos[0].name)
          )}

          <View style={styles.selectedPlaceDetails}>
            <View style={styles.selectedPlaceAddressRow}>
              <MaterialIcons name="place" size={18} color="#666" />
              <Text style={styles.selectedPlaceAddress}>
                {selectedPlace.placePrediction?.structuredFormat?.secondaryText?.text ||
                 selectedPlace.placePrediction?.formattedAddress ||
                 t('common.poi.fallbacks.unknown_address')}
              </Text>
            </View>

            {/* Use types from place details if available, otherwise fallback to place prediction */}
            {(selectedPlace.placeDetails?.types || selectedPlace.placePrediction.types) && (
              <View style={styles.selectedPlaceTypesContainer}>
                {(selectedPlace.placeDetails?.types || selectedPlace.placePrediction.types).slice(0, 3).map((type, index) => (
                  <View key={index} style={styles.selectedPlaceTypeTag}>
                    <Text style={styles.selectedPlaceTypeText}>
                      {type.replace(/_/g, ' ')}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            <TouchableOpacity
              style={styles.addSelectedPlaceButton}
              onPress={handleAddSelectedPlace}
            >
              <Text style={styles.addSelectedPlaceButtonText}>{t('common.poi.add_place')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Added places list */}
      <View style={styles.addedPlacesContainer}>
        {renderAddedPlaces()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    backgroundColor: 'white',
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  sectionNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  sectionNumber: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  sectionSubtitle: {
    fontSize: 12,
    color: '#888'
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
  suggestionsContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 16,
    backgroundColor: 'white',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    minHeight: 60,
  },
  suggestionTextContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  suggestionMainText: {
    fontSize: 16,
    fontWeight: '500',
    flexWrap: 'wrap',
  },
  suggestionSecondaryText: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
    flexWrap: 'wrap',
  },
  selectedPlaceContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16,
  },
  selectedPlaceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9f9f9',
  },
  selectedPlaceTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  placePhoto: {
    width: '100%',
    height: 200,
  },
  selectedPlaceDetails: {
    padding: 12,
  },
  selectedPlaceAddressRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  selectedPlaceAddress: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  selectedPlaceTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  selectedPlaceTypeTag: {
    backgroundColor: '#f0f8ff',
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 6,
    marginBottom: 6,
  },
  selectedPlaceTypeText: {
    fontSize: 12,
    color: '#4A90E2',
  },
  addSelectedPlaceButton: {
    backgroundColor: '#4A90E2',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 8,
  },
  addSelectedPlaceButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  addedPlacesContainer: {
    marginTop: 15,
    borderRadius: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    color: '#888',
    fontSize: 14,
  },
  poiItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginBottom: 8,
    padding: 2,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  poiImageContainer: {
    width: 120,
    height: 120,
  },
  poiImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10
  },
  poiItemContent: {
    flex: 1,
    padding: 12,
    position: 'relative',
  },
  poiName: {
    fontSize: 15,
    fontWeight: '600',
  },
  poiAddress: {
    fontSize: 13,
    color: '#666',
    marginTop: 3,
  },
  poiTypesContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  poiType: {
    fontSize: 12,
    color: '#4A90E2',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 4,
    borderRadius: 4,
  },
  removeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 5,
  },
});

export default PointsOfInterestSection;