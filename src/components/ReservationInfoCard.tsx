import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  Easing,
  FadeIn,
  FadeInDown,
  Layout,
} from 'react-native-reanimated';

// Consistent colors for the theme
const COLORS = {
  primary: '#3C82F6', // Modern blue
  primaryLight: '#EBF3FF',
  secondary: '#DEC48F',
  success: '#22C55E',
  warning: '#F59E0B',
  error: '#EF4444',
  completed: '#059669',
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    light: '#9CA3AF',
    white: '#FFFFFF',
  },
  background: {
    main: '#FFFFFF',
    light: '#F9FAFB',
    accent: '#F3F4F6',
  },
  border: '#E5E7EB',
};

interface Guest {
  uuid: string;
  displayName: string;
  phoneNumber?: string;
  email: string;
  gender?: string;
}

interface ReservationInfoCardProps {
  reservation: {
    uuid: string;
    arrivalDate: string;
    departureDate: string;
    adultCount: number;
    childCount: number;
    client: Guest;
    invitees?: Guest[];
    reservationCode: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  };
  expanded?: boolean;
  onToggleExpand?: () => void;
}

const ReservationInfoCard: React.FC<ReservationInfoCardProps> = ({
  reservation,
  expanded = false,
  onToggleExpand,
}) => {
  const { t, i18n } = useTranslation();
  const cardScale = useSharedValue(0.96);
  const rotateValue = useSharedValue(expanded ? 180 : 0);

  // Format date based on current language
  const formatDate = (dateString: string) => {
    try {
      const locale = i18n.language === 'fr' ? fr : undefined;
      const pattern = i18n.language === 'fr' ? 'd MMM yyyy' : 'MMM d, yyyy';
      return format(parseISO(dateString), pattern, { locale });
    } catch (error) {
      return dateString.slice(0, 10); // Fallback to YYYY-MM-DD format
    }
  };

  // Calculate stay duration
  const calculateStayDuration = () => {
    try {
      const arrival = parseISO(reservation.arrivalDate);
      const departure = parseISO(reservation.departureDate);
      const diffTime = Math.abs(departure.getTime() - arrival.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays === 1
        ? t('common.reservation_info.night_one', { count: diffDays })
        : t('common.reservation_info.night_other', { count: diffDays });
    } catch (error) {
      return '';
    }
  };

  // Get status color based on reservation status
  const getStatusInfo = (status: string) => {
    const statusMap = {
      'ACTIVE': { color: COLORS.success, icon: 'check-circle' },
      'PENDING': { color: COLORS.warning, icon: 'schedule' },
      'CANCELLED': { color: COLORS.error, icon: 'cancel' },
      'COMPLETED': { color: COLORS.completed, icon: 'verified' },
      'DEFAULT': { color: COLORS.text.light, icon: 'help-outline' }
    };

    return statusMap[status.toUpperCase()] || statusMap.DEFAULT;
  };

  // Animation for card entrance
  useEffect(() => {
    cardScale.value = withSequence(
      withTiming(1.02, { duration: 300, easing: Easing.out(Easing.ease) }),
      withTiming(1, { duration: 200 })
    );
  }, []);

  // Animation for expand/collapse icon
  useEffect(() => {
    rotateValue.value = withTiming(expanded ? 180 : 0, {
      duration: 300,
      easing: Easing.inOut(Easing.ease)
    });
  }, [expanded]);

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }]
  }));

  const arrowAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotateZ: `${rotateValue.value}deg` }]
  }));

  const statusInfo = getStatusInfo(reservation.status);

  return (
    <Animated.View
      style={[styles.container, cardAnimatedStyle]}
      entering={FadeInDown.duration(400).springify()}
      layout={Layout.springify()}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.headerIconContainer}>
            <MaterialIcons name="event-available" size={22} color={COLORS.primary} />
          </View>
          <Text style={styles.headerTitle} numberOfLines={1} ellipsizeMode="tail">
            {t('common.reservation_info.title')}
          </Text>
        </View>

        <TouchableOpacity
          onPress={onToggleExpand}
          style={styles.expandButton}
          activeOpacity={0.7}
        >
          <Animated.View style={arrowAnimatedStyle}>
            <MaterialIcons name="keyboard-arrow-down" size={24} color={COLORS.text.secondary} />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Main Info */}
      <View style={styles.mainInfo}>
        {/* Status */}
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <MaterialIcons name={statusInfo.icon} size={18} color={statusInfo.color} />
          </View>
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>{t('common.reservation_info.status_label')}</Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusIndicator, { backgroundColor: statusInfo.color }]} />
              <Text style={[styles.statusValueText, { color: statusInfo.color }]}>
                {t(`common.reservation_info.status.${reservation.status.toLowerCase()}`)}
              </Text>
            </View>
          </View>
        </View>

        {/* Dates */}
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <MaterialIcons name="date-range" size={18} color={COLORS.primary} />
          </View>
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>{t('common.reservation_info.dates')}</Text>
            <Text style={styles.infoValue}>
              {formatDate(reservation.arrivalDate)} - {formatDate(reservation.departureDate)}
            </Text>
            <Text style={styles.infoDuration}>{calculateStayDuration()}</Text>
          </View>
        </View>

        {/* Guests */}
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <MaterialIcons name="people" size={18} color={COLORS.primary} />
          </View>
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>{t('common.reservation_info.guests')}</Text>
            <Text style={styles.infoValue}>
              {reservation.adultCount} {reservation.adultCount === 1
                ? t('common.reservation_info.adult_one')
                : t('common.reservation_info.adult_other')}
              {reservation.childCount > 0 && `, ${reservation.childCount} ${
                reservation.childCount === 1
                  ? t('common.reservation_info.child_one')
                  : t('common.reservation_info.child_other')
              }`}
            </Text>
          </View>
        </View>

        {/* Reservation Code */}
        <View style={styles.infoRow}>
          <View style={styles.iconContainer}>
            <MaterialIcons name="confirmation-number" size={18} color={COLORS.primary} />
          </View>
          <View style={styles.infoContent}>
            <Text style={styles.infoLabel}>{t('common.reservation_info.code')}</Text>
            <Text style={styles.infoValue}>{reservation.reservationCode}</Text>
          </View>
        </View>
      </View>

      {/* Expanded Content */}
      {expanded && (
        <Animated.View
          entering={FadeIn.duration(300)}
          style={styles.expandedContent}
        >
          <View style={styles.divider} />

          {/* Primary Guest */}
          <View style={styles.guestSection}>
            <Text style={styles.sectionTitle}>{t('common.reservation_info.primary_guest')}</Text>
            <View style={styles.guestCard}>
              <View style={styles.guestAvatarContainer}>
                <View style={styles.guestAvatar}>
                  <Text style={styles.guestInitial}>
                    {reservation.client.displayName.charAt(0).toUpperCase()}
                  </Text>
                </View>
              </View>
              <View style={styles.guestInfo}>
                <Text style={styles.guestName}>{reservation.client.displayName}</Text>
                <View style={styles.guestContact}>
                  <MaterialIcons name="email" size={16} color={COLORS.text.secondary} style={styles.contactIcon} />
                  <Text style={styles.contactText}>{reservation.client.email}</Text>
                </View>
                {reservation.client.phoneNumber && (
                  <View style={styles.guestContact}>
                    <MaterialIcons name="phone" size={16} color={COLORS.text.secondary} style={styles.contactIcon} />
                    <Text style={styles.contactText}>{reservation.client.phoneNumber}</Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Additional Guests */}
          {reservation.invitees && reservation.invitees.length > 0 && (
            // Only show this section if there are additional guests after filtering
            reservation.invitees.filter(invitee => invitee.uuid !== reservation.client.uuid).length > 0 && (
              <View style={styles.guestSection}>
                <Text style={styles.sectionTitle}>{t('common.reservation_info.additional_guests')}</Text>
                {reservation.invitees
                  // Filter out the primary guest by comparing UUIDs
                  .filter(invitee => invitee.uuid !== reservation.client.uuid)
                  .map((invitee, index) => (
                  <Animated.View
                    key={invitee.uuid}
                    entering={FadeInDown.delay(index * 100).duration(300)}
                    style={styles.guestCard}
                  >
                    <View style={styles.guestAvatarContainer}>
                      <View style={[styles.guestAvatar, { backgroundColor: COLORS.secondary }]}>
                        <Text style={styles.guestInitial}>
                          {invitee.displayName.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.guestInfo}>
                      <Text style={styles.guestName}>{invitee.displayName}</Text>
                      <View style={styles.guestContact}>
                        <MaterialIcons name="email" size={16} color={COLORS.text.secondary} style={styles.contactIcon} />
                        <Text style={styles.contactText}>{invitee.email}</Text>
                      </View>
                      {invitee.phoneNumber && (
                        <View style={styles.guestContact}>
                          <MaterialIcons name="phone" size={16} color={COLORS.text.secondary} style={styles.contactIcon} />
                          <Text style={styles.contactText}>{invitee.phoneNumber}</Text>
                        </View>
                      )}
                    </View>
                  </Animated.View>
                ))}
              </View>
            )
          )}

          {/* Reservation Dates */}
          <View style={styles.datesSection}>
            <View style={styles.dateItem}>
              <View style={styles.dateIconWrapper}>
                <MaterialIcons name="access-time" size={14} color={COLORS.text.secondary} />
                <Text style={styles.dateLabel}>{t('common.reservation_info.created_at')}</Text>
              </View>
              <Text style={styles.dateValue}>{formatDate(reservation.createdAt)}</Text>
            </View>
            <View style={styles.dividerSmall} />
            <View style={styles.dateItem}>
              <View style={styles.dateIconWrapper}>
                <MaterialIcons name="update" size={14} color={COLORS.text.secondary} />
                <Text style={styles.dateLabel}>{t('common.reservation_info.updated_at')}</Text>
              </View>
              <Text style={styles.dateValue}>{formatDate(reservation.updatedAt)}</Text>
            </View>
          </View>
        </Animated.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background.main,
    borderRadius: 16,
    //marginHorizontal: 16,
    marginVertical: 12,
    padding: 10,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 5,
      },
    }),
    borderWidth: 1,
    borderColor: COLORS.border,
    overflow: 'hidden',
  },
  header: {

    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIconContainer: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
    letterSpacing: 0.2,
    flex: 1, // Allow the title to flex and truncate if needed
    marginRight: 8,
  },
  expandButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.background.accent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 3,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusValueText: {
    fontSize: 15,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  mainInfo: {
    marginTop: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 13,
    color: COLORS.text.secondary,
    marginBottom: 3,
    letterSpacing: 0.2,
  },
  infoValue: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  infoDuration: {
    fontSize: 13,
    color: COLORS.primary,
    marginTop: 3,
    fontWeight: '500',
  },
  expandedContent: {
    marginTop: 8,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 16,
  },
  dividerSmall: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 12,
    opacity: 0.6,
  },
  guestSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 12,
    letterSpacing: 0.2,
  },
  guestCard: {
    flexDirection: 'row',
    backgroundColor: COLORS.background.accent,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    alignItems: 'center',
  },
  guestAvatarContainer: {
    padding: 2,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  guestAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guestInitial: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text.white,
  },
  guestInfo: {
    flex: 1,
    marginLeft: 14,
  },
  guestName: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  guestContact: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 3,
  },
  contactIcon: {
    marginRight: 6,
  },
  contactText: {
    fontSize: 13,
    color: COLORS.text.secondary,
    letterSpacing: 0.2,
  },
  datesSection: {
    backgroundColor: COLORS.background.accent,
    borderRadius: 12,
    padding: 14,
  },
  dateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateIconWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateLabel: {
    fontSize: 13,
    color: COLORS.text.secondary,
    marginLeft: 6,
    letterSpacing: 0.2,
  },
  dateValue: {
    fontSize: 13,
    color: COLORS.text.primary,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
});

export default ReservationInfoCard;