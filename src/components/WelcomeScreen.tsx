import { useNavigation } from '@react-navigation/native';
import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Image,
  ImageBackground,
  Dimensions,
  ScrollView
} from 'react-native';
import * as Localization from 'expo-localization';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/stores/useAuthStore';
import { AppConstants } from '@/constants/default';
import { storage } from '@/utils/storage';

interface AccessCardProps {
  title: string;
  description: string;
  image: any;
  onPress: () => void;
}

const windowHeight = Dimensions.get('window').height;

const AccessCard: React.FC<AccessCardProps> = ({ title, description, image, onPress }) => (
  <TouchableOpacity
    style={styles.card}
    onPress={onPress}
  >
    <ImageBackground
      source={image}
      style={styles.cardBackground}
      resizeMode="cover"
    >
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle}>{title}</Text>
        <Text style={styles.cardDescription}>{description}</Text>
      </View>
    </ImageBackground>
  </TouchableOpacity>
);

const WelcomeScreen: React.FC = () => {
  const { role, setRole } = useAuthStore();
  const navigation = useNavigation();
  const { t } = useTranslation();
  const [locale, setLocale] = React.useState(Localization.getLocales()[0].languageTag);

  console.log('Locale: ', locale);
  console.log('Dimensions: ', Dimensions.get('screen'));

  useEffect(() => {
    console.log('Role: ', role);
  }, [role]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {/*  */}

        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/Logo-wodaabePlan.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* Welcome Text */}
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeTitle}>{t('common.welcome.title')}</Text>
          <Text style={styles.welcomeSubtitle}>{t('common.welcome.subtitle')}</Text>
        </View>

        {/* Access Cards */}
        <View style={styles.cardsContainerWrapper}>
          <Text style={styles.cardsTitle}>{t('common.accommodation_details_screen.cohosts_title')}</Text>
          <View style={styles.cardsContainer}>
            <AccessCard
              title={t('common.welcome.guest.title')}
              description={t('common.welcome.guest.description')}
              image={require('../../assets/visitor.jpg')}
              onPress={() => {
                setRole('GUEST');
                storage.set(AppConstants.USER_ROLE_KEY, 'GUEST');
                console.log('GUEST card pressed');
                navigation.navigate('Authentication' as never);
              }}
            />
            <AccessCard
              title={t('common.welcome.host.title')}
              description={t('common.welcome.host.description')}
              image={require('../../assets/Host.jpg')}
              onPress={() => {
                setRole('HOST');
                storage.set(AppConstants.USER_ROLE_KEY, 'HOST');
                console.log('Host card pressed');
                navigation.navigate('ProAuthentication' as never);
              }}
            />
            <AccessCard
              title={t('common.welcome.pro.title')}
              description={t('common.welcome.pro.description')}
              image={require('../../assets/pro.jpg')}
              onPress={() => {
                setRole('PRO');
                storage.set(AppConstants.USER_ROLE_KEY, 'PRO');
                console.log('Pro card pressed');
                navigation.navigate('ProAuthentication' as never);
              }}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingVertical: 20,
  },
  logoContainer: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 20,
  },
  logo: {
    width: 200,
    height: 50,
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: '600',
    color: '#2B3A4A',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 28,
    color: '#2B3A4A',
    fontWeight: '500',
  },
  cardsContainerWrapper: {
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: '#DEC48F',
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#FAFAFA',
  },
  cardsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2B3A4A',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  cardsContainer: {
    gap: 16,
  },
  card: {
    height: Math.floor(windowHeight * 0.20),
    borderRadius: 40,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  cardBackground: {
    flex: 1,
  },
  cardContent: {
    flex: 1,
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    //alignItems: 'center',
    justifyContent: 'center',
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 16,
    color: '#fff',
  },
});

export default WelcomeScreen;