import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Modal, FlatList, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import * as Localization from 'expo-localization';
import { storage } from '@utils/storage';
import { AppConstants } from '@constants/default';

interface LanguageItem {
  code: string;
  name: string;
}

const LANGUAGES: LanguageItem[] = [
  { code: 'en', name: '🇬🇧 English' },
  { code: 'fr', name: '🇫🇷 Français' }
];

const LanguageSelector = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const { i18n, t } = useTranslation();

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = storage.getString(AppConstants.LANGUAGE_CACHE_KEY);
        if (savedLanguage) {
          i18n.changeLanguage(savedLanguage);
        }
      } catch (error) {
        console.error('Error loading language:', error);
      }
    };
    loadLanguage();
  }, []);

  // Get current language or default to device language
  const currentLanguage = i18n.language || Localization.getLocales()[0].languageTag.split('-')[0];

  const changeLanguage = async (languageCode: string) => {
    try {
      i18n.changeLanguage(languageCode); // the change listener will update the mmkv storage
      setModalVisible(false);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  };

  const renderLanguageItem = ({ item }: { item: LanguageItem }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        currentLanguage === item.code && styles.selectedLanguage,
      ]}
      onPress={() => changeLanguage(item.code)}
    >
      <Text style={styles.languageText}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.languageButton}
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.buttonText}>
          {LANGUAGES.find(lang => lang.code === currentLanguage)?.name}
        </Text>
      </TouchableOpacity>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('common.language_selector.title')}</Text>
            <FlatList
              data={LANGUAGES}
              renderItem={renderLanguageItem}
              keyExtractor={(item) => item.code}
            />
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.cancelText}>{t('common.language_selector.button')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  languageButton: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 8,
    minWidth: 150,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxHeight: '60%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  languageItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectedLanguage: {
    backgroundColor: '#e0e0e0',
  },
  languageText: {
    fontSize: 16,
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  cancelText: {
    textAlign: 'center',
    fontSize: 16,
    color: 'red',
  },
});

export default LanguageSelector;