import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withSpring, 
  withTiming 
} from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface SubscriptionButtonProps {
  isPending: boolean;
  isDisabled: boolean;
  onPress: () => void;
  text?: string;
}

const SubscriptionButton: React.FC<SubscriptionButtonProps> = ({ 
  isPending, 
  isDisabled, 
  onPress,
  text 
}) => {
  const { t } = useTranslation();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }]
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  return (
    <AnimatedTouchableOpacity
      style={[
        styles.button, 
        isDisabled ? styles.disabledButton : styles.activeButton,
        animatedStyle
      ]}
      onPress={!isDisabled ? onPress : undefined}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={isDisabled}
    >
      {isPending && <ActivityIndicator size="small" color="#0000ff" />}
      <Text style={[
        styles.buttonText,
        isDisabled ? styles.disabledText : styles.activeText
      ]}>
        {isDisabled 
          ? t('pro.subscription.button.select_plan') 
          : (text || t('pro.subscription.button.proceed_checkout'))}
      </Text>
    </AnimatedTouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    width: 'auto',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    margin: 30,
  },
  activeButton: {
    backgroundColor: '#3498DB',
  },
  disabledButton: {
    backgroundColor: '#95A5A6',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  activeText: {
    color: 'white',
  },
  disabledText: {
    color: '#BDC3C7',
  },
});

export default SubscriptionButton;