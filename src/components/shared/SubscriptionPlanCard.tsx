import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Animated, { FadeIn, Layout } from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';

interface Plan {
  name: string;
  price: number;
  description: string;
  durationInDays: number;
}

interface SubscriptionPlanCardProps {
  plan: Plan;
  isSelected: boolean;
  onSelect: () => void;
}

const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({ plan, isSelected, onSelect }) => {
  const { t } = useTranslation();

  return (
    <Animated.View 
      entering={FadeIn}
      layout={Layout}
      style={[
        styles.cardContainer,
        isSelected && styles.selectedCard
      ]}
    >
      <TouchableOpacity onPress={onSelect}>
        <View style={styles.cardContent}>
          <View style={styles.headerRow}>
            <Text style={styles.planName}>{plan.name}</Text>
            <Text style={styles.planPrice}>{plan.price}€/month</Text>
          </View>
          
          <Text style={styles.planDescription}>{plan.description}</Text>
          
          <View style={styles.detailsRow}>
            <Text style={styles.planDuration}>
              {t('pro.subscription.plans.duration', { days: plan.durationInDays })}
            </Text>
            
            {isSelected && (
              <View style={styles.selectedBadge}>
                <Text style={styles.selectedBadgeText}>{t('pro.subscription.plans.selected')}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#3498DB',
    borderRadius: 15,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCard: {
    borderColor: '#DEC48F',
  },
  cardContent: {
    padding: 20,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  planName: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  planPrice: {
    color: '#DEC48F',
    fontSize: 20,
    fontWeight: 'bold',
  },
  planDescription: {
    color: 'white',
    fontSize: 14,
    marginBottom: 10,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  planDuration: {
    color: '#D1987C',
    fontSize: 12,
  },
  selectedBadge: {
    backgroundColor: '#DEC48F',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  selectedBadgeText: {
    color: '#2C3E50',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default SubscriptionPlanCard;