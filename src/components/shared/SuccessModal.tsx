import React from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet } from 'react-native';
import LottieView from 'lottie-react-native';
import { useTranslation } from 'react-i18next';

interface SuccessModalProps {
  visible: boolean;
  onClose: () => void;
}

const SuccessModal: React.FC<SuccessModalProps> = ({ visible, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.lottieContainer}>
            <LottieView
              source={require('../../../assets/animations/success-animation.json')}
              autoPlay
              loop={false}
              style={styles.lottieAnimation}
            />
          </View>
          
          <Text style={styles.titleText}>{t('subscription.success.title')}</Text>
          
          <Text style={styles.subtitleText}>
            {t('subscription.success.subtitle')}
          </Text>
          
          <TouchableOpacity 
            style={styles.continueButton}
            onPress={onClose}
          >
            <Text style={styles.continueButtonText}>{t('subscription.success.continue')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  lottieContainer: {
    width: 200,
    height: 200,
    marginBottom: 20,
  },
  lottieAnimation: {
    width: '100%',
    height: '100%',
  },
  titleText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 25,
    textAlign: 'center',
  },
  continueButton: {
    backgroundColor: '#3498DB',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 50,
    elevation: 2,
  },
  continueButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
});

export default SuccessModal;