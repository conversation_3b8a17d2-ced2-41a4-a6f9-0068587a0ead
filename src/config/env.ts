import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Try to import from .env file
let envGoogleMapsApiKey: string | undefined;
try {
  // This will be replaced at build time by react-native-dotenv
  envGoogleMapsApiKey = require('@env').GOOGLE_MAPS_API_KEY;
} catch (error) {
  console.warn('Failed to import GOOGLE_MAPS_API_KEY from .env', error);
}

// Fallback mechanisms for API keys
export const GOOGLE_MAPS_API_KEY =
  // First try from .env file
  envGoogleMapsApiKey ||
  // Then try from Expo Constants (set via app.config.ts or eas.json)
  Constants.expoConfig?.extra?.googleMapsApiKey ||
  // Platform-specific fallbacks from native code
  (Platform.OS === 'ios'
    ? Constants.expoConfig?.ios?.config?.googleMapsApiKey
    : undefined) ||
  // Last resort fallback - empty string will cause Maps to show dev mode
  '';

// Export other environment variables as needed
export const API_BASE_URL =
  Constants.expoConfig?.extra?.API_BASE_URL ||
  'https://api.wodaabe-stays.com/api/v1';

// Function to check if we have valid API keys
export const validateApiKeys = (): { valid: boolean; missing: string[] } => {
  const missing: string[] = [];

  if (!GOOGLE_MAPS_API_KEY) missing.push('GOOGLE_MAPS_API_KEY');
  if (!API_BASE_URL) missing.push('API_BASE_URL');

  return {
    valid: missing.length === 0,
    missing
  };
};

// Debug function to check Google Maps API key
export const debugGoogleMapsApiKey = (): void => {
  console.log('Google Maps API Key Status:');
  console.log('- From .env:', envGoogleMapsApiKey ? 'Available' : 'Not available');
  console.log('- From Expo Constants:', Constants.expoConfig?.extra?.googleMapsApiKey ? 'Available' : 'Not available');
  console.log('- From iOS Config:', Platform.OS === 'ios' && Constants.expoConfig?.ios?.config?.googleMapsApiKey ? 'Available' : 'Not available');
  console.log('- Final API Key:', GOOGLE_MAPS_API_KEY ? `${GOOGLE_MAPS_API_KEY.substring(0, 5)}...` : 'Empty');
};
