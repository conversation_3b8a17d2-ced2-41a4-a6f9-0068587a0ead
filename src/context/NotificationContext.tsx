import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  ReactNode,
  useCallback,
} from "react";
import * as Notifications from "expo-notifications";
import { registerForPushNotificationsAsync } from "@/utils/registerForPushNotificationsAsync";
import { scheduleLocalNotification, cancelAllNotifications } from "@/utils/sendTestNotification";
import { Alert } from "react-native";
import { useAuthStore } from "@/stores/useAuthStore";
import { httpClient } from "@/utils/http";

interface NotificationContextType {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  error: string | null;
  isRegistered: boolean;
  sendTestNotification: (title: string, body: string, data?: Record<string, any>) => Promise<void>;
  cancelAllNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [notification, setNotification] =
    useState<Notifications.Notification | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isRegistered, setIsRegistered] = useState<boolean>(false);
  const { token } = useAuthStore(); // Get the auth token to check if user is logged in

  const notificationListener = useRef<any>();
  const responseListener = useRef<any>();
  const tokenRef = useRef<string | null>(null);

  // Function to send a test notification
  const sendTestNotification = useCallback(async (
    title: string,
    body: string,
    data: Record<string, any> = {}
  ) => {
    try {
      // Check if notifications are registered
      if (!isRegistered) {
        console.log('Notifications not registered, attempting to register...');
        // Only send to server if user is logged in
        const isLoggedIn = !!token;
        const pushToken = await registerForPushNotificationsAsync(isLoggedIn);
        if (pushToken) {
          setExpoPushToken(pushToken);
          setIsRegistered(true);
          console.log('Successfully registered for notifications with token:', pushToken);
        } else {
          throw new Error('Failed to register for notifications');
        }
      }

      // Schedule the notification
      const notificationId = await scheduleLocalNotification(title, body, data);
      console.log(`Test notification scheduled with ID: ${notificationId}`);

      // Check if the notification was received
      setTimeout(() => {
        if (!notification) {
          console.log('No notification received after scheduling');
        }
      }, 2000);

      Alert.alert("Success", "Test notification sent successfully!");
    } catch (error) {
      console.error("Error sending test notification:", error);
      Alert.alert("Error", `Failed to send test notification: ${error}`);
    }
  }, [isRegistered, notification, token]);

  // Function to cancel all notifications
  const cancelAllScheduledNotifications = useCallback(async () => {
    try {
      await cancelAllNotifications();
      console.log("All notifications cancelled");
      Alert.alert("Success", "All notifications cancelled");
    } catch (error) {
      console.error("Error cancelling notifications:", error);
      Alert.alert("Error", `Failed to cancel notifications: ${error}`);
    }
  }, []);

  // Register for push notifications (without sending to server if not logged in)
  const registerForNotifications = useCallback(async (sendToServer: boolean = false) => {
    try {
      const token = await registerForPushNotificationsAsync(sendToServer);
      if (token) {
        setExpoPushToken(token);
        setIsRegistered(true);
        console.log("Successfully registered for notifications with token:", token);
        return token;
      } else {
        setError("Failed to get push token");
        console.log("Failed to get push token");
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      console.error("Error registering for push notifications:", err);
      return null;
    }
  }, []);

  // Initial setup for notification listeners
  useEffect(() => {
    // Note: Notification handler is configured in App.tsx

    // Register for push notifications without sending to server initially
    registerForNotifications(false);

    // Set up notification listeners
    notificationListener.current =
      Notifications.addNotificationReceivedListener((receivedNotification) => {
        console.log("🔔 Notification Received:", receivedNotification);
        setNotification(receivedNotification);
      });

    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log(
          "🔔 Notification Response:",
          JSON.stringify(response, null, 2)
        );

        // Extract notification data
        const data = response.notification.request.content.data;
        console.log("Notification Data:", JSON.stringify(data, null, 2));

        // Handle notification response here (e.g., navigation)
        // You can add navigation logic based on the notification data
      });

    // Clean up listeners on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(
          notificationListener.current
        );
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [registerForNotifications]);

  // Watch for changes in auth token and register with server when logged in
  useEffect(() => {
    // Store the current token value in a ref to avoid unnecessary re-registrations
    if (token === tokenRef.current) {
      return;
    }

    tokenRef.current = token;

    // If user is logged in and we have a token, register with the server
    if (token) {
      console.log("User is logged in, registering push token with server");

      // If we already have a token, just send it to the server
      if (expoPushToken) {
        try {
          // Send the token to the server directly
          httpClient.post('/chat/register-push-token', {
            token: expoPushToken
          }).then(() => {
            console.log('Successfully sent existing push token to backend');
          }).catch((error: any) => {
            console.error('Error sending existing push token to backend:', error);
          });
        } catch (error: any) {
          console.error('Error sending existing push token to backend:', error);
        }
      } else {
        // Otherwise register for notifications and send to server
        registerForNotifications(true);
      }
    }
  }, [token, expoPushToken, registerForNotifications]);

  return (
    <NotificationContext.Provider
      value={{
        expoPushToken,
        notification,
        error,
        isRegistered,
        sendTestNotification,
        cancelAllNotifications: cancelAllScheduledNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};