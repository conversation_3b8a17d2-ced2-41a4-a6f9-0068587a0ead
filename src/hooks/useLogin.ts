import { useMutation } from "@tanstack/react-query";
import { httpClient } from "@/utils/http";

const loginUser = async ({ role, userContact, loginType }: { role: string, userContact: string, loginType: string }) => {
    console.log("loginUser" ,role, userContact, loginType);
    const body = {
        "login": userContact,
        "type" : loginType,
        "userType" : role
    };
    console.log("Body: ", body);
    
    return httpClient.post(
        '/users/authenticate',
        body
    );
}

export const useLogin = () => {
    return useMutation({
        mutationFn: loginUser,
    });
};