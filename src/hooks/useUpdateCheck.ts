import { useState, useEffect } from 'react';
import { Platform, Alert, Linking } from 'react-native';
import Constants from 'expo-constants';
import { useTranslation } from 'react-i18next';
import { storage } from '../utils/storage';

interface UpdateInfo {
  updateAvailable: boolean;
  currentVersion: string;
  storeVersion: string;
  storeUrl: string;
}

interface UpdatePreferences {
  autoCheckEnabled: boolean;
  lastDismissedVersion?: string;
  lastDismissedTime?: number;
}

const DEFAULT_PREFERENCES: UpdatePreferences = {
  autoCheckEnabled: true,
};

async function checkiOSVersion(): Promise<UpdateInfo | null> {
  try {
    const bundleId = Constants.expoConfig?.ios?.bundleIdentifier;
    if (!bundleId) return null;

    const response = await fetch(`https://itunes.apple.com/lookup?bundleId=${bundleId}`);
    const data = await response.json();

    if (data.results?.length > 0) {
      const storeVersion = data.results[0].version;
      const currentVersion = Constants.expoConfig?.version || '1.0.0';

      return {
        updateAvailable: storeVersion !== currentVersion,
        currentVersion,
        storeVersion,
        storeUrl: data.results[0].trackViewUrl,
      };
    }
  } catch (error) {
    console.log('iOS update check failed:', error);
  }
  return null;
}

async function checkAndroidVersion(): Promise<UpdateInfo | null> {
  try {
    const packageName = Constants.expoConfig?.android?.package;
    if (!packageName) return null;

    const response = await fetch(`https://play.google.com/store/apps/details?id=${packageName}`);
    const html = await response.text();

    // Parse HTML to extract version - look for version pattern
    const versionMatch = html.match(/Current Version.*?>([\d.]+)</i) ||
      html.match(/\[{2}"([\d.]+)"/);

    if (versionMatch) {
      const storeVersion = versionMatch[1];
      const currentVersion = Constants.expoConfig?.version || '1.0.0';

      return {
        updateAvailable: storeVersion !== currentVersion,
        currentVersion,
        storeVersion,
        storeUrl: `https://play.google.com/store/apps/details?id=${packageName}`,
      };
    }
  } catch (error) {
    console.log('Android update check failed:', error);
  }
  return null;
}

function getPreferences(): UpdatePreferences {
  try {
    const stored = storage.getString('update-preferences');
    return stored ? { ...DEFAULT_PREFERENCES, ...JSON.parse(stored) } : DEFAULT_PREFERENCES;
  } catch {
    return DEFAULT_PREFERENCES;
  }
}

function setPreferences(prefs: UpdatePreferences) {
  storage.set('update-preferences', JSON.stringify(prefs));
}

function shouldShowUpdate(updateInfo: UpdateInfo): boolean {
  const prefs = getPreferences();

  // Check if user dismissed this version recently (within 24 hours)
  if (prefs.lastDismissedVersion === updateInfo.storeVersion && prefs.lastDismissedTime) {
    const hoursSinceDismissal = (Date.now() - prefs.lastDismissedTime) / (1000 * 60 * 60);
    if (hoursSinceDismissal < 24) {
      return false;
    }
  }

  return true;
}

function openAppStore(storeUrl: string) {
  Linking.openURL(storeUrl);
}

export function useUpdateCheck() {
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const { t } = useTranslation();

  const checkForUpdates = async (showAlert = true) => {
    const prefs = getPreferences();
    if (!prefs.autoCheckEnabled && !showAlert) return;

    setIsChecking(true);

    try {
      const result = Platform.OS === 'ios'
        ? await checkiOSVersion()
        : await checkAndroidVersion();

      if (result?.updateAvailable) {
        setUpdateInfo(result);

        if (showAlert && shouldShowUpdate(result)) {
          showUpdateAlert(result);
        }
      }
    } catch (error) {
      console.log('Update check failed:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const showUpdateAlert = (info: UpdateInfo) => {
    // Fallback translations in case i18n is not loaded yet
    const title = t('common.update.title', { defaultValue: 'Update Available' });
    const message = t('common.update.message', {
      storeVersion: info.storeVersion,
      currentVersion: info.currentVersion,
      defaultValue: `A new version (${info.storeVersion}) is available. You're currently using version ${info.currentVersion}.`
    });
    const laterText = t('common.update.buttons.later', { defaultValue: 'Later' });
    const updateText = t('common.update.buttons.update', { defaultValue: 'Update' });

    Alert.alert(
      title,
      message,
      [
        {
          text: laterText,
          style: 'cancel',
          onPress: () => {
            const prefs = getPreferences();
            setPreferences({
              ...prefs,
              lastDismissedVersion: info.storeVersion,
              lastDismissedTime: Date.now(),
            });
          },
        },
        {
          text: updateText,
          onPress: () => openAppStore(info.storeUrl),
        },
      ]
    );
  };

  return {
    updateInfo,
    isChecking,
    checkForUpdates,
    showUpdateAlert,
  };
}