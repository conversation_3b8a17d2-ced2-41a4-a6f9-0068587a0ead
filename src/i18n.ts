import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import { storage } from './utils/storage';
import { AppConstants } from './constants/default';

// Import translation JSON files
import enCommon from './locales/common/en.json';
import enWelcome from './locales/welcome/en.json';
import enAuth from './locales/auth/en.json';
import enOnboarding from './locales/onboarding/en.json';
import enHost from './locales/host/en.json';
import enSettings from './locales/settings/en.json';
import enBookings from './locales/bookings/en.json';
import enServices from './locales/services/en.json';
import enChat from './locales/chat/en.json';
import enProServices from './locales/pro/services/en.json';
import enProSubscription from './locales/pro/subscription/en.json';

import frCommon from './locales/common/fr.json';
import frWelcome from './locales/welcome/fr.json';
import frAuth from './locales/auth/fr.json';
import frOnboarding from './locales/onboarding/fr.json';
import frHost from './locales/host/fr.json';
import frSettings from './locales/settings/fr.json';
import frBookings from './locales/bookings/fr.json';
import frServices from './locales/services/fr.json';
import frChat from './locales/chat/fr.json';
import frProServices from './locales/pro/services/fr.json';
import frProSubscription from './locales/pro/subscription/fr.json';

import { TranslationKeys } from './types/translationKeys';

const resources = {
  en: {
    translation: {
      common: enCommon,
      welcome: enWelcome,
      auth: enAuth,
      onboarding: enOnboarding,
      host: enHost,
      settings: enSettings,
      ...enBookings,
      services: enServices,
      chat: enChat,
      pro: {
        services: enProServices,
        subscription: enProSubscription
      }
    } satisfies TranslationKeys
  },
  fr: {
    translation: {
      common: frCommon,
      welcome: frWelcome,
      auth: frAuth,
      onboarding: frOnboarding,
      host: frHost,
      settings: frSettings,
      ...frBookings,
      services: frServices,
      chat: frChat,
      pro: {
        services: frProServices,
        subscription: frProSubscription
      }
    } satisfies TranslationKeys
  }
} as const;

declare module 'i18next' {
  interface CustomTypeOptions {
    resources: typeof resources['en']['translation'];
  }
}

const getInitialLanguage = (): string => {
  try {
    const savedLanguage = storage.getString(AppConstants.LANGUAGE_CACHE_KEY);
    if (savedLanguage) {
      return savedLanguage;
    }
  } catch (error) {
    console.warn('Failed to load language from storage:', error);
  }
  
  return Localization.getLocales()[0].languageTag.split('-')[0];
}

const initializeI18n = async () => {
  const initialLanguage = getInitialLanguage();
  
  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: initialLanguage,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false
      },
      // Performance optimizations
      returnNull: false,
      returnEmptyString: false,
      returnObjects: true,
      cache: {
        enabled: true,
        maxAge: 7 * 24 * 60 * 60 * 1000, // Cache for 7 days
      },
      detection: {
        caches: ['localStorage'],
        order: ['querystring', 'localStorage', 'navigator']
      }
    });

  // Use MMKV for language changes
  i18n.on('languageChanged', (lng) => {
    storage.set(AppConstants.LANGUAGE_CACHE_KEY, lng);
  });

  return i18n;
};

void initializeI18n();

export default i18n;