# Translations Structure

This directory contains all the translation files for the application, organized in a modular structure by feature/domain.

## Directory Structure

```
locales/
├── README.md
├── common/          # Shared translations (buttons, form fields, etc.)
├── welcome/         # Welcome screen and role selection
├── auth/           # Authentication and OTP screens
├── onboarding/     # Guest onboarding process
├── host/           # Host-specific features
├── guest/          # Guest-specific features
├── pro/            # Professional service provider features
└── settings/       # Settings and preferences
```

## Guidelines

1. **Modular Organization**
   - Keep translations organized by feature/domain
   - Place shared/reusable translations in the `common` directory
   - Create new directories for new features

2. **File Naming**
   - Use `en.json` and `fr.json` in each directory for respective languages
   - Maintain consistent file names across all directories

3. **Translation Keys**
   - Use descriptive, lowercase keys with underscores
   - Group related translations using nested objects
   - Keep a consistent structure between different language files

4. **Type Safety**
   - Update `src/types/translationKeys.ts` when adding new translations
   - Ensure all translations satisfy the TypeScript interfaces

5. **Best Practices**
   - Keep translations simple and context-aware
   - Use interpolation for dynamic content: `{{variable}}`
   - Add comments for complex translations or special formatting

## Usage Example

```typescript
// Using translations in components
const { t } = useTranslation();

// Accessing nested translations
t('common.buttons.next')
t('welcome.roles.guest.title')
```

## Adding New Translations

1. Create translation files in the appropriate feature directory
2. Add translations for all supported languages
3. Update the TypeScript interfaces in `translationKeys.ts`
4. Import and include new translations in `src/i18n.ts`