{
  "media": {
    "title": "Media",
    "main_photo": {
      "placeholder": "Add a Cover Photo"
    },
    "add_media": {
      "image": "Add Image",
      "video": "Add Video",
      "document": "Add Document"
    },
    "processing": "Processing...",
    "description_placeholder": "Add a description for this media...",
    "document_placeholder": "Document",
    "cover_badge": "Cover",
    "set_cover": "Set as Cover",
    "empty_state": {
      "title": "No Media Added",
      "subtitle": "Add images, videos, or documents to showcase your service."
    }
  },
  "errors": {
    "fetch_categories": "Failed to load service categories. Please try again.",
    "no_categories": "No categories available"
  },
  "categories": {
    "cleaning-service": "Cleaning Service",
    "maintenance---repairs": "Maintenance & Repairs",
    "property-management": "Property Management",
    "security-services": "Security Services",
    "landscaping": "Landscaping",
    "interior-design": "Interior Design",
    "check-in-services": "Check-in Services",
    "pest-control": "Pest Control",
    "pool-maintenance": "Pool Maintenance",
    "smart-home-setup": "Smart Home Setup",
    "photography": "Photography",
    "concierge-services": "Concierge Services"
    // Add other category slugs and their English translations here
  },
  "services": {
    "create_service": {
      "title": "Create New Service",
      "subtitle": "Fill in the details below to list your service",
      "tabs": {
        "basic": "Basic Info",
        "contact": "Contact",
        "media": "Media"
      },
      "basic_info": {
        "title": "Basic Information",
        "service_title": {
          "placeholder": "Enter service title"
        },
        "price": {
          "placeholder": "Price (e.g., 50)"
        },
        "description": {
          "placeholder": "Describe your service..."
        },
        "address": {
          "placeholder": "Service address or area covered"
        },
        "location": {
          "latitude": "Latitude",
          "longitude": "Longitude",
          "permission_required": "Location Permission Required",
          "permission_message": "Please grant location permission to automatically fill address and coordinates."
        }
      },
      "contact_info": {
        "title": "Contact Information",
        "company_name": {
          "placeholder": "Company Name (Optional)"
        },
        "email": {
          "placeholder": "Contact Email"
        },
        "phone": {
          "placeholder": "Contact Phone Number"
        }
      },
      "media": {
        "title": "Showcase Your Service",
        "main_photo": {
          "placeholder": "Add a Cover Photo"
        },
        "add_media": {
          "image": "Add Image",
          "video": "Add Video",
          "document": "Add Document"
        },
        "processing": "Processing...",
        "description_placeholder": "Add a description for this media...",
        "document_placeholder": "Document",
        "cover_badge": "Cover",
        "set_cover": "Set as Cover",
        "empty_state": {
          "title": "No Media Added",
          "subtitle": "Add images, videos, or documents to showcase your service."
        }
      },
      "validation": {
        "title_required": "Service title is required",
        "description_required": "Service description is required",
        "price_required": "Price is required and must be greater than 0",
        "contact_required": "Email and Phone Number are required",
        "media_required": "At least one media item is required"
      },
      "submit": {
        "button": "Create Service",
        "success": {
          "title": "Success!",
          "message": "Your service has been created successfully."
        },
        "error": {
          "title": "Error",
          "message": "Failed to create service. Please try again."
        }
      },
      "errors": {
        "fetch_categories": "Failed to load service categories. Please try again.",
        "no_categories": "No categories available"
      },
      "categories": {
        "cleaning-service": "Cleaning Service",
        "maintenance---repairs": "Maintenance & Repairs",
        "property-management": "Property Management",
        "security-services": "Security Services",
        "landscaping": "Landscaping",
        "interior-design": "Interior Design",
        "check-in-services": "Check-in Services",
        "pest-control": "Pest Control",
        "pool-maintenance": "Pool Maintenance",
        "smart-home-setup": "Smart Home Setup",
        "photography": "Photography",
        "concierge-services": "Concierge Services"
        // Add other category slugs and their English translations here
      }
    }
  }
} 