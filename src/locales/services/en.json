{"title": "Services", "search": "Search...", "noListings": "No listings available.", "noFilterResults": "No services match your filters.", "clearFilters": "Clear Filters", "clearAll": "Clear All", "filters": "Filters", "apply": "Apply", "reset": "Reset", "min": "Min", "max": "Max", "price": "Price", "rating": "Rating", "distance": "Distance", "any": "Any", "priceRange": "Price Range", "minRating": "Minimum Rating", "maxDistance": "Maximum Distance", "sortBy": "Sort By", "loading": "Loading services...", "error": "Failed to load listings. Please try again later.", "retry": "Retry", "category": "Category", "viewAll": "View all", "categories": {"diy": "DIY", "gardening": "Gardening", "housekeeping": "Housekeeping"}, "serviceCard": {"reviews_one": "{{count}} review", "reviews_other": "{{count}} reviews", "distance": "{{distance}} km"}, "details": {"serviceNotFound": "Service not found", "information": "Information", "email": "Email", "phone": "Phone", "name": "Name", "company": "Company", "gender": "Gender", "category": "Category", "media": "Media", "location": "Location", "contact": "Contact", "seeContact": "See Contact", "chatWithPro": "Chat with this pro", "payment": {"completePayment": "Complete Payment", "loadingPayment": "Loading payment page...", "successful": "Payment Successful!", "viewContactDetails": "You can now view the contact details."}, "error": {"errorMessage": "Error: {{message}}"}, "rating": {"rateService": "Rate Service", "title": "Rate this Service", "subtitle": "How was your experience?", "feedbackPlaceholder": "Share your experience (optional)", "submit": "Submit Rating", "thankYou": "Thank you for your feedback!", "errorTitle": "Rating Error", "errorMessage": "Failed to submit rating. Please try again.", "selectRatingTitle": "Select Rating", "selectRatingMessage": "Please select a rating before submitting."}, "ratingWithCount": "{{count}} reviews", "noRatings": "No reviews"}, "create_service": {"title": "Services Pro", "subtitle": "Manage your professional services", "tabs": {"basic": "Basic", "contact": "Contact", "media": "Media"}, "basic_info": {"title": "Basic Information", "service_title": {"placeholder": "Enter your service title"}, "price": {"placeholder": "Enter price"}, "description": {"placeholder": "Describe your service"}, "address": {"placeholder": "Enter your address"}, "location": {"latitude": "Latitude", "longitude": "Longitude", "permission_required": "Location Permission Required", "permission_message": "We need your location to better serve your customers"}}, "contact_info": {"title": "Contact Information", "company_name": {"placeholder": "Enter company name"}, "email": {"placeholder": "Enter your email"}, "phone": {"placeholder": "Enter your phone number"}}, "media": {"title": "Media", "main_photo": {"title": "Main Photo", "placeholder": "Add a main photo for your service", "add_button": "Add Photo"}, "add_media": {"image": "Image", "video": "Video", "document": "Document"}, "description_placeholder": "Add a description for this media...", "cover_badge": "Cover", "set_cover": "Set as cover", "empty_state": {"title": "No media added yet", "subtitle": "Add images, videos, or documents to showcase your service"}, "processing": "Processing media...", "document_placeholder": "Document"}, "validation": {"title_required": "Service title is required", "description_required": "Service description is required", "price_required": "Price is required", "contact_required": "Phone number and email are required", "media_required": "At least one media item is required"}, "submit": {"button": "Create Service", "success": {"title": "Success", "message": "Your service has been created successfully"}, "error": {"title": "Error", "message": "Failed to create service. Please try again."}}}, "pro": {"services": {"create_service": {"media": {"description_placeholder": "Add a description for this media..."}}}}}