import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { RootStack<PERSON>aramList, UserTabs<PERSON>aramList, ChatStackParamList, ActivitiesStackParamList, HostTabsParamList, ProTabsParamList } from "../types/navigation";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from '@expo/vector-icons';

import ActivityMapScreen from "../screens/activities/ActivityMapScreen";
import ActivityListScreen from "../screens/activities/ActivityListScreen";
import ActivityDetailsScreen from "../screens/activities/ActivityDetailsScreen";

import ChatListScreen from "../screens/chat/ChatListScreen";
import ChatRoomScreen from "../screens/chat/ChatRoomScreen";

import AccommodationScreen from "../screens/accomodation/AccommodationScreen";
import AuthenticationScreen from "../screens/AuthenticationScreen";
import OnboardingScreen from "../screens/OnboardingScreen";
import RoleSelectionScreen from "../screens/RoleSelectionScreen";
import OtpScreen from "@/screens/OtpScreen";
import ProAuthenticationScreen from "@/screens/ProAuthenticationScreen";
import RegisterProfileScreen from "@/screens/RegisterProfileScreen";
import { useTranslation } from "react-i18next";
import ProOtpScreen from "@/screens/ProOtpScreen";
import { storage } from "@/utils/storage";
import { AppConstants } from "@/constants/default";
import { useAuthStore } from "@/stores/useAuthStore";
import { useEffect } from "react";
import AccommodationDetailsScreen from "@/screens/accomodation/AccommodationDetailsScreen";
import AccommodationCreationScreen from "@/screens/accomodation/AccommodationCreationScreen";
import AccommodationUpdateScreen from "@/screens/accomodation/AccommodationUpdateScreen";

import BookingScreen from "@/screens/bookings/BookingsScreen";
import BookingDetailsScreen from "@/screens/bookings/BookingDetailsScreen";
import BookingCreationScreen from "@/screens/bookings/BookingCreationScreen";
import BookingUpdateScreen from "@/screens/bookings/BookingUpdateScreen";
import ServicesScreen from "@/screens/services/ServicesScreen";
import ServiceDetailsScreen from "@/screens/services/ServiceDetailsScreen";
import ProServicesScreen from "@/screens/pro/services/ProServicesScreen";
import { useNavigation } from "@react-navigation/native";
import ProServiceDetailsScreen from "@/screens/pro/services/ProServicesDetailsScreen";
import ProServicesCreationScreen from "@/screens/pro/services/ProServicesCreationScreen";
import ProServicesUpdateScreen from '@/screens/pro/services/ProServicesUpdateScreen';
import UserAccommodationScreen from "@/screens/guest/GuestAccomodationScreen";
import ProSubscriptionPlansScreen from "@/screens/pro/subscription/ProSubscriptionPlansScreen";
import GuestChatListScreen from "@/screens/guest chat/GuestChatListScreen";
import GuestChatRoomScreen from "@/screens/guest chat/GuestChatRoomScreen";
import HostChatListScreen from "@/screens/host chat/HostChatListScreen";
import HostChatRoomScreen from "@/screens/host chat/HostChatRoomScreen";
import UserMenuScreen from "@/screens/UserMenuScreen";
import ProSubscriptionStatusScreen from "@/screens/pro/subscription/ProSubscriptionStatusScreen";
import EditProfileScreen from "@/screens/EditProfileScreen";

const RootStack = createNativeStackNavigator<RootStackParamList>();

const UserTabs = createBottomTabNavigator<UserTabsParamList>();
const GuestChatStack = createNativeStackNavigator<ChatStackParamList>();
const ChatStack = createNativeStackNavigator<ChatStackParamList>();
const ActivitiesStack = createNativeStackNavigator<ActivitiesStackParamList>();

const HostTabs = createBottomTabNavigator<HostTabsParamList>();
const AccommodationStack = createNativeStackNavigator();
const BookingStack = createNativeStackNavigator();
const ServicesStack = createNativeStackNavigator();
const HostChatStack = createNativeStackNavigator();
const UserMenuStack = createNativeStackNavigator();

const ProTabs = createBottomTabNavigator<ProTabsParamList>();
const ProServicesStack = createNativeStackNavigator();
const ProSubscriptionStack = createNativeStackNavigator();

// Utility Menu Component (for inline menu items)
const UtilityMenu = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.utilityMenu}>
      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {/* Handle Language Change */}}
      >
        <Ionicons name="language-outline" size={24} />
        <Text>{t('change_language')}</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {/* Handle Logout */}}
      >
        <Ionicons name="log-out-outline" size={24} />
        <Text>{t('logout')}</Text>
      </TouchableOpacity>

      {/* Add more utility actions */}
    </View>
  );
};

// Host Tab Navigator
function HostTabNavigator() {
  const { t } = useTranslation();
  return (
    <HostTabs.Navigator>
      <HostTabs.Screen
        name="HostAccommodation"
        component={AccommodationStackNavigator}
        options={{
          title: t('common.tab_bar.listings'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
      <HostTabs.Screen
        name="HostBookings"
        component={BookingStackNavigator}
        options={{
          title: t('common.tab_bar.bookings'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="calendar" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
      <HostTabs.Screen
        name="HostProServices"
        component={ServicesStackNavigator}
        options={{
          title: t('common.tab_bar.services'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="briefcase-outline" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
      <HostTabs.Screen
        name="HostChat"
        component={HostChatNavigator}
        options={{
          title: t('common.tab_bar.chat'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="chatbubbles-outline" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
      <HostTabs.Screen
        name="HostMenu"
        component={UserMenuStackNavigator}
        options={{
          title: t('common.tab_bar.profile'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
    </HostTabs.Navigator>
  );
}

// Services Stack Navigator
function ServicesStackNavigator() {
  return (
    <ServicesStack.Navigator>
      <ServicesStack.Screen
        name="Services"
        component={ServicesScreen}
      />
      <ServicesStack.Screen
        name="ServiceDetails"
        component={ServiceDetailsScreen}
        options={{
          headerShown: false
        }}
      />
    </ServicesStack.Navigator>
  );
}

// Services Stack Navigator
function UserMenuStackNavigator() {
  const { t } = useTranslation();
  return (
    <UserMenuStack.Navigator>
      <UserMenuStack.Screen
        name="UserMenu"
        component={UserMenuScreen}
        options={{
          title: t('common.tab_bar.profile'),
          headerShown: false,
        }}
      />
      <UserMenuStack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: t('common.settings.menu_items.edit_profile'),
          headerShown: true,
          headerStyle: {
            backgroundColor: '#ffffff',
          },
          headerTransparent: false,
          headerBlurEffect: 'regular',
          // Ensure proper edge-to-edge behavior
          contentStyle: { backgroundColor: '#ffffff' },
        }}
      />
    </UserMenuStack.Navigator>
  );
}

// Accomodation Stack Navigator
function AccommodationStackNavigator() {
  return (
    <AccommodationStack.Navigator
        initialRouteName="Accommodation"
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: '#ffffff',
          },
          headerTransparent: false,
          headerBlurEffect: 'regular',
          // Ensure proper edge-to-edge behavior
          contentStyle: { backgroundColor: '#ffffff' },
        }}
    >
      <AccommodationStack.Screen
        name="Accommodation"
        component={AccommodationScreen}
      />
      <AccommodationStack.Screen
        name="AccommodationDetails"
        component={AccommodationDetailsScreen}
      />
      <AccommodationStack.Screen
        name="AccommodationCreation"
        component={AccommodationCreationScreen}
        options={{
          presentation: 'modal',
        }}
      />
      <AccommodationStack.Screen
        name="AccommodationUpdate"
        component={AccommodationUpdateScreen}
        options={{
          presentation: 'modal',
        }}
      />
    </AccommodationStack.Navigator>
  )
}

function BookingStackNavigator() {
  return (
    <BookingStack.Navigator
      initialRouteName="Bookings"
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: '#ffffff',
        },
        headerTransparent: false,
        headerBlurEffect: 'regular',
        // Ensure proper edge-to-edge behavior
        contentStyle: { backgroundColor: '#ffffff' },
      }}
    >
      <BookingStack.Screen
        name="Bookings"
        component={BookingScreen}
      />
      <BookingStack.Screen
        name="BookingDetails"
        component={BookingDetailsScreen}
        options={({ route }) => ({
          title: route?.params?.title || 'Booking details',
        })}
      />

      {/* Group modal screens together */}
      <BookingStack.Group screenOptions={{ presentation: 'modal' }}>
        <BookingStack.Screen
          name="BookingCreation"
          component={BookingCreationScreen}
        />
        <BookingStack.Screen
          name="BookingUpdate"
          component={BookingUpdateScreen}
        />
      </BookingStack.Group>
    </BookingStack.Navigator>
  );
}

function ProSubscriptionStackNavigator() {
  return (
    <ProSubscriptionStack.Navigator
      initialRouteName="ProSubscriptionStatus"
      screenOptions={{
        headerShown: false,
      }}
    >
      <ProSubscriptionStack.Screen
        name="ProSubscriptionPlans"
        component={ProSubscriptionPlansScreen}
      />
      <ProSubscriptionStack.Screen
        name="ProSubscriptionStatus"
        component={ProSubscriptionStatusScreen}
      />
    </ProSubscriptionStack.Navigator>
  )
}

// Pro Tab Navigator
function ProTabNavigator() {
  const { t } = useTranslation();
  return (
    <ProTabs.Navigator>
      <ProTabs.Screen
        name="ProServices"
        component={ProServicesNavigator}
        options={{
          title: t('common.tab_bar.services'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <ProTabs.Screen
        name="ProSubscription"
        component={ProSubscriptionStackNavigator}
        options={{
          title: t('common.tab_bar.subscription'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="card-outline" size={size} color={color} />
          ),
        }}
      />
      <ProTabs.Screen
        name="ProChat"
        component={ChatNavigator}
        options={{
          title: t('common.tab_bar.chat'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="chatbubbles-outline" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
      <ProTabs.Screen
        name="ProMenu"
        component={UserMenuStackNavigator}
        options={{
          title: t('common.tab_bar.profile'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person" size={size} color={color} />
          ),
          headerShown: false,
        }}
      />
    </ProTabs.Navigator>
  );
}

function ProServicesNavigator() {
  return (
    <ProServicesStack.Navigator>
      <ProServicesStack.Screen
        name="ProServicesList"
        component={ProServicesScreen}
        options={{
          headerShown: false
        }}
      />
      <ProServicesStack.Screen
        name="ProServiceDetails"
        component={ProServiceDetailsScreen}
        options={{
          headerShown: false
        }}
      />
      <ProServicesStack.Screen
        name="ProServiceCreation"
        component={ProServicesCreationScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <ProServicesStack.Screen
        name="ProServicesUpdate"
        component={ProServicesUpdateScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
    </ProServicesStack.Navigator>
  );
}

// Activities Stack Navigator
function ActivitiesNavigator() {
    return (
        <ActivitiesStack.Navigator
            initialRouteName="ActivityMap"
          >
            <ActivitiesStack.Screen
                name="ActivityMap"
                component={ActivityMapScreen}
                options={{ title: 'Carte' }}
            />
            <ActivitiesStack.Screen
                name="ActivityList"
                component={ActivityListScreen}
                options={{ title: 'Près de moi' }}
            />
            <ActivitiesStack.Screen
                name="ActivityDetails"
                component={ActivityDetailsScreen}
                options={{ title: 'Détails' }}
            />
        </ActivitiesStack.Navigator>
    );
}

  // Chat Stack Navigator
  function ChatNavigator() {
    const { t } = useTranslation();
    return (
      <ChatStack.Navigator
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: '#ffffff',
          },
          headerTransparent: false,
          headerBlurEffect: 'regular',
          contentStyle: { backgroundColor: '#ffffff' },
        }}
      >
        <ChatStack.Screen
          name="ChatList"
          component={ChatListScreen}
          options={{ title: t('common.chat.title') }}
        />
        <ChatStack.Screen
          name="ChatRoom"
          component={ChatRoomScreen}
          options={{ title: t('common.chat.title') }}
        />
      </ChatStack.Navigator>
    );
  }

  // Host Chat Stack Navigator
  function HostChatNavigator() {
    const { t } = useTranslation();
    return (
      <HostChatStack.Navigator
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: '#ffffff',
          },
          headerTransparent: false,
          headerBlurEffect: 'regular',
          contentStyle: { backgroundColor: '#ffffff' },
        }}
      >
        <HostChatStack.Screen
          name="HostChatList"
          component={HostChatListScreen}
          options={{ title: t('common.hostChat.title') }}
        />
        <HostChatStack.Screen
          name="HostChatRoom"
          component={HostChatRoomScreen}
          options={{
            title: t('common.hostChat.title'),
            headerBackTitle: '',
            headerTitleStyle: {
              fontSize: 16,
              fontWeight: '600',
            },
          }}
        />
      </HostChatStack.Navigator>
    );
  }

  // Guest Chat Stack Navigator
  function GuestChatNavigator() {
    const { t } = useTranslation();
    return (
      <GuestChatStack.Navigator
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: '#ffffff',
          },
          headerTransparent: false,
          headerBlurEffect: 'regular',
          contentStyle: { backgroundColor: '#ffffff' },
        }}
      >
        <GuestChatStack.Screen
          name="ChatList"
          component={GuestChatListScreen}
          options={{ title: t('common.chat.title') }}
        />
        <GuestChatStack.Screen
          name="ChatRoom"
          component={GuestChatRoomScreen}
          options={{ title: t('common.chat.title') }}
        />
      </GuestChatStack.Navigator>
    );
  }

  // User Tab Navigator
  function UserTabNavigator() {
    const { t } = useTranslation();
    return (
      <UserTabs.Navigator>
        <UserTabs.Screen
          name="Accommodation"
          component={UserAccommodationScreen}
          options={{
            title: t('common.tab_bar.listings'),
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="home" color={color} size={size} />
            ),
          }}
        />
        <UserTabs.Screen
          name="Activities"
          component={ActivitiesNavigator}
          options={{
            title: t('common.tab_bar.activities'),
            headerShown: false,
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="map" color={color} size={size} />
            ),
          }}
        />
        <UserTabs.Screen
          name="Chat"
          component={GuestChatRoomScreen}
          options={{
            title: t('common.tab_bar.chat'),
            headerShown: true,
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="chatbubble" color={color} size={size} />
            ),
          }}
        />
        <UserTabs.Screen
          name="Profile"
          component={UserMenuStackNavigator}
          options={{
            title: t('common.tab_bar.profile'),
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="person" color={color} size={size} />
            ),
            headerShown: false,
          }}
        />
      </UserTabs.Navigator>
    );
  }

  // Root Navigator
const Navigation = () => {
    const { token, role, onboardingCompleted } = useAuthStore();
    const navigation = useNavigation();

    // console.log('userRole', role);
    // console.log('token', token);

    // useEffect(() => {
    //   if (token) {
    //     switch (role) {
    //       case 'GUEST':
    //         navigation.navigate('UserTabs');
    //         break;
    //       case 'HOST':
    //         navigation.navigate('HostTabs');
    //         break;
    //       case 'PRO':
    //         navigation.navigate('ProTabs');
    //         break;
    //       default:
    //         navigation.navigate('RoleSelection');
    //     }
    //   }
    //   else {
    //     navigation.navigate('RoleSelection');
    //   }
    // }, [token]);

    const roleNavigators = {
      GUEST: <RootStack.Screen name="UserTabs" component={UserTabNavigator} />,
      HOST: <RootStack.Screen name="HostTabs" component={HostTabNavigator} />,
      PRO: <RootStack.Screen name="ProTabs" component={ProTabNavigator} />,
    };

    return (
      <RootStack.Navigator
        screenOptions={{
          headerShown: false,
          // Global edge-to-edge configuration
          contentStyle: { backgroundColor: 'transparent' },
        }}
      >
        {!token ? (
            <>
                <RootStack.Screen name="RoleSelection" component={RoleSelectionScreen} />
                <RootStack.Screen name="Authentication" component={AuthenticationScreen} />
                <RootStack.Screen name="ProAuthentication" component={ProAuthenticationScreen} />
                <RootStack.Screen name="OTP" component={OtpScreen} />
                <RootStack.Screen name="ProOTP" component={ProOtpScreen} options={{ headerShown: false }} />
                <RootStack.Screen name="RegisterProfile" component={RegisterProfileScreen} />
            </>
        ) : !onboardingCompleted ? (
            <RootStack.Screen name="Onboarding" component={OnboardingScreen} />
        ) : (
            <>
                {role === "GUEST" && <RootStack.Screen name="UserTabs" component={UserTabNavigator} />}
                {role === "HOST" && <RootStack.Screen name="HostTabs" component={HostTabNavigator} />}
                {role === "PRO" && <RootStack.Screen name="ProTabs" component={ProTabNavigator} />}
            </>
        )}
      </RootStack.Navigator>
  );
};

export default Navigation;