import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Vibration,
  TextInput,
  ScrollView,
  Keyboard
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
// @ts-ignore
import SmoothPinCodeInput from 'react-native-smooth-pincode-input';
import { useTranslation } from 'react-i18next';
import * as Haptics from 'expo-haptics';
import LottieView from 'lottie-react-native';
import PhoneInput from 'react-native-international-phone-number';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { storage } from '@/utils/storage';
import { AppConstants } from '@/constants/default';
import BackButton from '@/components/BackButton';

type RootStackParamList = {
  OTP: { login: string };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface AuthResponse {
  code: number;
  data?: {
    login?: string;
    messages?: [{ messageContent: string }];
  };
}

interface AuthError {
  response?: {
    data?: {
      messages?: [{ messageContent: string }];
    };
  };
}

const { width } = Dimensions.get('window');

const AuthenticationScreen = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProp>();
  const { setUserCode, userCode } = useAuthStore();
  const [code, setCode] = useState('');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<any>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const errorShakeAnim = useRef(new Animated.Value(0)).current;
  const successAnimation = useRef(null);
  const emailInputRef = useRef(null);
  const pinInputRef = useRef<any>(null);

  // Email validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Phone number validation for international format
  const validatePhoneNumber = (phone: string) => {
    // Phone number is optional, so empty is valid
    if (phone.length === 0) return true;

    // If phone number is provided, it should have reasonable length
    // The PhoneInput component handles country-specific validation
    return phone.length >= 3; // Minimum reasonable length for any phone number
  };

  // Shake animation on error
  const shakeAnimation = () => {
    Animated.sequence([
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();

    if (Platform.OS === 'ios') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } else {
      Vibration.vibrate(400);
    }
  };

  // Button press animation
  const animateButtonPress = () => {
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Handle code input change
  const onCodeChange = (value: string) => {
    setCode(value);
    if (error) setError('');
    if (Platform.OS === 'ios' && value.length > 0) {
      Haptics.selectionAsync();
    }
  };

  // Handle email input change
  const onEmailChange = (value: string) => {
    setEmail(value);
    setEmailError('');
    if (Platform.OS === 'ios' && value.length > 0) {
      Haptics.selectionAsync();
    }
  };

  // Handle phone input change
  const onPhoneChange = (value: string) => {
    setPhoneNumber(value);
    setPhoneError('');
    if (Platform.OS === 'ios' && value.length > 0) {
      Haptics.selectionAsync();
    }
  };

  // Handle country selection
  const handleSelectedCountry = (country: any) => {
    setSelectedCountry(country);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (code.length !== 6) {
      setError(t('auth.guest.login.error.incomplete'));
      shakeAnimation();
      return;
    }

    if (!email) {
      setEmailError(t('auth.guest.login.email.validation.required'));
      shakeAnimation();
      return;
    }

    if (!validateEmail(email)) {
      setEmailError(t('auth.guest.login.email.validation.format'));
      shakeAnimation();
      return;
    }

    if (phoneNumber && !validatePhoneNumber(phoneNumber)) {
      setPhoneError(t('auth.guest.login.phone.validation.format'));
      shakeAnimation();
      return;
    }

    animateButtonPress();
    setIsLoading(true);
    setError('');
    setEmailError('');
    setPhoneError('');

    try {
      const requestBody: any = {
        code,
        login: email,
      };
      if (phoneNumber) {
        // Format phone number with country code like in RegisterProfileScreen
        const formattedPhone = selectedCountry?.callingCode
          ? selectedCountry.callingCode + phoneNumber.replace(/\s+/g, "")
          : phoneNumber;
        requestBody.phone_number = formattedPhone;
      }
      console.log(requestBody);
      const response = await httpClient.post<AuthResponse>('/users/guest/authenticate', requestBody);

      if (response.code === 200) {
        storage.set(AppConstants.USER_CODE_KEY, code.toString());
        setUserCode(code.toString());

        if (Platform.OS === 'ios') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } else {
          Vibration.vibrate(100);
        }

        const login = response?.data?.login;
        if (login) {
          navigation.navigate('OTP', { login });
        } else {
          throw new Error('Login parameter is missing from response');
        }
      } else {
        throw new Error(response?.data?.messages?.[0]?.messageContent || 'Authentication failed');
      }
    } catch (err) {
      const error = err as AuthError;
      setCode('');
      setEmail('');
      setPhoneNumber('');
      setError(error?.response?.data?.messages?.[0]?.messageContent || t('auth.guest.login.error.invalid'));
      shakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Add keyboard dismiss handler
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior='padding'
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0} // Increased offset for iOS
    >
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <BackButton />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={styles.dismissKeyboardArea}
          onPress={dismissKeyboard}
        >
          <Animated.View
            style={[
              styles.contentContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }, { translateX: errorShakeAnim }]
              }
            ]}
          >
        {success ? (
          <View style={styles.successContainer}>
            <LottieView
              ref={successAnimation}
              source={require('../../assets/animations/success-animation.json')}
              autoPlay
              loop={false}
              style={styles.successAnimation}
            />
            <Text style={styles.successText}>{t('auth.guest.login.success')}</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={styles.title}>{t('auth.guest.login.title')}</Text>
              <Text style={styles.message}>{t('auth.guest.login.message')}</Text>
            </View>

            <View style={styles.inputContainer}>
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => {
                  // Toggle keyboard visibility
                  if (pinInputRef.current) {
                    // Focus the input to show keyboard
                    pinInputRef.current.focus();
                  }
                }}
                style={styles.otpTouchable}
              >
                <SmoothPinCodeInput
                  ref={pinInputRef}
                  cellStyle={styles.otpCell}
                  cellStyleFocused={styles.otpCellFocused}
                  cellStyleFilled={styles.otpCellFilled}
                  textStyle={styles.otpText}
                  textStyleFocused={styles.otpTextFocused}
                  containerStyle={styles.pinContainer}
                  value={code}
                  onTextChange={onCodeChange}
                  codeLength={6}
                  cellSize={50}
                  cellSpacing={10}
                  mask={<View style={styles.mask} />}
                  maskDelay={300}
                  password={false}
                  animated={true}
                  autoFocus={true}
                  keyboardType={'number-pad'}
                  restrictToNumbers={true}
                  editable={true}
                  placeholder=""
                  onFocus={() => {
                    // Handle focus event
                    console.log('OTP input focused');
                  }}
                  onBlur={() => {
                    // Handle blur event
                    console.log('OTP input blurred');
                  }}
                />
              </TouchableOpacity>

              {error ? (
                <Animated.Text style={styles.errorText}>
                  {error}
                </Animated.Text>
              ) : (
                <Text style={styles.helperText}>
                  {t('auth.guest.login.helper', { length: 6 - code.length })}
                </Text>
              )}

              <View style={styles.emailContainer}>
                {/* <Text style={styles.emailLabel}>{t('auth.guest.login.email.label')}</Text> */}
                <TextInput
                  ref={emailInputRef}
                  style={[
                    styles.emailInput,
                    emailError ? styles.emailInputError : null
                  ]}
                  placeholder={t('auth.guest.login.email.placeholder')}
                  value={email}
                  onChangeText={onEmailChange}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="email"
                />
                {emailError ? (
                  <Text style={styles.emailErrorText}>{emailError}</Text>
                ) : null}
                {/* Phone number input below email */}
                <View style={{ marginTop: 16 }}>
                  <PhoneInput
                    value={phoneNumber}
                    phoneInputStyles={{
                      container: {
                        height: 56,
                        borderWidth: 1,
                        borderColor: phoneError ? '#FF3B30' : '#DDDDDD',
                        borderRadius: 12,
                        backgroundColor: phoneError ? '#FFF5F5' : '#F5F5F5',
                      },
                      input: {
                        paddingHorizontal: 16,
                        fontSize: 16,
                        color: '#333',
                        height: 56,
                      }
                    }}
                    selectedCountry={selectedCountry}
                    onChangePhoneNumber={onPhoneChange}
                    onChangeSelectedCountry={handleSelectedCountry}
                    placeholder={t('auth.guest.login.phone.placeholder', 'Phone number (optional)')}
                  />
                  {phoneError ? (
                    <Text style={styles.emailErrorText}>{phoneError}</Text>
                  ) : null}
                </View>
              </View>
            </View>

            <Animated.View
              style={[
                styles.button,
                (code.length === 6 && email && !emailError) ? styles.buttonActive : styles.buttonInactive,
                { transform: [{ scale: buttonScale }] }
              ]}
            >
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={isLoading || code.length !== 6 || !email || !!emailError}
                activeOpacity={0.8}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}
              >
                {isLoading ? (
                  <ActivityIndicator color="#ffffff" size="small" />
                ) : (
                  <Text style={styles.buttonText}>
                    {t('auth.guest.login.button')}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>
          </>
        )}
          </Animated.View>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    height: 'auto',
    justifyContent: 'center',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 120 : 80, // Extra padding at the bottom for iOS
  },
  dismissKeyboardArea: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  headerContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  inputContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: 40,
  },
  pinContainer: {
    width: '100%',
    justifyContent: 'center',
    marginBottom: 16,
  },
  otpTouchable: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  otpCell: {
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
  },
  otpCellFocused: {
    borderColor: '#3498DB',
    borderWidth: 2,
    backgroundColor: '#F0F5FF',
    transform: [{scale: 1.05}],
  },
  otpCellFilled: {
    borderColor: '#3498DB',
    borderWidth: 1.5,
    backgroundColor: 'rgba(52, 152, 219, 0.05)',
  },
  otpText: {
    fontSize: 24,
    color: '#333333',
    fontWeight: '600',
  },
  otpTextFocused: {
    fontSize: 24,
    color: '#3498DB',
    fontWeight: '700',
  },
  mask: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#333',
  },
  emailContainer: {
    width: '100%',
    marginTop: 24,
  },
  emailLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  emailInput: {
    width: '100%',
    height: 56,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#333',
  },
  emailInputError: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5',
  },
  emailErrorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginTop: 8,
    fontWeight: '500',
  },
  button: {
    width: width * 0.85,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#3498DB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    marginTop: Platform.OS === 'ios' ? 20 : 0, // Added top margin for iOS
    marginBottom: Platform.OS === 'ios' ? 20 : 0, // Added bottom margin for iOS
    paddingHorizontal: 20, // Add padding to increase touchable area
    minWidth: 200, // Ensure minimum width
  },
  buttonActive: {
    backgroundColor: '#3498DB',
  },
  buttonInactive: {
    backgroundColor: '#3498DB',
    opacity: 0.5,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginTop: 8,
    fontWeight: '500',
  },
  helperText: {
    color: '#888',
    fontSize: 14,
    marginTop: 8,
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successAnimation: {
    width: 200,
    height: 200,
  },
  successText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4CAF50',
    marginTop: 16,
  }
});

export default AuthenticationScreen;