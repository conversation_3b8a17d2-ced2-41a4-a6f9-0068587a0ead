import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { useQueryClient, useMutation, useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import * as ImagePicker from 'expo-image-picker';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { FontAwesome, Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '@/stores/useAuthStore';
import CountryPicker, { Country } from 'react-native-country-picker-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface ProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  avatar?: string;
  phoneNumber?: string;
}

const EditProfileScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const queryClient = useQueryClient();
  const insets = useSafeAreaInsets();
  const [formData, setFormData] = useState<ProfileData>({});
  const [countryPickerVisible, setCountryPickerVisible] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [isImageUploading, setIsImageUploading] = useState(false);

  // Fetch current user data
  const { data: userData, isLoading } = useQuery({
    queryKey: ['userProfile'],
    queryFn: async () => {
      const response = await httpClient.get('/users/authenticate/info');
      console.log('userData', response.data);
      return response.data;
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileData) => {
      const response = await httpClient.post('/users/updateprofile', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      navigation.goBack();
    },
  });

  useEffect(() => {
    if (userData) {
      setFormData({
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        address: userData.address,
        city: userData.city,
        country: userData.country,
        avatar: userData.avatar,
        phoneNumber: userData.phoneNumber,
      });
      if (userData.country) {
        setSelectedCountry({
          cca2: userData.country,
          name: userData.country,
          callingCode: [''],
          region: '',
          subregion: '',
          currency: [],
          flag: '',
        });
      }
    }
  }, [userData]);

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        setIsImageUploading(true);
        // Here you would typically upload the image to your server
        // and get back the URL. For now, we'll just update the local state
        setFormData(prev => ({ ...prev, avatar: result.assets[0].uri }));
        setIsImageUploading(false);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setIsImageUploading(false);
    }
  };

  const handleSubmit = () => {
    // Only send changed fields
    const changedFields: ProfileData = {};
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== userData[key]) {
        changedFields[key as keyof ProfileData] = value;
      }
    });
    console.log('changedFields', changedFields);

    if (Object.keys(changedFields).length > 0) {
      updateProfileMutation.mutate(changedFields);
    } else {
      navigation.goBack();
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { paddingTop: insets.top }]}
      contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.form}>
        <Input
          label={t('common.profile.email')}
          value={formData.email}
          editable={false}
          placeholder={t('common.profile.email_placeholder')}
          keyboardType="email-address"
          autoCapitalize="none"
          style={styles.disabledInput}
        />

        <Input
          label={t('common.profile.firstName')}
          value={formData.firstName}
          onChangeText={(text) => setFormData(prev => ({ ...prev, firstName: text }))}
          placeholder={t('common.profile.firstName_placeholder')}
        />

        <Input
          label={t('common.profile.lastName')}
          value={formData.lastName}
          onChangeText={(text) => setFormData(prev => ({ ...prev, lastName: text }))}
          placeholder={t('common.profile.lastName_placeholder')}
        />

        <Input
          label={t('common.profile.address')}
          value={formData.address}
          onChangeText={(text) => setFormData(prev => ({ ...prev, address: text }))}
          placeholder={t('common.profile.address_placeholder')}
        />

        <Input
          label={t('common.profile.city')}
          value={formData.city}
          onChangeText={(text) => setFormData(prev => ({ ...prev, city: text }))}
          placeholder={t('common.profile.city_placeholder')}
        />

        <Input
          label={t('common.profile.phoneNumber')}
          value={formData.phoneNumber}
          onChangeText={(text) => setFormData(prev => ({ ...prev, phoneNumber: text }))}
          placeholder={t('common.profile.phoneNumber_placeholder')}
          keyboardType="phone-pad"
        />

        <TouchableOpacity
          style={styles.countryPicker}
          onPress={() => setCountryPickerVisible(true)}
        >
          <Text style={styles.label}>{t('common.profile.country')}</Text>
          <View style={styles.countryContainer}>
            <CountryPicker
              visible={countryPickerVisible}
              onClose={() => setCountryPickerVisible(false)}
              onSelect={(country) => {
                setSelectedCountry(country);
                setFormData(prev => ({ ...prev, country: country.cca2 }));
                setCountryPickerVisible(false);
              }}
              countryCode={selectedCountry?.cca2}
              withFilter
              withFlag
              withCountryNameButton
              withAlphaFilter
              placeholder={t('common.profile.select_country')}
            />
            <Ionicons name="chevron-down" size={20} color="#666" />
          </View>
        </TouchableOpacity>

        <Button
          title={t('common.buttons.save')}
          onPress={handleSubmit}
          loading={updateProfileMutation.isPending}
          style={styles.submitButton}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    padding: 20,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#0000ff',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  form: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  countryPicker: {
    marginBottom: 20,
  },
  countryContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  countryText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  submitButton: {
    marginTop: 20,
  },
  disabledInput: {
    opacity: 0.7,
    backgroundColor: '#f5f5f5',
  },
});

export default EditProfileScreen; 