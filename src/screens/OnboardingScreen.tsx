import { View, Text, StyleSheet, Dimensions, TouchableOpacity, Button } from 'react-native'
import React, { useState } from 'react'
// @ts-ignore
import Onboarding from 'react-native-onboarding-swiper';
import LottieView from 'lottie-react-native';
import { useNavigation } from '@react-navigation/native';
import LanguageSelector from '@/components/shared/LanguageSelector';
import { useTranslation } from 'react-i18next';
import { completeOnboarding } from '@/utils/util';
import { useAuthStore } from '@/stores/useAuthStore';

const { width, height } = Dimensions.get('window');

type RoleType = 'GUEST' | 'HOST' | 'PRO';

const OnboardingScreen = () => {
  const navigation = useNavigation();
  const { setOnboardingCompleted, role } = useAuthStore();
  const { t } = useTranslation();
  console.log('Role: ', role);

  const pages = {
    "GUEST": [
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.guest.slide1.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/flags.json')} 
                autoPlay 
                loop 
                style={styles.lottie} 
              />,
        subtitle: (
          <View>
            <Text style={styles.subtitle}>{t('onboarding.guest.slide1.message')}</Text>
            <LanguageSelector />
          </View>
        ),
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.guest.slide2.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/house.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.guest.slide2.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.guest.slide3.title')}</Text>,
        title: <LottieView
                source={require('../../assets/animations/map.json')}
                autoPlay
                loop
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.guest.slide3.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.guest.slide4.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/chat.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.guest.slide4.message')}</Text>,
      },
    ],
    "HOST": [
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.host.slide1.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/flags.json')} 
                autoPlay 
                loop 
                style={styles.lottie} 
              />,
        subtitle: (
          <View>
            <Text style={styles.subtitle}>{t('onboarding.host.slide1.message')}</Text>
            <LanguageSelector />
          </View>
        ),
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.host.slide2.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/house_rental.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.host.slide2.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.host.slide3.title')}</Text>,
        title: <LottieView
                source={require('../../assets/animations/handyman.json')}
                autoPlay
                loop
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.host.slide3.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.host.slide4.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/chat.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.host.slide4.message')}</Text>,
      },
    ],
    "PRO": [
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.pro.slide1.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/flags.json')} 
                autoPlay 
                loop 
                style={styles.lottie} 
              />,
        subtitle: (
          <View>
            <Text style={styles.subtitle}>{t('onboarding.pro.slide1.message')}</Text>
            <LanguageSelector />
          </View>
        ),
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.pro.slide2.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/handyman.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.pro.slide2.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.pro.slide3.title')}</Text>,
        title: <LottieView
                source={require('../../assets/animations/contact.json')}
                autoPlay
                loop
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.pro.slide3.message')}</Text>,
      },
      {
        backgroundColor: '#fff',
        image: <Text style={styles.title}>{t('onboarding.pro.slide4.title')}</Text>,
        title: <LottieView 
                source={require('../../assets/animations/chat.json')} 
                autoPlay 
                loop 
                style={styles.lottie}
              />,
        subtitle: <Text style={styles.subtitle}>{t('onboarding.pro.slide4.message')}</Text>,
      },
    ]
  }

  const handleDone = () => {
    console.log('done')
    completeOnboarding();
    setOnboardingCompleted(true);
  }

  const handleSkip = () => {
    console.log('skip')
    setOnboardingCompleted(true);
  }

  const doneButton =({...props}) => {
    console.log('done', props);
    return (
      <TouchableOpacity style={styles.doneButton} onPress={handleDone}>
        <Text style={styles.doneButtonText}>{t('onboarding.buttons.done')}</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <Onboarding
        containerStyles={styles.onboardingContainer}
        titleStyles={styles.title}
        subTitleStyles={styles.subtitle}
        bottomBarHighlight={false}
        showSkip={true}
        skipLabel={t('onboarding.buttons.skip')}
        nextLabel={t('onboarding.buttons.next')}
        DoneButtonComponent={doneButton}
        pages={pages[role as RoleType]}
        onDone={handleDone}
        onSkip={handleSkip}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  onboardingContainer: {
    paddingHorizontal: 20,
  },
  contentContainer: {
    width: width - 40,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: '600',
    color: '#1E2B3B',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#1E2B3B',
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 24,
    marginBottom: 16,
  },
  lottie: {
    width: width,
    height: width,
    resizeMode: 'stretch',
    marginBottom: 36,
  },
  lottieContainer: {
    width: width * 0.9,
    height: width * 0.9,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    overflow: 'hidden',
  },
  doneButton: {
    padding: 15,
    backgroundColor: '#4682B4',
    borderTopLeftRadius: 100,
    borderBottomLeftRadius: 100,
  },
  doneButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  }
})

export default OnboardingScreen