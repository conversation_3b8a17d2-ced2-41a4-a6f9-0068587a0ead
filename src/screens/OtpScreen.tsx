import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Vibration,
  ScrollView,
  Keyboard
} from 'react-native';
// @ts-ignore
import SmoothPinCodeInput from 'react-native-smooth-pincode-input';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/types/navigation';
import { useTranslation } from 'react-i18next';
import { checkOnboardingStatus } from '@/utils/util';
import { useAuthStore } from '@/stores/useAuthStore';
import LottieView from 'lottie-react-native';
import * as Haptics from 'expo-haptics';
import { AppConstants } from '@/constants/default';
import { storage } from '@/utils/storage';
import { httpClient } from '@/utils/http';
import BackButton from '@/components/BackButton';
const { width } = Dimensions.get('window');

type OtpScreenProps = NativeStackScreenProps<RootStackParamList, 'OTP'>;

interface OtpScreenParams {
  login: string;
}

const OtpScreen: React.FC<OtpScreenProps> = ({ route, navigation }) => {
  const [isFirstLaunch, setIsFirstLaunch] = useState(false);
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const { t } = useTranslation();
  const { userContact, setToken } = useAuthStore();
  const login = route.params ? (route.params as OtpScreenParams).login : '';
  const successAnimation = useRef(null);
  const pinInputRef = useRef<any>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const errorShakeAnim = useRef(new Animated.Value(0)).current;

  // Shake animation on error
  const shakeAnimation = () => {
    Animated.sequence([
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();

    // Trigger vibration for haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } else {
      Vibration.vibrate(400);
    }
  };

  // Button press animation
  const animateButtonPress = () => {
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Light haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    checkFirstLaunch();
  }, []);

  const checkFirstLaunch = () => {
    const hasCompletedOnboarding = checkOnboardingStatus();
    console.log('hasCompletedOnboarding:', hasCompletedOnboarding);
    setIsFirstLaunch(!hasCompletedOnboarding);
  };

  const handleOtpChange = (value: string) => {
    setOtp(value);
    if (error) setError('');

    // Subtle haptic feedback when typing
    if (Platform.OS === 'ios' && value.length > 0) {
      Haptics.selectionAsync();
    }
  };

  const handleSubmit = async () => {
    if (otp.length < 6) {
      setError(t('auth.otp.error_incomplete'));
      shakeAnimation();
      return;
    }

    animateButtonPress();
    setIsLoading(true);
    setError('');

    try {
      const body = {
        "login": `${login}`,
        "code": `${otp}`
      };

      const result: any = await httpClient.post('/users/authenticate/verify', body);

      if (result.code === 200) {
        // Store token and update auth state
        storage.set(AppConstants.TOKEN_KEY, result.data.token);
        setToken(result.data.token);

        // Show success animation
        setSuccess(true);

        // Trigger success haptic
        if (Platform.OS === 'ios') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } else {
          Vibration.vibrate(100);
        }

        // Small delay to show success animation before navigation
        setTimeout(() => {
          navigation.navigate(isFirstLaunch ? 'Onboarding' as never : 'UserTabs' as never);
        }, 1500);
      } else {
        throw new Error('Verification failed');
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      setError(t('auth.otp.error_invalid'));
      shakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Add keyboard dismiss handler
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior='padding'
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0} // Increased offset for iOS
    >

      <BackButton color="#DEC48F" />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={styles.dismissKeyboardArea}
          onPress={dismissKeyboard}
        >
          <Animated.View
            style={[
              styles.contentContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }, { translateX: errorShakeAnim }]
              }
            ]}
          >
        {success ? (
          <View style={styles.successContainer}>
            <LottieView
              ref={successAnimation}
              source={require('../../assets/animations/success-animation.json')}
              autoPlay
              loop={false}
              style={styles.successAnimation}
            />
            <Text style={styles.successText}>{t('auth.otp.success')}</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={styles.title}>{t('auth.otp.message')}</Text>
              <Text style={styles.contactText}>
                {userContact ? `${t('auth.otp.sent_to')} ${userContact}` : ''}
              </Text>
            </View>

            <View style={styles.inputContainer}>
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => {
                  // Toggle keyboard visibility
                  if (pinInputRef.current) {
                    // Focus the input to show keyboard
                    pinInputRef.current.focus();
                  }
                }}
                style={styles.otpTouchable}
              >
                <SmoothPinCodeInput
                  ref={pinInputRef}
                  cellStyle={styles.otpCell}
                  cellStyleFocused={styles.otpCellFocused}
                  cellStyleFilled={styles.otpCellFilled}
                  textStyle={styles.otpText}
                  textStyleFocused={styles.otpTextFocused}
                  containerStyle={styles.pinContainer}
                  value={otp}
                  onTextChange={handleOtpChange}
                  codeLength={6}
                  cellSize={60}
                  cellSpacing={8}
                  animated={true}
                  password={false}
                  autoFocus={true}
                  editable={true}
                  keyboardType="number-pad"
                  restrictToNumbers={true}
                  placeholder=""
                  maskDelay={0}
                  onFocus={() => {
                    // Handle focus event
                    console.log('OTP input focused');
                  }}
                  onBlur={() => {
                    // Handle blur event
                    console.log('OTP input blurred');
                  }}
                />
              </TouchableOpacity>

              {error ? (
                <Animated.Text style={styles.errorText}>
                  {error}
                </Animated.Text>
              ) : (
                <Text style={styles.helperText}>
                  {t('auth.otp.helper', { length: 6 - otp.length })}
                </Text>
              )}
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                  style={[
                    styles.button,
                    otp.length === 6 ? styles.buttonActive : styles.buttonInactive,
                    { transform: [{ scale: buttonScale._value }] }
                  ]}
                  onPress={handleSubmit}
                  disabled={isLoading || otp.length < 6}
                  activeOpacity={0.8}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFFFFF" size="small" />
                  ) : (
                    <Text style={styles.buttonText}>{t('auth.otp.button')}</Text>
                  )}
                </TouchableOpacity>

              <TouchableOpacity
                style={styles.resendButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
              >
                <Text style={styles.resendButtonText}>{t('auth.otp.resend')}</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
          </Animated.View>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C3E50',
    justifyContent: 'flex-start',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 120 : 80, // Extra padding at the bottom for iOS
  },
  dismissKeyboardArea: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  headerContainer: {
    width: '100%',
    alignItems: 'center',
    marginTop: 60,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#DEC48F',
    marginBottom: 12,
    textAlign: 'center',
  },
  contactText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
    textAlign: 'center',
    marginTop: 8,
  },
  inputContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: 40,
  },
  pinContainer: {
    width: '100%',
    justifyContent: 'center',
    marginBottom: 16,
  },
  otpTouchable: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  otpCell: {
    width: 50,
    height: 50,
    borderWidth: 2,
    borderColor: 'rgba(52, 152, 219, 0.5)',
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  otpCellFocused: {
    borderColor: '#D1987C',
    backgroundColor: 'rgba(209, 152, 124, 0.2)',
    transform: [{scale: 1.05}],
  },
  otpCellFilled: {
    borderColor: '#D1987C',
    backgroundColor: 'rgba(209, 152, 124, 0.15)',
  },
  otpText: {
    color: '#FFFFFF',
    fontSize: 26,
    fontWeight: '700',
  },
  otpTextFocused: {
    color: '#FFFFFF',
    fontSize: 26,
    fontWeight: '700',
    textShadowColor: 'rgba(255,255,255,0.5)',
    textShadowOffset: {width: 0, height: 0},
    textShadowRadius: 10,
  },
  errorText: {
    color: '#E74C3C',
    fontSize: 14,
    marginTop: 12,
    fontWeight: '500',
  },
  helperText: {
    color: '#FFFFFF',
    opacity: 0.7,
    fontSize: 14,
    marginTop: 12,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: Platform.OS === 'ios' ? 60 : 40, // Increased bottom margin for iOS
    paddingTop: 20, // Added padding at the top
  },
  button: {
    width: width * 0.85,
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    marginBottom: 16,
    paddingHorizontal: 20, // Add padding to increase touchable area
    minWidth: 200, // Ensure minimum width
  },
  buttonActive: {
    backgroundColor: '#D1987C',
  },
  buttonInactive: {
    backgroundColor: 'rgba(209, 152, 124, 0.5)',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  resendButton: {
    padding: 8,
  },
  resendButtonText: {
    color: '#3498DB',
    fontSize: 14,
    fontWeight: '600',
  },
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successAnimation: {
    width: 200,
    height: 200,
  },
  successText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2ECC71',
    marginTop: 16,
    textAlign: 'center',
  }
});

export default OtpScreen;