import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
  ActivityIndicator,
  KeyboardAvoidingView,
  StatusBar,
  Dimensions,
  ScrollView,
  Keyboard
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import SmoothPinCodeInput from 'react-native-smooth-pincode-input';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSequence,
  withTiming,
  FadeIn,
  FadeOut,
  SlideInUp
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';

// Import your types and utilities
import { RootStackParamList } from '@/types/navigation';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { storage } from '@/utils/storage';
import { AppConstants } from '@/constants/default';
import BackButton from '@/components/BackButton';

const { width, height } = Dimensions.get('window');

// Create animated components
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

type OtpScreenProps = NativeStackScreenProps<RootStackParamList, 'ProOTP'>;

interface AuthResponse {
  code: number;
  data: {
    token: string;
  };
}

interface UserInfoResponse {
  code: number;
  data: {
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    phoneNumber: string | null;
    profile: {
      birthDate: string | null;
    };
  };
}

const ProOtpScreen: React.FC<OtpScreenProps> = () => {
  // Hooks
  const navigation = useNavigation<any>();
  const { t } = useTranslation();
  const { userContact, setToken } = useAuthStore();
  const pinInputRef = useRef<any>(null);
  const insets = useSafeAreaInsets();

  // State
  const [otp, setOtp] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // Animation values
  const shake = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const contentOpacity = useSharedValue(1);
  const iconScale = useSharedValue(1);

  // Animated styles
  const shakeStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shake.value }]
    };
  });

  const buttonStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }],
      backgroundColor: otp.length === 6 ? '#D1987C' : '#E5E5E5',
    };
  });

  const containerStyle = useAnimatedStyle(() => {
    return {
      opacity: contentOpacity.value,
    };
  });

  // Initialize component with animation
  useEffect(() => {
    // Animate icon on mount
    iconScale.value = withSequence(
      withTiming(1.2, { duration: 300 }),
      withTiming(1, { duration: 200 })
    );

    // Ensure the component is mounted before focusing input
    const timer = setTimeout(() => {
      if (pinInputRef.current) {
        pinInputRef.current.focus();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Trigger shake animation for error feedback
  const triggerShake = useCallback(() => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    shake.value = withSequence(
      withTiming(-10, { duration: 50 }),
      withTiming(10, { duration: 50 }),
      withTiming(-8, { duration: 50 }),
      withTiming(8, { duration: 50 }),
      withTiming(-4, { duration: 50 }),
      withTiming(4, { duration: 50 }),
      withTiming(0, { duration: 50 })
    );
  }, []);

  // Success feedback
  const triggerSuccess = useCallback(() => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    iconScale.value = withSequence(
      withTiming(1.2, { duration: 200 }),
      withTiming(1, { duration: 200 })
    );
  }, []);

  // Handle OTP verification
  const handleVerifyOtp = useCallback(async () => {
    if (otp.length !== 6 || isSubmitting) {
      return;
    }

    // Animate button press
    buttonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    // Light haptic feedback on button press
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setIsSubmitting(true);
    setError('');

    try {
      const body = {
        login: userContact,
        code: otp
      };

      const result = await httpClient.post<AuthResponse>('/users/authenticate/verify', body);

      if (result.code === 200 && result.data.token) {
        // Store token in secure storage
        storage.set(AppConstants.TOKEN_KEY, result.data.token);

        // Show success animation
        triggerSuccess();

        try {
          // Get user info
          const userInfo = await httpClient.get<UserInfoResponse>('users/authenticate/info');

          if (userInfo.data) {
            const userData = userInfo.data;
            const isProfileComplete = userData.firstName &&
                                     userData.lastName &&
                                     userData.profile.birthDate &&
                                     userData.email;

            if (!isProfileComplete) {
              // Navigate to profile completion screen
              navigation.navigate('RegisterProfile', { userInfo: userData });
            } else {
              // Set token in auth store and continue to main app
              setToken(result.data.token);
            }
          }
        } catch (error) {
          console.error("Error fetching user info:", error);
          setError(t('Failed to authenticate. Please try again.'));
          Alert.alert(
            t('Error'),
            t('Failed to authenticate. Please try again.')
          );
          triggerShake();
        }
      } else {
        throw new Error('Invalid response');
      }
    } catch (error) {
      console.error("Verification error:", error);

      // Show error with animation and haptic feedback
      triggerShake();
      setError(t('Invalid verification code. Please check and try again.'));

      Alert.alert(
        t('Error'),
        t('Invalid verification code. Please check and try again.')
      );

      // Clear OTP field on error for better UX
      setOtp('');
      if (pinInputRef.current) {
        pinInputRef.current.focus();
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [otp, isSubmitting, userContact, navigation, t, setToken, triggerShake, triggerSuccess]);

  // Handle OTP complete (when all 6 digits are entered)
  const handleOtpComplete = useCallback((_code: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    handleVerifyOtp();
  }, [handleVerifyOtp]);

  // Add keyboard dismiss handler
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.safeArea}>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 80} // Increased offset for iOS
        enabled
      >
        <BackButton color="#D1987C" />

        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <TouchableOpacity
            activeOpacity={1}
            style={styles.dismissKeyboardArea}
            onPress={dismissKeyboard}
          >
            <Animated.View
              style={[styles.content, containerStyle]}
              entering={FadeIn.duration(500)}
              exiting={FadeOut.duration(300)}
            >
        <Animated.View
          entering={SlideInUp.delay(100).springify()}
          style={[styles.verificationBox, shakeStyle]}
        >
          <Animated.View
            style={[styles.iconContainer, { transform: [{ scale: iconScale.value }] }]}
          >
            <Ionicons name="shield-checkmark" size={50} color="#D1987C" />
          </Animated.View>

          <Text style={styles.verificationText}>
            {t('We sent a verification code to')}
          </Text>
          <Text style={styles.phoneNumber}>
            {userContact}
          </Text>
        </Animated.View>

        <Animated.View
          style={styles.otpContainer}
          entering={SlideInUp.delay(200).springify()}
        >
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => {
              if (pinInputRef.current) {
                pinInputRef.current.focus();
              }
            }}
            style={styles.otpTouchable}
          >
            <SmoothPinCodeInput
              ref={pinInputRef}
              cellStyle={styles.otpCell}
              cellStyleFocused={styles.otpCellFocused}
              textStyle={styles.otpText}
              value={otp}
              onTextChange={setOtp}
              onFulfill={handleOtpComplete}
              keyboardType="number-pad"
              codeLength={6}
              cellSize={45}
              cellSpacing={10}
              autoFocus
              password={false}
              animated={true}
              restrictToNumbers={true}
              editable={true}
              cellStyleFilled={styles.otpCellFilled}
              textStyleFocused={styles.otpTextFocused}
              placeholder=""
              maskDelay={0}
              onBackspace={() => {
                // Handle backspace if needed
              }}
              onFocus={() => {
                // Handle focus event
                console.log('OTP input focused');
              }}
              onBlur={() => {
                // Handle blur event
                console.log('OTP input blurred');
              }}
            />
          </TouchableOpacity>

          {error ? (
            <Animated.Text
              style={styles.errorText}
              entering={FadeIn.duration(300)}
            >
              {error}
            </Animated.Text>
          ) : (
            <Text style={styles.helperText}>
              {otp.length < 6 ?
                t('Enter the 6-digit code') :
                t('Press confirm to verify')}
            </Text>
          )}
        </Animated.View>

        <Animated.View
          entering={SlideInUp.delay(300).springify()}
          style={styles.buttonContainer}
        >
          <TouchableOpacity
            style={[
              styles.confirmButton,
              { backgroundColor: otp.length === 6 ? '#D1987C' : '#E5E5E5' }
            ]}
            onPress={handleVerifyOtp}
            disabled={otp.length !== 6 || isSubmitting}
            activeOpacity={0.8}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            {isSubmitting ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.confirmButtonText}>{t('Confirm')}</Text>
            )}
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={styles.resendButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }}
          >
            <Text style={styles.resendText}>{t('Resend code')}</Text>
          </TouchableOpacity> */}
        </Animated.View>
      </Animated.View>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 120 : 80, // Extra padding at the bottom for iOS
  },
  dismissKeyboardArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'flex-start',
  },
  verificationBox: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: 24,
    marginTop: 20,
    width: '100%',
  },
  iconContainer: {
    width: 90,
    height: 90,
    backgroundColor: 'rgba(209, 152, 124, 0.1)',
    borderRadius: 45,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    overflow: 'hidden',
  },
  verificationText: {
    fontSize: 16,
    color: '#555555',
    marginBottom: 8,
    textAlign: 'center',
  },
  phoneNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
    padding: 24,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    width: '100%',
  },
  otpTouchable: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  otpCell: {
    borderWidth: 1.5,
    borderColor: '#E5E5E5',
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
  },
  otpCellFocused: {
    borderColor: '#D1987C',
    backgroundColor: '#FFFFFF',
    shadowColor: '#D1987C',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  otpCellFilled: {
    borderColor: '#D1987C',
    backgroundColor: 'rgba(209, 152, 124, 0.05)',
  },
  otpText: {
    fontSize: 22,
    color: '#333333',
    fontWeight: '600',
  },
  otpTextFocused: {
    fontSize: 22,
    color: '#D1987C',
    fontWeight: '700',
  },
  errorText: {
    color: '#E74C3C',
    fontSize: 14,
    marginTop: 16,
    textAlign: 'center',
  },
  helperText: {
    color: '#777777',
    fontSize: 14,
    marginTop: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    marginTop: 'auto',
    marginBottom: Platform.OS === 'ios' ? 40 : 20, // Increased bottom margin for iOS
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 24,
    paddingTop: 20, // Added padding at the top
  },
  confirmButton: {
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    flexDirection: 'row',
    paddingHorizontal: 20, // Add padding to increase touchable area
    minWidth: 200, // Ensure minimum width
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  resendButton: {
    marginTop: 16,
    padding: 8,
  },
  resendText: {
    color: '#D1987C',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default ProOtpScreen;
