import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Switch,
  ScrollView,
  Linking,
  Alert,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import i18n from '@/i18n';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  interpolate,
  Extrapolate,
  FadeInDown,
} from 'react-native-reanimated';
import { Feather, FontAwesome5 } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import LanguageSelector from '@/components/shared/LanguageSelector';

import { useAuthStore } from '@/stores/useAuthStore';
import { httpClient } from '@/utils/http';

interface UserInfo {
  displayName?: string;
  email?: string;
}

const UserMenuScreen: React.FC = () => {
  const { t } = useTranslation();
  const { setToken } = useAuthStore();
  const insets = useSafeAreaInsets();
  const [darkMode, setDarkMode] = useState(false);
  const scrollY = useSharedValue(0);
  const headerHeight = useSharedValue(220);
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const menuItemsOpacity = useRef(new Array(7).fill(0).map(() => useSharedValue(0))).current;
  const navigation = useNavigation();

  useEffect(() => {
    menuItemsOpacity.forEach((opacity, index) => {
      opacity.value = withDelay(300 + index * 100, withTiming(1, { duration: 500 }));
    });
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, []);

  const getUserInfo = async () => {
    try {
      const info: any = await httpClient.get('users/authenticate/info');
      setUserInfo(info.data);
    } catch (error) {
      console.error('Failed to fetch user info:', error);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      getUserInfo();
    }, [])
  );

  const toggleDarkMode = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setDarkMode(!darkMode);
    menuItemsOpacity.forEach((opacity) => {
      opacity.value = withTiming(1);
    });
  };

  // const toggleOnboarding = () => {
  //   Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  //   //setOnboardingCompleted(!onboardingCompleted);
  //   storage.set(AppConstants.ONBOARDING_COMPLETED_KEY, !onboarding);
  //   setOnboarding(!onboarding);
  // };

  const handleLogout = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setToken('');
  };

  const animatedHeaderStyle = useAnimatedStyle(() => {
    return {
      height: interpolate(
        scrollY.value,
        [0, 100],
        [headerHeight.value, 120],
        Extrapolate.CLAMP
      ),
    };
  });

  const animatedAvatarStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      scrollY.value,
      [0, 100],
      [1, 0.8],
      Extrapolate.CLAMP
    );
    return {
      transform: [{ scale }],
    };
  });

  const animatedNameStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [0, 50],
      [1, 0.7],
      Extrapolate.CLAMP
    );
    return {
      opacity,
    };
  });

  const handleMenuItemPress = (index: number) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    menuItemsOpacity[index].value = withTiming(0.8, { duration: 100 }, () => {
      menuItemsOpacity[index].value = withTiming(1, { duration: 150 });
    });
  };

  const getMenuItemStyle = (index: number) => {
    return useAnimatedStyle(() => {
      return {
        opacity: menuItemsOpacity[index].value,
        backgroundColor: darkMode ? '#34495E' : '#FFFFFF',
      };
    });
  };

  const showLanguageSelector = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const handleEditProfile = () => {
    console.log('handleEditProfile');
    navigation.navigate('EditProfile' as never);
  };

  const handleHelpAndSupport = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const email = '<EMAIL>';

    // Get current language
    const currentLanguage = i18n.language || 'en';

    // Create localized subject
    const subject = encodeURIComponent(
      currentLanguage === 'fr'
        ? 'Demande d\'aide et support'
        : 'Help and Support Request'
    );

    // Create localized email body based on current language
    let emailBody = '';

    if (currentLanguage === 'fr') {
      // French template
      emailBody = `
      Bonjour l'équipe Wodaabe Stays,

      J'ai besoin d'aide concernant :

      [Veuillez décrire votre problème ici]

      Informations utilisateur :
      Nom : ${userInfo?.displayName || ''}
      Email : ${userInfo?.email || ''}

      Version de l'application : 1.0.0
      Appareil : ${Platform.OS} ${Platform.Version}

      Merci,
      ${userInfo?.displayName || 'Un utilisateur Wodaabe Stays'}
      `;
    } else {
      // English template (default)
      emailBody = `
      Hello Wodaabe Stays Team,

      I need assistance with the following:

      [Please describe your issue here]

      User Info:
      Name: ${userInfo?.displayName || ''}
      Email: ${userInfo?.email || ''}

      App Version: 1.0.0
      Device: ${Platform.OS} ${Platform.Version}

      Thank you,
      ${userInfo?.displayName || 'A Wodaabe Stays User'}
      `;
    }

    const body = encodeURIComponent(emailBody);
    const url = `mailto:${email}?subject=${subject}&body=${body}`;

    return Linking.openURL(url);
  };

  return (
    <View style={[styles.container, darkMode && styles.darkContainer]}>
      <Animated.View style={[styles.header, animatedHeaderStyle, darkMode && styles.darkHeader, { paddingTop: insets.top + 30, paddingBottom: 30 }]}>
        <View style={styles.headerContent}>
          <Animated.View style={[styles.avatarContainer, animatedAvatarStyle]}>
            <View style={styles.avatarIconContainer}>
              <FontAwesome5 name="user-circle" size={65} color="#FFFFFF" />
            </View>
          </Animated.View>
          <View style={styles.userInfoContainer}>
            <Animated.Text style={[styles.userName, animatedNameStyle, darkMode && styles.darkText]}>
              {userInfo?.displayName || ''}
            </Animated.Text>
            <Text style={[styles.userEmail, darkMode && styles.darkSubtext]}>
              {userInfo?.email || ''}
            </Text>
          </View>
        </View>
      </Animated.View>

      <Animated.ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        onScroll={(event) => {
          scrollY.value = event.nativeEvent.contentOffset.y;
        }}
        scrollEventThrottle={16}
      >
        <Text style={[styles.sectionTitle, darkMode && styles.darkText]}>{t('settings.account_settings')}</Text>

        <Animated.View entering={FadeInDown.delay(300).duration(400)}>
          <Animated.View style={[styles.menuItem, getMenuItemStyle(0)]}>
            <TouchableOpacity
              style={styles.menuItemContent}
              onPress={handleEditProfile}
            >
              <View style={styles.menuItemLeft}>
                <Feather name="user" size={22} color={darkMode ? '#DEC48F' : '#3498DB'} />
                <Text style={[styles.menuItemText, darkMode && styles.darkText]}>
                  {t('common.settings.menu_items.edit_profile')}
                </Text>
              </View>
              <Feather name="chevron-right" size={22} color={darkMode ? '#DEC48F' : '#3498DB'} />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        <Animated.View entering={FadeInDown.delay(400).duration(400)}>
          <Animated.View style={[styles.menuItem, getMenuItemStyle(1)]}>
            <TouchableOpacity
              style={styles.menuItemContent}
              onPress={showLanguageSelector}
            >
              <View style={styles.menuItemLeft}>
                <Feather name="globe" size={22} color={darkMode ? '#DEC48F' : '#3498DB'} />
                <Text style={[styles.menuItemText, darkMode && styles.darkText]}>{t('settings.language')}</Text>
              </View>
              <LanguageSelector />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        <Text style={[styles.sectionTitle, darkMode && styles.darkText]}>{t('settings.preferences')}</Text>

        <Animated.View entering={FadeInDown.delay(500).duration(400)}>
          <Animated.View style={[styles.menuItem, getMenuItemStyle(2)]}>
            <View style={styles.menuItemContent}>
              <View style={styles.menuItemLeft}>
                <Feather name="moon" size={22} color={darkMode ? '#DEC48F' : '#3498DB'} />
                <Text style={[styles.menuItemText, darkMode && styles.darkText]}>{t('settings.dark_mode')}</Text>
              </View>
              <Switch
                trackColor={{ false: '#E0E0E0', true: '#D1987C' }}
                thumbColor={darkMode ? '#DEC48F' : '#3498DB'}
                ios_backgroundColor="#E0E0E0"
                onValueChange={toggleDarkMode}
                value={darkMode}
              />
            </View>
          </Animated.View>
        </Animated.View>

        <Animated.View entering={FadeInDown.delay(600).duration(400)}>
          <Animated.View style={[styles.menuItem, getMenuItemStyle(5)]}>
            <TouchableOpacity
              style={styles.menuItemContent}
              onPress={() => {
                handleMenuItemPress(5);
                handleHelpAndSupport();
              }}
            >
              <View style={styles.menuItemLeft}>
                <Feather name="help-circle" size={22} color={darkMode ? '#DEC48F' : '#3498DB'} />
                <Text style={[styles.menuItemText, darkMode && styles.darkText]}>{t('settings.help_and_support')}</Text>
              </View>
              <Feather name="chevron-right" size={20} color={darkMode ? '#DEC48F' : '#3498DB'} />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        <View style={styles.logoutContainer}>
          <TouchableOpacity
            style={[styles.logoutButton, darkMode && styles.darkLogoutButton]}
            onPress={handleLogout}
          >
            <Feather name="log-out" size={20} color="#FFF" style={styles.logoutIcon} />
            <Text style={styles.logoutText}>{t('settings.log_out')}</Text>
          </TouchableOpacity>
        </View>
      </Animated.ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  darkContainer: {
    backgroundColor: '#1A2530',
  },
  header: {
    height: 220,
    backgroundColor: '#3498DB',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  darkHeader: {
    backgroundColor: '#2C3E50',
  },
  headerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  avatarContainer: {
    marginBottom: 15,
  },
  userInfoContainer: {
    alignItems: 'center',
    width: '100%',
  },
  avatarIconContainer: {
    width: 90,
    height: 90,
    borderRadius: 45,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 25,
    marginBottom: 10,
    color: '#2C3E50',
  },
  menuItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    minHeight: 60,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    color: '#2C3E50',
    marginLeft: 15,
  },
  darkText: {
    color: '#F8F8F8',
  },
  darkSubtext: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  logoutContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  logoutButton: {
    backgroundColor: '#D1987C',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
  },
  darkLogoutButton: {
    backgroundColor: '#D1987C',
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UserMenuScreen;