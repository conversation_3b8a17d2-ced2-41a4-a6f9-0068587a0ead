import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  View,
} from 'react-native';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { MaterialIcons } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import { AppConstants } from '@/constants/default';
import { httpClient } from '@/utils/http';
import { uploadFile } from '@/utils/util';
import { useTranslation } from 'react-i18next';
import CollapsibleSection from '@/components/CollapsibleSection';
import GeneralInformationSection from '@/components/GeneralInformationSection';
import PhotoSection from '@/components/PhotoSection';
import LocationSection from '@/components/LocationSection';
import MediaSection from '@/components/MediaSection';
import AmenitiesSection from '@/components/AmenitiesSection';
import PointsOfInterestSection from '@/components/PointsOfInterestSection';
import FAQSection from '@/components/FAQSection';

// Define interfaces for better type safety
interface Media {
  uri: string;
  type: string;
  description: string;
  amenityTag: string | null;
  tutorial: boolean;
}

interface PointOfInterest {
  name: string;
  placeId: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface FAQ {
  uuid: string;
  question: string;
  answer: string;
  isNew?: boolean;
}

interface FormData {
  title: string;
  description: string;
  capacity: string;
  address: string;
  coordinates: {
    latitude: string;
    longitude: string;
  };
  mainPhoto: string | null;
  media: Media[];
  mediaDescription: string;
  pointsOfInterest: PointOfInterest[];
  faqs: FAQ[];
}

interface POIForm {
  name: string;
  address: string;
  latitude: string;
  longitude: string;
}

interface AmenityItem {
  id: string;
  name: {
    en: string;
    fr: string;
  };
  icon: string;
}

const AccommodationCreationScreen = ({ navigation } : { navigation: NavigationProp<ParamListBase>}) => {
  const { t } = useTranslation();
  const mediaScrollViewRef = React.useRef<ScrollView>(null);

  // State management for form data
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    capacity: '',
    address: '',
    coordinates: {
      latitude: '',
      longitude: ''
    },
    mainPhoto: null,
    media: [],
    mediaDescription: '',
    pointsOfInterest: [],
    faqs: [],
  });
  const [isGeocoding, setIsGeocoding] = useState(false);
  const [isPoiGeocoding, setIsPoiGeocoding] = useState(false);
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [poiForm, setPoiForm] = useState<POIForm>({
    name: '',
    address: '',
    latitude: '',
    longitude: '',
  });

  // Shared value for button animation
  const publishScale = useSharedValue(1);

  // Available amenities
  const availableAmenities: AmenityItem[] = [
    { id: 'wifi', name: { en: 'Wi-Fi', fr: 'Wi-Fi' }, icon: 'wifi' },
    { id: 'parking', name: { en: 'Parking', fr: 'Parking' }, icon: 'local-parking' },
    { id: 'air_conditioning', name: { en: 'Air Conditioning', fr: 'Climatisation' }, icon: 'ac-unit' },
    { id: 'heating', name: { en: 'Heating', fr: 'Chauffage' }, icon: 'whatshot' },
    { id: 'kitchen', name: { en: 'Equipped Kitchen', fr: 'Cuisine équipée' }, icon: 'kitchen' },
    { id: 'tv', name: { en: 'TV', fr: 'Télévision' }, icon: 'tv' },
    { id: 'washing_machine', name: { en: 'Washing Machine', fr: 'Machine à laver' }, icon: 'local-laundry-service' },
    { id: 'dryer', name: { en: 'Dryer', fr: 'Sèche-linge' }, icon: 'dry-cleaning' },
    { id: 'dishwasher', name: { en: 'Dishwasher', fr: 'Lave-vaisselle' }, icon: 'countertops' },
    { id: 'pool', name: { en: 'Pool', fr: 'Piscine' }, icon: 'pool' },
    { id: 'hot_tub', name: { en: 'Hot Tub', fr: 'Jacuzzi' }, icon: 'hot-tub' },
    { id: 'gym', name: { en: 'Gym', fr: 'Salle de sport' }, icon: 'fitness-center' },
    { id: 'workspace', name: { en: 'Workspace', fr: 'Espace de travail' }, icon: 'work' },
    { id: 'balcony', name: { en: 'Balcony / Terrace', fr: 'Balcon / Terrasse' }, icon: 'deck' },
    { id: 'garden', name: { en: 'Garden', fr: 'Jardin' }, icon: 'park' },
    { id: 'fireplace', name: { en: 'Fireplace', fr: 'Cheminée' }, icon: 'fireplace' },
    { id: 'bbq', name: { en: 'BBQ Area', fr: 'Espace barbecue' }, icon: 'outdoor-grill' },
    { id: 'safe', name: { en: 'Safe', fr: 'Coffre-fort' }, icon: 'lock' },
    { id: 'elevator', name: { en: 'Elevator', fr: 'Ascenseur' }, icon: 'elevator' },
    { id: 'pet_friendly', name: { en: 'Pet Friendly', fr: 'Animaux acceptés' }, icon: 'pets' },
    { id: 'leisure_equipment', name: { en: 'Leisure Equipment', fr: 'Équipements de loisirs' }, icon: 'gamepad-variant' },
    { id: 'comfort_equipment', name: { en: 'Comfort Equipment', fr: 'Équipements de confort' }, icon: 'sofa' },
    { id: 'rules', name: { en: 'Rules', fr: 'Règles' }, icon: 'rule' }
  ];

  // Request permissions on mount
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.permissions.location_permission_title'), t('common.permissions.location_permission_message'));
      }
    })();

    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        }
      }
    })();
  }, [t]);

  // Geocode address when user types one (debounced)
  useEffect(() => {
    if (formData.address.length > 3) {
      setIsGeocoding(true);
      const timer = setTimeout(() => {
        Location.geocodeAsync(formData.address)
          .then(results => {
            if (results.length > 0) {
              const { latitude, longitude } = results[0];
              setFormData(prev => ({
                ...prev,
                coordinates: {
                  latitude: latitude.toString(),
                  longitude: longitude.toString()
                }
              }));
            }
            setIsGeocoding(false);
          })
          .catch(() => {
            setIsGeocoding(false);
          });
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [formData.address]);

  const handleAddPoi = () => {
    if (!poiForm.name || !poiForm.address) {
      Alert.alert(t('common.errors.title'), t('common.errors.poi_fields_required'));
      return;
    }

    const newPoi: PointOfInterest = {
      name: poiForm.name,
      placeId: generateUUID(), // or use generateUUID() if you don't have uuid
      address: poiForm.address,
      latitude: parseFloat(poiForm.latitude) || 0,
      longitude: parseFloat(poiForm.longitude) || 0
    };

    setFormData({
      ...formData,
      pointsOfInterest: [...formData.pointsOfInterest, newPoi]
    });

    // Reset form
    setPoiForm({
      name: '',
      address: '',
      latitude: '',
      longitude: '',
    });

    // Success message
    Alert.alert(t('common.success.title'), t('common.success.poi_added'));
  };

  const handleRemovePoi = (index: number) => {
    Alert.alert(
      t('common.alerts.confirm_title'),
      t('common.alerts.confirm_remove_poi'),
      [
        {
          text: t('common.buttons.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.buttons.remove'),
          style: 'destructive',
          onPress: () => {
            const updatedPois = [...formData.pointsOfInterest];
            updatedPois.splice(index, 1);
            setFormData({
              ...formData,
              pointsOfInterest: updatedPois
            });
            // Success message
            Alert.alert(t('common.success.title'), t('common.success.poi_removed'));
          }
        }
      ]
    );
  };

  // Function to get current location
  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          t('common.permissions.location_permission_title'),
          t('common.permissions.location_permission_message')
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      // Update form data with coordinates
      setFormData({
        ...formData,
        coordinates: {
          latitude: latitude.toString(),
          longitude: longitude.toString()
        }
      });

      // Attempt to get address from coordinates (reverse geocoding)
      try {
        const geocode = await Location.reverseGeocodeAsync({
          latitude,
          longitude
        });

        if (geocode.length > 0) {
          const addressObj = geocode[0];
          const address = [
            addressObj.name,
            addressObj.street,
            addressObj.district,
            addressObj.city,
            addressObj.region,
            addressObj.country
          ].filter(Boolean).join(', ');

          setFormData(prev => ({
            ...prev,
            address
          }));
        }
      } catch (error) {
        console.error('Reverse geocoding error:', error);
      }
    } catch (error) {
      console.error('Location error:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.errors.location_error')
      );
    }
  };

  // Function to pick main photo
  const pickMainPhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setFormData({
          ...formData,
          mainPhoto: result.assets[0].uri
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.errors.title'), t('common.errors.image_pick_error'));
    }
  };

  // Function to pick media (images, videos, documents)
  const pickMedia = async (mediaType: string) => {
    try {
      if (mediaType === 'document') {
        const result = await DocumentPicker.getDocumentAsync({
          type: 'application/pdf',
          copyToCacheDirectory: true,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const docUri = result.assets[0].uri;
          const docName = result.assets[0].name || 'Document';

          const newMedia: Media = {
            uri: docUri,
            type: 'document',
            description: docName,
            amenityTag: null,
            tutorial: false
          };

          setFormData({
            ...formData,
            media: [...formData.media, newMedia]
          });

          // Scroll to the end of the media list after adding the new item
          setTimeout(() => {
            mediaScrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }
        return;
      }

      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        return;
      }

      let pickerOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        quality: 0.8,
      };

      // Adjust options based on media type
      if (mediaType === 'image') {
        pickerOptions.mediaTypes = ImagePicker.MediaTypeOptions.Images;
      } else if (mediaType === 'video') {
        pickerOptions.mediaTypes = ImagePicker.MediaTypeOptions.Videos;
      }

      const result = await ImagePicker.launchImageLibraryAsync(pickerOptions);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Add the media to the formData
        const newMedia: Media = {
          uri: result.assets[0].uri,
          type: mediaType,
          description: '',
          amenityTag: null,
          tutorial: false
        };

        setFormData({
          ...formData,
          media: [...formData.media, newMedia]
        });

        // Scroll to the end of the media list after adding the new item
        setTimeout(() => {
          mediaScrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Error picking media:', error);
      Alert.alert(t('common.errors.title'), t('common.errors.media_pick_error'));
    }
  };

  // Remove media from the list
  const removeMedia = (index: number) => {
    const updatedMedia = [...formData.media];
    updatedMedia.splice(index, 1);
    setFormData({
      ...formData,
      media: updatedMedia
    });
  };

  // Animated style for Publish button
  const animatedPublishStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: publishScale.value }]
    };
  });

  const generateUUID = () => Math.random().toString(36).substring(2) + Date.now().toString(36);

  // Function to handle FAQs
  const handleFaqsChange = (faqs: FAQ[]) => {
    setFormData(prev => ({
      ...prev,
      faqs
    }));
  };

  // Function to add FAQs to a housing after creation
  const addFaqsToHousing = async (housingUuid: string, faqs: FAQ[]) => {
    if (!faqs || faqs.length === 0) return;

    try {
      // Create an array of promises for adding each FAQ
      const faqPromises = faqs.map(async (faq) => {
        try {
          const response = await httpClient.post(`/housings/${housingUuid}/faq/add`, {
            question: faq.question,
            answer: faq.answer
          });
          return response;
        } catch (error) {
          console.error(`Error adding FAQ: ${faq.question}`, error);
          return null;
        }
      });

      // Wait for all FAQ additions to complete
      await Promise.all(faqPromises);
      console.log('All FAQs added successfully');
    } catch (error) {
      console.error('Error adding FAQs:', error);
      // We don't want to show an error alert here as the housing was created successfully
      // Just log the error and continue
    }
  };

  // Submit form data
  const handleSubmit = async () => {
    // Check required fields
    if (!formData.title || !formData.description || !formData.mainPhoto) {
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_creation_screen.required_fields')
      );
      return;
    }

    try {
      setIsLoading(true);

      // Upload the main photo
      const mainPhotoLink = await uploadFile(formData.mainPhoto, AppConstants.HOUSING_FIREBASE_FOLDER);

      // Upload all media files concurrently with amenityTag and tutorial state
      const mediaUploadPromises = formData.media.map(async (media) => {
        const url = await uploadFile(media.uri, AppConstants.HOUSING_FIREBASE_FOLDER);
        return {
          url: url,
          type: media.type,
          description: media.description,
          amenityTag: media.amenityTag,
          tutorial: media.tutorial
        };
      });

      // Wait for all media uploads to complete
      const houseMediaLinks = await Promise.all(mediaUploadPromises);

      const body = {
        title: formData.title,
        description: formData.description,
        capacity: parseInt(formData.capacity, 10),
        latitude: parseFloat(formData.coordinates.latitude),
        longitude: parseFloat(formData.coordinates.longitude),
        address: formData.address,
        profilePicture: mainPhotoLink,
        housingMedia: houseMediaLinks,
        pointsOfInterest: formData.pointsOfInterest,
      };

      const response = await httpClient.post('/housings/create', body);

      if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
        // Get the housing UUID from the response
        const housingUuid = response.data && typeof response.data === 'object' ? (response.data as any).uuid : null;

        if (housingUuid && formData.faqs.length > 0) {
          // Add FAQs to the newly created housing
          await addFaqsToHousing(housingUuid, formData.faqs);
        }

        Alert.alert(
          t('common.accommodation_creation_screen.success'),
          t('common.accommodation_creation_screen.success_message')
        );
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.errors.title'),
          t('common.accommodation_creation_screen.error_message')
        );
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_creation_screen.error_message')
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (poiForm.address.length > 3) {
      setIsPoiGeocoding(true);
      const timer = setTimeout(() => {
        Location.geocodeAsync(poiForm.address)
          .then(results => {
            if (results.length > 0) {
              const { latitude, longitude } = results[0];
              setPoiForm(prev => ({
                ...prev,
                latitude: latitude.toString(),
                longitude: longitude.toString()
              }));
            }
            setIsPoiGeocoding(false);
          })
          .catch(() => {
            setIsPoiGeocoding(false);
          });
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [poiForm.address]);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
      ),
      headerTitle: t('common.accommodation_creation_screen.header_title'),
      headerRight: () => (
        <Animated.View style={animatedPublishStyle}>
          <TouchableOpacity
            style={styles.publishButton}
            onPressIn={() => {
              publishScale.value = withSpring(0.95);
            }}
            onPressOut={() => {
              publishScale.value = withSpring(1);
            }}
            onPress={handleSubmit}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="blue" />
            ) : (
              <Text style={styles.publishButtonText}>{t('common.accommodation_creation_screen.publish_button')}</Text>
            )}
          </TouchableOpacity>
        </Animated.View>
      ),
    });
  }, [navigation, publishScale, animatedPublishStyle, handleSubmit, t]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ScrollView style={styles.scrollContainer}>
        <CollapsibleSection title={t('common.general_info.title')}>
          <GeneralInformationSection formData={formData} setFormData={setFormData} />
        </CollapsibleSection>

        <CollapsibleSection title={t('common.photo.title')}>
          <PhotoSection formData={formData} pickMainPhoto={pickMainPhoto} />
        </CollapsibleSection>

        <CollapsibleSection title={t('common.location.title')}>
          <LocationSection formData={formData} setFormData={setFormData} isGeocoding={isGeocoding} getCurrentLocation={getCurrentLocation} />
        </CollapsibleSection>

        <CollapsibleSection title={t('common.media.title')}>
          <MediaSection
            formData={formData}
            setFormData={setFormData}
            pickMedia={pickMedia}
            removeMedia={removeMedia}
            mediaScrollViewRef={mediaScrollViewRef}
            availableAmenities={availableAmenities}
          />
        </CollapsibleSection>

        <CollapsibleSection title={t('common.accommodation_update_screen.points_of_interest')}>
          <PointsOfInterestSection
            poiForm={poiForm}
            setPoiForm={setPoiForm}
            isPoiGeocoding={isPoiGeocoding}
            handleAddPoi={handleAddPoi}
            formData={formData}
            setFormData={setFormData}
            handleRemovePoi={handleRemovePoi}
          />
        </CollapsibleSection>

        <CollapsibleSection title={t('common.faq_section.title')}>
          <Text style={styles.sectionSubtitle}>{t('common.faq_section.subtitle')}</Text>
          <Text style={styles.noteText}>{t('common.accommodation_creation_screen.faq_note_with_save')}</Text>
          <FAQSection
            housingUuid="temp"
            initialFaqs={formData.faqs}
            onFaqsChange={handleFaqsChange}
          />
        </CollapsibleSection>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          {isLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.submitButtonText}>{t('common.accommodation_creation_screen.create_listing_button')}</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f8f8' },
  scrollContainer: { flex: 1, paddingTop: 16, marginBottom: 16 },
  backButton: { padding: 8 },
  publishButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginRight: 8
  },
  publishButtonText: { color: 'white', fontWeight: '600' },
  submitButton: {
    backgroundColor: '#4A90E2', padding: 16, borderRadius: 8, margin: 16, alignItems: 'center',
  },
  submitButtonText: { color: 'white', fontWeight: '600', fontSize: 16 },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  noteText: {
    fontSize: 14,
    color: '#888',
    fontStyle: 'italic',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#4A90E2',
  },
});

export default AccommodationCreationScreen;
