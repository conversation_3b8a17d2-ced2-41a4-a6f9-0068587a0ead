import React, { useLayoutEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList,
  ScrollView,
  Dimensions
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
const HostImage = require('../../../assets/Host.jpg');
import Animated, {
  FadeIn,
  FadeOut,
  Layout,
  useAnimatedStyle,
  withTiming,
  withSequence,
  useSharedValue
} from 'react-native-reanimated';
import { useAuthStore } from '@/stores/useAuthStore';
import { useTranslation } from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import { useNotification } from '@/context/NotificationContext';

// Define proper types for items and navigation
interface Listing {
  title: string;
  address: string;
  capacity: number;
  coverImage?: string;
  [key: string]: any; // For any other props
}

interface ListingsData {
  cohosted?: Listing[];
  housings?: Listing[];
}

// Define a type for our dummy invitation section item
interface InvitationSectionItem {
  type: 'invitations';
}

// Define a union type for all possible item types in our sections
type SectionItemType = Listing | InvitationSectionItem;

interface SectionData {
  title: string;
  data: SectionItemType[];
  renderItem?: (info: { item: SectionItemType }) => React.ReactElement | null;
}

interface ServerResponse<T> {
  code: number;
  status: string;
  messages: string[];
  data: T;
}

interface CohostInvitation {
  uuid: string;
  status: string;
  host: {
    displayName: string;
    email: string;
    phoneNumber: string;
  };
  housing: {
    uuid: string;
    title: string;
    description: string;
    coverImage: string;
  };
  coHost: string;
  createdAt: string;
}

interface ListItem {
  type: 'invitation' | 'listing';
  data: CohostInvitation | Listing;
}

const InvitationCard = ({ item, onAccept, onReject }: { item: CohostInvitation; onAccept: (uuid: string) => Promise<any>; onReject: (uuid: string) => Promise<any> }) => {
  console.log("Invitation: ", item);
  const [accepting, setAccepting] = useState(false);
  const [accepted, setAccepted] = useState(false);
  const [rejecting, setRejecting] = useState(false);
  const [rejected, setRejected] = useState(false);
  const buttonScale = useSharedValue(1);
  const rejectButtonScale = useSharedValue(1);
  const cardOpacity = useSharedValue(1); // Add opacity for fade-out animation
  const { t } = useTranslation();

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }]
  }));

  const rejectButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: rejectButtonScale.value }]
  }));

  // Add animated style for the card opacity
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value
  }));

  const handleAccept = async () => {
    // Start loading state and button press animation
    setAccepting(true);
    buttonScale.value = withTiming(0.95, { duration: 100 });

    try {
      // Call the accept function
      const result = await onAccept(item.uuid);

      // Show success state with animation
      setAccepting(false);
      setAccepted(true);

      // Success animation - pulse the button
      buttonScale.value = withSequence(
        withTiming(1.15, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );

      // After animation completes, fade out the card
      return new Promise(resolve => {
        setTimeout(() => {
          // Start fade-out animation
          cardOpacity.value = withTiming(0, { duration: 300 });

          // Wait for fade-out to complete before resolving
          setTimeout(() => {
            resolve(result);
          }, 300);
        }, 500); // Wait 500ms to ensure user sees the success animation
      });
    } catch (error) {
      console.error('Error in handleAccept:', error);
      setAccepting(false);
      buttonScale.value = withTiming(1, { duration: 200 });
      return false;
    }
  };

  const handleReject = async () => {
    // Start loading state and button press animation
    setRejecting(true);
    rejectButtonScale.value = withTiming(0.95, { duration: 100 });

    try {
      // Call the reject function
      const result = await onReject(item.uuid);

      // Update UI to show success state
      setRejecting(false);
      setRejected(true);

      // Success animation - pulse the button
      rejectButtonScale.value = withSequence(
        withTiming(1.15, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );

      // After animation completes, fade out the card
      return new Promise(resolve => {
        setTimeout(() => {
          // Start fade-out animation
          cardOpacity.value = withTiming(0, { duration: 300 });

          // Wait for fade-out to complete before resolving
          setTimeout(() => {
            resolve(result);
          }, 300);
        }, 500); // Wait 500ms to ensure user sees the success animation
      });
    } catch (error) {
      // Reset to normal state if there's an error
      console.error('Error in handleReject:', error);
      setRejecting(false);
      rejectButtonScale.value = withTiming(1, { duration: 200 });
      return false;
    }
  };

  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      layout={Layout.springify()}
      style={[styles.invitationCard, cardAnimatedStyle]}
    >
      <View style={styles.invitationHeader}>
        {item.housing.coverImage && (
          <Image
            source={{ uri: item.housing.coverImage }}
            style={styles.invitationCoverImage}
          />
        )}
        <LinearGradient
          colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0)']}
          style={styles.invitationGradient}
        />
        <View style={styles.invitationInfo}>
          <View style={styles.invitationMessage}>
            <Text style={styles.invitationHostName}>
              {item.host.displayName}
            </Text>
            <Text style={styles.invitationAction}>
              {t('common.accommodation_screen.invitation_title')}
            </Text>
          </View>
          <Text style={styles.invitationHousingName}>
            {item.housing.title}
          </Text>
        </View>
      </View>

      <View style={styles.invitationContent}>
        <Text style={styles.invitationDescription} numberOfLines={2}>
          {item.housing.description}
        </Text>

        <View style={styles.buttonsContainer}>
          <Animated.View style={[styles.buttonContainer, buttonAnimatedStyle]}>
            <TouchableOpacity
              style={[
                styles.acceptButton,
                accepting && styles.acceptButtonLoading,
                accepted && styles.acceptButtonSuccess
              ]}
              onPress={handleAccept}
              disabled={accepting || accepted || rejecting || rejected}
            >
              {accepting ? (
                <ActivityIndicator size="small" color="white" />
              ) : accepted ? (
                <MaterialIcons name="check" size={24} color="white" />
              ) : (
                <View style={styles.buttonContent}>
                  <MaterialIcons name="check-circle" size={18} color="white" style={styles.buttonIcon} />
                  <Text style={styles.acceptButtonText}>
                    {t('common.accommodation_screen.accept_invitation')}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>

          <Animated.View style={[styles.buttonContainer, rejectButtonAnimatedStyle]}>
            <TouchableOpacity
              style={[
                styles.rejectButton,
                rejecting && styles.rejectButtonLoading,
                rejected && styles.rejectButtonSuccess
              ]}
              onPress={handleReject}
              disabled={accepting || accepted || rejecting || rejected}
            >
              {rejecting ? (
                <ActivityIndicator size="small" color="white" />
              ) : rejected ? (
                <MaterialIcons name="close" size={24} color="white" />
              ) : (
                <View style={styles.buttonContent}>
                  <MaterialIcons name="cancel" size={18} color="white" style={styles.buttonIcon} />
                  <Text style={styles.rejectButtonText}>
                    {t('common.accommodation_screen.reject_invitation')}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    </Animated.View>
  );
};

const AccommodationScreen = () => {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const { t } = useTranslation();
  const { notification, expoPushToken, error } = useNotification();
  const queryClient = useQueryClient();
  console.log("Notification: ", notification);
  console.log("ExpoPushToken: ", expoPushToken);
  console.log("Error: ", error);

  const { data: listings, isLoading, isError, refetch } = useQuery({
    queryKey: ['listings'],
    queryFn: async () => {
      const response = await httpClient.get('/housings/getall?onlyMine=1') as ServerResponse<ListingsData>;
      console.log("Listings: ", response.data);
      return response.data;
    }
  });

  // Fetch cohost invitations
  const { data: invitations, isLoading: isLoadingInvitations, refetch: refetchInvitations } = useQuery({
    queryKey: ['cohost-invitations'],
    queryFn: async () => {
      const response = await httpClient.get<ServerResponse<CohostInvitation[]>>('/housings/cohost-invitations/getall');
      // console.log("Invitations raw response: ", response);
      // console.log("Invitations data: ", response.data);

      // Make sure we're getting an array of invitations
      if (!Array.isArray(response.data)) {
        console.error("Expected array of invitations but got:", response.data);
        return [];
      }

      // Filter out accepted invitations
      const pendingInvitations = (response.data || []).filter(invitation => invitation.status === 'pending');
      // console.log("Pending invitations count:", pendingInvitations.length);
      // console.log("Pending invitations:", pendingInvitations);
      return pendingInvitations;
    }
  });

  const onRefresh = useCallback(() => {
    console.log("Refreshing data...");
    setRefreshing(true);
    // Refetch both listings and invitations
    Promise.all([
      refetch(),
      refetchInvitations()
    ])
      .catch(error => {
        console.error('Error refreshing data:', error);
      })
      .finally(() => {
        setRefreshing(false);
        console.log("Refreshed data");
      });
  }, [refetch, refetchInvitations]);

  useFocusEffect(
    React.useCallback(() => {
      // Refetch both listings and invitations when screen comes into focus
      Promise.all([
        refetch(),
        refetchInvitations()
      ]).catch(error => {
        console.error('Error refreshing data on focus:', error);
      });
    }, [refetch, refetchInvitations])
  );

  // Prepare sections data directly during render
  // This ensures it runs when needed and with the current data
  const getSections = () => {
    // Initialize with an empty array even if listings is null
    const sectionsArray: SectionData[] = [];

    // Only proceed with adding sections if listings data exists
    if (listings) {
      // Only add cohosted section if there are items
      if (listings.cohosted && listings.cohosted.length > 0) {
        sectionsArray.push({
          title: t('common.accommodation_screen.cohosted'),
          data: listings.cohosted
        });
      }

      // Only add housings section if there are items
      if (listings.housings && listings.housings.length > 0) {
        sectionsArray.push({
          title: t('common.accommodation_screen.your_listings'),
          data: listings.housings
        });
      }
    }

    return sectionsArray;
  };

  // Get sections when component renders
  const sections = getSections();

  // Debug logging for invitations state
  console.log("AccommodationScreen render - invitations:", invitations?.length || 0, "items");
  console.log("AccommodationScreen render - invitations array:", invitations);

  const navigateToDetails = (item: Listing) => {
    navigation.navigate('AccommodationDetails', { listingUuid: item.uuid });
  };

  const handleAddNewListing = () => {
    navigation.navigate('AccommodationCreation');
  };

  const handleAcceptInvitation = async (invitationUuid: string): Promise<boolean> => {
    console.log('🚀 handleAcceptInvitation called with UUID:', invitationUuid);
    try {
      console.log('📡 Making API call to accept invitation...');
      const response = await httpClient.post<ServerResponse<{ code: number }>>(`/housings/cohost-invitation/${invitationUuid}/accept`);
      console.log('📡 API response received:', response.data);

      if (response.code === 200) {
        console.log('✅ Acceptance successful, response code:', response.code);

        // Immediately refetch data after successful API call
        try {
          console.log('🔄 Starting data refresh process...');

          // Remove both invitations and listings queries from cache
          queryClient.removeQueries({ queryKey: ['cohost-invitations'] });
          queryClient.removeQueries({ queryKey: ['listings'] });
          console.log('🗑️ Query caches cleared, now refetching...');

          // Force refetch both invitations and listings data
          const [invitationsResult, listingsResult] = await Promise.all([
            refetchInvitations(),
            refetch() // This is the listings refetch function
          ]);

          console.log('📥 Invitations refetched, new data:', invitationsResult.data);
          console.log('📥 New invitations length:', invitationsResult.data?.length || 0);
          console.log('📥 Listings refetched, new data:', listingsResult.data);
          console.log('📥 New cohosted listings length:', listingsResult.data?.cohosted?.length || 0);

          console.log('✅ All data refreshed successfully');
          return true;
        } catch (error) {
          console.error('❌ Error during refetch:', error);
          return true; // Still return true since the API call succeeded
        }
      } else {
        console.log('❌ API call failed, response code:', response.code);
        return false;
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_screen.invite_accept_error')
      );
      return false; // Return false instead of throwing to match the expected return type
    }
  };

  const handleRejectInvitation = async (invitationUuid: string): Promise<boolean> => {
    try {
      const response = await httpClient.post<ServerResponse<{ message: string }>>(`/housings/cohost-invitation/${invitationUuid}/reject`);

      if (response.code === 200) {
        // Show success message
        console.log('Rejection successful:', response.data.message);

        // Immediately refetch data after successful API call
        try {
          console.log('🔄 Starting data refresh process...');

          // Remove both invitations and listings queries from cache
          queryClient.removeQueries({ queryKey: ['cohost-invitations'] });
          queryClient.removeQueries({ queryKey: ['listings'] });
          console.log('🗑️ Query caches cleared, now refetching...');

          // Force refetch both invitations and listings data
          const [invitationsResult, listingsResult] = await Promise.all([
            refetchInvitations(),
            refetch() // This is the listings refetch function
          ]);

          console.log('📥 Invitations refetched, new data:', invitationsResult.data);
          console.log('📥 New invitations length:', invitationsResult.data?.length || 0);
          console.log('📥 Listings refetched, new data:', listingsResult.data);
          console.log('📥 New cohosted listings length:', listingsResult.data?.cohosted?.length || 0);

          console.log('✅ All data refreshed successfully');
          return true;
        } catch (error) {
          console.error('❌ Error during refetch:', error);
          return true; // Still return true since the API call succeeded
        }
      }
      return false;
    } catch (error) {
      console.error('Error rejecting invitation:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_screen.invite_reject_error')
      );
      return false; // Return false instead of throwing to match the expected return type
    }
  };

  const renderListingItem = ({ item }: { item: Listing }) => {
    //console.log("Item: ", item);
    return (
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        layout={Layout.springify()}
        style={styles.listingCard}
      >
        <TouchableOpacity onPress={() => navigateToDetails(item)}>
          {item.coverImage && <Image source={{ uri: item.coverImage || HostImage }} style={styles.listingImage} />}
          <View style={styles.listingInfo}>
            <Text style={styles.listingTitle}>{item.title}</Text>
            <Text style={styles.listingLocation}>{item.address}</Text>
            <View style={styles.listingDetails}>
              <View style={styles.ratingContainer}>
                <Text style={styles.ratingText}>{t('common.accommodation_screen.capacity')}: {item.capacity}</Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderSectionHeader = ({ section }: { section: SectionData }) => {
    // Don't show header for housing section if there are no cohosted listings
    if (section.title === t('common.accommodation_screen.your_listings') &&
        (!listings?.cohosted || listings.cohosted.length === 0)) {
      return null;
    }

    return (
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{section.title}</Text>
      </View>
    );
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: t('common.accommodation_screen.title'),
    });
  }, [navigation, t]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
        <Text style={styles.loadingText}>{t('common.accommodation_screen.loading')}</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('common.accommodation_screen.error')}</Text>
      </View>
    );
  }

  // Check if there are no listings AND no invitations
  const hasNoContent = (!listings || (!listings.cohosted?.length && !listings.housings?.length)) &&
                      (!invitations || invitations.length === 0);

  if (hasNoContent) {
    return (
      <Animated.View
        style={styles.emptyContainer}
        entering={FadeIn}
        exiting={FadeOut}
      >
        <Text style={styles.emptyText}>{t('common.accommodation_screen.no_content')}</Text>
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={handleAddNewListing}
        >
          <Ionicons name="add" size={28} color="#fff" />
        </TouchableOpacity>
      </Animated.View>
    );
  }

// Separate component for the invitations carousel
type InvitationsCarouselProps = {
  invitations: CohostInvitation[];
  onAccept: (invitationUuid: string) => Promise<boolean>;
  onReject: (invitationUuid: string) => Promise<boolean>;
};

const InvitationsCarousel: React.FC<InvitationsCarouselProps> = ({ invitations, onAccept, onReject }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // Function to scroll to a specific index
  const scrollToIndex = (index: number) => {
    if (scrollViewRef.current) {
      const slideWidth = Dimensions.get('window').width * 0.85 + 16;
      scrollViewRef.current.scrollTo({
        x: index * slideWidth,
        animated: true
      });
      setActiveIndex(index);
    }
  };

  // Handle scroll events
  const handleScroll = (event: any) => {
    const slideWidth = Dimensions.get('window').width * 0.85 + 16;
    const offset = event.nativeEvent.contentOffset.x;
    const newActiveIndex = Math.round(offset / slideWidth);
    setActiveIndex(newActiveIndex);
  };

  // Effect to handle changes in the invitations array
  React.useEffect(() => {
    // If the active index is now beyond the available invitations, scroll to the last one
    if (invitations.length > 0 && activeIndex >= invitations.length) {
      scrollToIndex(invitations.length - 1);
    }
  }, [invitations.length, activeIndex]);

  // Custom handler for accept action with proper scrolling
  const handleAcceptWithScroll = async (invitationUuid: string) => {
    // Find the index of the invitation being accepted
    const invitationIndex = invitations.findIndex(inv => inv.uuid === invitationUuid);
    if (invitationIndex === -1) return false;

    // Check if this is the last invitation and we're currently viewing it
    const isLastInvitation = invitationIndex === invitations.length - 1;
    const isCurrentlyViewing = invitationIndex === activeIndex;

    console.log(`Accepting invitation: ${invitationUuid}`);
    console.log(`Invitation index: ${invitationIndex}, Active index: ${activeIndex}`);
    console.log(`Is last invitation: ${isLastInvitation}, Is currently viewing: ${isCurrentlyViewing}`);

    // Store whether we need to scroll after acceptance
    const shouldScrollAfter = isLastInvitation && isCurrentlyViewing && invitations.length > 1;
    const scrollToIndexAfter = invitationIndex - 1;

    try {
      // First call the original accept handler
      const result = await onAccept(invitationUuid);

      // After acceptance is processed, scroll if needed
      if (shouldScrollAfter) {
        // Wait a bit longer to ensure the animation is visible and data is refetched
        setTimeout(() => {
          console.log(`Scrolling to previous invitation at index ${scrollToIndexAfter}`);
          scrollToIndex(scrollToIndexAfter);
        }, 700); // Wait 700ms after the accept handler completes
      }

      return result;
    } catch (error) {
      console.error('Error in handleAcceptWithScroll:', error);
      throw error;
    }
  };

  // Custom reject handler that checks if we need to scroll
  const handleRejectWithScroll = async (invitationUuid: string) => {
    // Find the index of the invitation being rejected
    const invitationIndex = invitations.findIndex(inv => inv.uuid === invitationUuid);
    if (invitationIndex === -1) return false;

    // Check if this is the last invitation and we're currently viewing it
    const isLastInvitation = invitationIndex === invitations.length - 1;
    const isCurrentlyViewing = invitationIndex === activeIndex;

    console.log(`Rejecting invitation: ${invitationUuid}`);
    console.log(`Invitation index: ${invitationIndex}, Active index: ${activeIndex}`);
    console.log(`Is last invitation: ${isLastInvitation}, Is currently viewing: ${isCurrentlyViewing}`);

    // Store whether we need to scroll after rejection
    const shouldScrollAfter = isLastInvitation && isCurrentlyViewing && invitations.length > 1;
    const scrollToIndexAfter = invitationIndex - 1;

    try {
      // First call the original reject handler
      const result = await onReject(invitationUuid);

      // After rejection is processed, scroll if needed
      if (shouldScrollAfter) {
        // Wait a bit longer to ensure the animation is visible and data is refetched
        setTimeout(() => {
          console.log(`Scrolling to previous invitation at index ${scrollToIndexAfter}`);
          scrollToIndex(scrollToIndexAfter);
        }, 700); // Wait 700ms after the rejection handler completes
      }

      return result;
    } catch (error) {
      console.error('Error in handleRejectWithScroll:', error);
      throw error;
    }
  };

  if (!invitations || !Array.isArray(invitations) || invitations.length === 0) {
    console.log("InvitationsCarousel: No invitations to display, returning null");
    return null;
  }

  console.log("Rendering invitations carousel with", invitations.length, "invitations");

  return (
    <View style={styles.invitationsContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.invitationsList}
        snapToInterval={Dimensions.get('window').width * 0.85 + 16} // Card width + margin
        decelerationRate="fast"
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {invitations.map((invitation) => {
          console.log("Rendering invitation card for", invitation.uuid);
          return (
            <InvitationCard
              key={invitation.uuid}
              item={invitation}
              onAccept={handleAcceptWithScroll}
              onReject={handleRejectWithScroll}
            />
          );
        })}
      </ScrollView>

      {/* Pagination indicators */}
      <View style={styles.paginationContainer}>
        {invitations.map((_, index) => (
          <View
            key={index}
            style={[styles.paginationDot, index === activeIndex && styles.paginationDotActive]}
          />
        ))}
      </View>
    </View>
  );
};

  return (
    <SafeAreaView style={styles.container}>

      <SectionList
        sections={[
          ...(invitations && Array.isArray(invitations) && invitations.length > 0 ? [{
            title: t('common.accommodation_screen.pending_invitations'),
            data: [{ type: 'invitations' } as InvitationSectionItem], // Use a single dummy item
            renderItem: () => {
              console.log('Rendering invitations section with', invitations?.length || 0, 'invitations');
              return <InvitationsCarousel
                invitations={invitations}
                onAccept={handleAcceptInvitation}
                onReject={handleRejectInvitation}
              />
            }
          }] : []),
          ...sections
        ]}
        renderSectionHeader={({ section }) => (
          <View style={[
            styles.sectionHeader,
            section.title === t('common.accommodation_screen.pending_invitations') && styles.invitationsSectionHeader
          ]}>
            {section.title === t('common.accommodation_screen.pending_invitations') ? (
              <View style={styles.invitationsTitleContainer}>
                <MaterialIcons name="mail" size={24} color="#4A90E2" />
                <Text style={styles.invitationsTitle}>
                  {section.title}
                </Text>
              </View>
            ) : (
              <Text style={styles.sectionTitle}>{section.title}</Text>
            )}
          </View>
        )}
        renderItem={({ section, item }) => {
          if (section.title === t('common.accommodation_screen.pending_invitations')) {
            return null; // We handle this with custom renderItem in the section
          }
          // Type guard to ensure we're dealing with a Listing
          if ('title' in item && 'address' in item) {
            return renderListingItem({ item: item as Listing });
          }
          return null;
        }}
        keyExtractor={(_, index) => index.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        stickySectionHeadersEnabled={true}
      />

      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleAddNewListing}
      >
        <Ionicons name="add" size={28} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  logoutButton: {

  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4682B4',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  listContainer: {
    padding: 16,
  },
  sectionHeader: {
    backgroundColor: '#f8f8f8',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  invitationsSectionHeader: {
    backgroundColor: '#fff',
    borderBottomWidth: 0,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4682B4',
  },
  listingCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    overflow: 'hidden',
  },
  listingImage: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  listingInfo: {
    padding: 16,
  },
  listingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  listingLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  listingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 4,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4682B4',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingButtonText: {
    fontSize: 28,
    color: '#fff',
    fontWeight: 'bold',
  },
  invitationsTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  invitationsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  invitationsList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  invitationsContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  invitationCard: {
    width: Dimensions.get('window').width * 0.85, // 85% of screen width
    backgroundColor: '#fff',
    borderRadius: 16,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    overflow: 'hidden',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#4A90E2',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  invitationHeader: {
    height: 140,
    position: 'relative',
  },
  invitationCoverImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  invitationGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '100%',
  },
  invitationInfo: {
    padding: 16,
    position: 'relative',
    zIndex: 1,
    flex: 1,
    justifyContent: 'space-between',
  },
  invitationMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  invitationHostName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
    marginRight: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  invitationAction: {
    fontSize: 20,
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  invitationHousingName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#fff',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  invitationContent: {
    padding: 16,
    backgroundColor: '#fff',
  },
  invitationDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  buttonContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  acceptButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  acceptButtonLoading: {
    backgroundColor: '#4A90E2',
  },
  acceptButtonSuccess: {
    backgroundColor: '#4CAF50',
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  rejectButton: {
    backgroundColor: '#F44336',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#F44336',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  rejectButtonLoading: {
    backgroundColor: '#F44336',
  },
  rejectButtonSuccess: {
    backgroundColor: '#757575',
  },
  rejectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 6,
  },
});

export default AccommodationScreen;