import React, { useState, useEffect, useLayoutEffect, useCallback, useMemo } from 'react';
import { GOOGLE_MAPS_API_KEY } from '@/config/env';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { MaterialIcons } from '@expo/vector-icons';
// import MapView, { Marker } from 'react-native-maps';
//import CountryPicker from 'react-native-country-picker-modal';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import { httpClient } from '@/utils/http';
import { deleteFileFromUrl, uploadFile } from '@/utils/util';
// import { list } from 'firebase/storage';
import { AppConstants } from '@/constants/default';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import CollapsibleSection from '@/components/CollapsibleSection';
import GeneralInformationSection from '@/components/GeneralInformationSection';
import PhotoSection from '@/components/PhotoSection';
import LocationSection from '@/components/LocationSection';
import MediaSection from '@/components/MediaSection';
// import AmenitiesSection from '@/components/AmenitiesSection';
import PointsOfInterestSection from '@/components/PointsOfInterestSection';
import FAQSection from '@/components/FAQSection';

// Define interfaces for better type safety
interface Media {
  id?: string; // For existing media
  uri: string;
  url?: string; // Adding url property for compatibility
  type: 'image' | 'video' | 'document'; // Restrict to specific types
  description: string;
  amenityTag: string | null;
  tutorial: boolean | null; // Change to boolean | null to match MediaItem
  isExisting?: boolean; // Flag to identify existing media vs new media
  modified?: boolean; // Flag to identify if existing media has been modified
}

interface PointOfInterest {
  name: string;
  placeId: string;
  address: string;
  latitude: number;
  longitude: number;
  photo?: string;
  types?: string[];
  location?: {
    latitude: number;
    longitude: number;
  };
}

interface FormData {
  title: string;
  description: string;
  capacity: string;
  address: string;
  coordinates: {
    latitude: string;
    longitude: string;
  };
  mainPhoto: string | null;
  media: Media[];
  mediaDescription: string;
  amenities: string[];
  pointsOfInterest: PointOfInterest[];
  faqs?: FAQ[];
}

interface POIForm {
  name: string;
  address: string;
  latitude: string;
  longitude: string;
}

interface AmenityItem {
  id: string;
  name: {
    en: string;
    fr: string;
  };
  icon: string;
}

interface FAQ {
  uuid: string;
  question: string;
  answer: string;
}

interface Listing {
  uuid: string;
  title: string;
  description: string;
  capacity: string;
  address: string;
  latitude: string;
  longitude: string;
  coverImage: string;
  amenities: string[];
  pointsOfInterest: PointOfInterest[];
  media?: Array<{
    uuid: string;
    url: string;
    type: string;
    description?: string;
    amenityTag?: string;
    tutorial?: boolean;
  }>;
  faqs?: FAQ[];
}

interface ServerResponse {
  code: number;
  status: string;
  data: any;
}

const AccommodationUpdateScreen = ({ navigation, route } : { navigation: NavigationProp<ParamListBase>, route: any}) => {
  const listingUuid = route.params.listingUuid as string;
  const { t } = useTranslation();
  const mediaScrollViewRef = React.useRef<ScrollView>(null);

  // Fetch listing data using React Query
  const { data: listing, isLoading: isListingLoading, error: listingError } = useQuery({
    queryKey: ['listing', listingUuid],
    queryFn: async () => {
      const response = await httpClient.get(`/housings/getone/${listingUuid}`) as ServerResponse;
      console.log("Raw listing data:", JSON.stringify(response.data, null, 2));

      // Directly return the response data, assuming media property is correctly named

      return response.data as Listing;
    },
    enabled: !!listingUuid,
  });

  // State management for form data
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    capacity: '',
    address: '',
    coordinates: {
      latitude: '',
      longitude: ''
    },
    mainPhoto: null,
    media: [],
    mediaDescription: '',
    amenities: [],
    pointsOfInterest: []
  });

  // Function to fetch place details for a POI
  const fetchPlaceDetails = async (placeId: string) => {
    try {
      const response = await fetch(`https://places.googleapis.com/v1/places/${placeId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
          'X-Goog-FieldMask': 'id,displayName,photos,formattedAddress,location,types'
        }
      });

      return await response.json();
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  };

  // Update form data when listing is loaded
  useEffect(() => {
    if (listing) {
      // Get media items directly from the listing
      const mediaItems = listing.media || [];

      // Transform existing media into the correct format
      const existingMedia = mediaItems.map(media => {
        return {
          id: media.uuid, // Use uuid as id
          uri: media.url, // Use url as uri
          type: getCorrectMediaType(media.type),
          description: media.description || '',
          amenityTag: media.amenityTag || null,
          tutorial: media.tutorial || false,
          isExisting: true, // Mark as existing media
          modified: false // Initially not modified
        };
      });

      // First set the form data without enhanced POIs
      setFormData({
        title: listing.title,
        description: listing.description,
        capacity: listing.capacity,
        address: listing.address,
        coordinates: {
          latitude: listing.latitude,
          longitude: listing.longitude
        },
        mainPhoto: listing.coverImage,
        media: existingMedia,
        mediaDescription: '',
        amenities: listing.amenities,
        pointsOfInterest: listing.pointsOfInterest,
        faqs: listing.faqs || []
      });

      // Then fetch place details for each POI and update the form data
      const fetchPOIDetails = async () => {
        console.log('Fetching place details for POIs:', listing.pointsOfInterest);

        const enhancedPOIs = await Promise.all(
          listing.pointsOfInterest.map(async (poi) => {
            if (poi.placeId) {
              console.log(`Fetching details for POI: ${poi.name} with placeId: ${poi.placeId}`);
              const placeDetails = await fetchPlaceDetails(poi.placeId);

              if (placeDetails) {
                console.log(`Got place details for ${poi.name}:`, {
                  photo: placeDetails.photos?.[0]?.name,
                  types: placeDetails.types
                });

                // Return enhanced POI with place details
                return {
                  ...poi,
                  photo: placeDetails.photos?.[0]?.name,
                  types: placeDetails.types || [],
                  // Create a location object for compatibility with PointsOfInterestSection
                  location: {
                    latitude: poi.latitude || 0,
                    longitude: poi.longitude || 0
                  },
                  // Ensure latitude and longitude are set if they're null
                  latitude: poi.latitude || placeDetails.location?.latitude || 0,
                  longitude: poi.longitude || placeDetails.location?.longitude || 0
                };
              } else {
                console.log(`No place details found for ${poi.name}`);
              }
            } else {
              console.log(`POI ${poi.name} has no placeId`);
            }

            // If no place details or error, return original POI with location object
            return {
              ...poi,
              location: {
                latitude: poi.latitude || 0,
                longitude: poi.longitude || 0
              },
              // Ensure latitude and longitude are set if they're null
              latitude: poi.latitude || 0,
              longitude: poi.longitude || 0
            };
          })
        );

        console.log('Enhanced POIs:', enhancedPOIs);

        // Update form data with enhanced POIs
        setFormData(prevData => ({
          ...prevData,
          pointsOfInterest: enhancedPOIs
        }));
      };

      fetchPOIDetails();
    }
  }, [listing]);

  // Helper function to ensure media type is valid
  const getCorrectMediaType = (typeStr: string): 'image' | 'video' | 'document' => {
    const lowerType = typeStr.toLowerCase();
    if (lowerType.includes('image') || lowerType.includes('photo') || lowerType.includes('jpg') || lowerType.includes('png')) {
      return 'image';
    } else if (lowerType.includes('video') || lowerType.includes('mp4') || lowerType.includes('mov')) {
      return 'video';
    } else {
      return 'document';
    }
  };

  const [poiForm, setPoiForm] = useState<POIForm>({
    name: '',
    address: '',
    latitude: '',
    longitude: '',
  });

  const [isPoiGeocoding, setIsPoiGeocoding] = useState(false);
  const [isGeocoding, setIsGeocoding] = useState(false);
  // const [showCountryPicker, setShowCountryPicker] = useState(false); // Unused state
  const [isLoading, setIsLoading] = useState(false);
  // const [isLocating, setIsLocating] = useState(false); // Unused state

  // Shared value for button animation
  const publishScale = useSharedValue(1);

  // Available amenities - memoized to prevent recreation on each render
  const availableAmenities = useMemo<AmenityItem[]>(() => [
    { id: 'wifi', name: { en: 'Wi-Fi', fr: 'Wi-Fi' }, icon: 'wifi' },
    { id: 'parking', name: { en: 'Parking', fr: 'Parking' }, icon: 'local-parking' },
    { id: 'air_conditioning', name: { en: 'Air Conditioning', fr: 'Climatisation' }, icon: 'ac-unit' },
    { id: 'heating', name: { en: 'Heating', fr: 'Chauffage' }, icon: 'whatshot' },
    { id: 'kitchen', name: { en: 'Equipped Kitchen', fr: 'Cuisine équipée' }, icon: 'kitchen' },
    { id: 'tv', name: { en: 'TV', fr: 'Télévision' }, icon: 'tv' },
    { id: 'washing_machine', name: { en: 'Washing Machine', fr: 'Machine à laver' }, icon: 'local-laundry-service' },
    { id: 'dryer', name: { en: 'Dryer', fr: 'Sèche-linge' }, icon: 'dry-cleaning' },
    { id: 'dishwasher', name: { en: 'Dishwasher', fr: 'Lave-vaisselle' }, icon: 'countertops' },
    { id: 'pool', name: { en: 'Pool', fr: 'Piscine' }, icon: 'pool' },
    { id: 'hot_tub', name: { en: 'Hot Tub', fr: 'Jacuzzi' }, icon: 'hot-tub' },
    { id: 'gym', name: { en: 'Gym', fr: 'Salle de sport' }, icon: 'fitness-center' },
    { id: 'workspace', name: { en: 'Workspace', fr: 'Espace de travail' }, icon: 'work' },
    { id: 'balcony', name: { en: 'Balcony / Terrace', fr: 'Balcon / Terrasse' }, icon: 'deck' },
    { id: 'garden', name: { en: 'Garden', fr: 'Jardin' }, icon: 'park' },
    { id: 'fireplace', name: { en: 'Fireplace', fr: 'Cheminée' }, icon: 'fireplace' },
    { id: 'bbq', name: { en: 'BBQ Area', fr: 'Espace barbecue' }, icon: 'outdoor-grill' },
    { id: 'safe', name: { en: 'Safe', fr: 'Coffre-fort' }, icon: 'lock' },
    { id: 'elevator', name: { en: 'Elevator', fr: 'Ascenseur' }, icon: 'elevator' },
    { id: 'pet_friendly', name: { en: 'Pet Friendly', fr: 'Animaux acceptés' }, icon: 'pets' },
    { id: 'leisure_equipment', name: { en: 'Leisure Equipment', fr: 'Équipements de loisirs' }, icon: 'gamepad-variant' },
    { id: 'comfort_equipment', name: { en: 'Comfort Equipment', fr: 'Équipements de confort' }, icon: 'sofa' },
    { id: 'rules', name: { en: 'Rules', fr: 'Règles' }, icon: 'rule' }
  ], []);

  const handleAddPoi = () => {
    // Log the poiForm data to verify coordinates are being passed correctly
    console.log('handleAddPoi called with poiForm:', poiForm);

    // Check if all required fields are filled
    if (!poiForm.name || !poiForm.address || !poiForm.latitude || !poiForm.longitude) {
      Alert.alert(
        t('common.errors.title'),
        t('common.errors.poi_fields_required')
      );
      return;
    }

    // Add POI to the formData
    const newPoi: PointOfInterest = {
      name: poiForm.name,
      placeId: Math.random().toString(36).substring(2, 15),
      address: poiForm.address,
      latitude: parseFloat(poiForm.latitude),
      longitude: parseFloat(poiForm.longitude),
      // Add location object for compatibility with PointsOfInterestSection
      location: {
        latitude: parseFloat(poiForm.latitude),
        longitude: parseFloat(poiForm.longitude)
      }
      // Note: photo and types will be added by PointsOfInterestSection if available
    };

    setFormData({
      ...formData,
      pointsOfInterest: [...formData.pointsOfInterest, newPoi]
    });

    // Reset the POI form
    setPoiForm({
      name: '',
      address: '',
      latitude: '',
      longitude: ''
    });

    // Show success message
    Alert.alert(
      t('common.success.title'),
      t('common.success.poi_added')
    );
  };

  const handleRemovePoi = (index: number) => {
    Alert.alert(
      t('common.alerts.confirm_title'),
      t('common.alerts.confirm_remove_poi'),
      [
        {
          text: t('common.buttons.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.buttons.remove'),
          style: 'destructive',
          onPress: () => {
            const updatedPois = [...formData.pointsOfInterest];
            updatedPois.splice(index, 1);
            setFormData({
              ...formData,
              pointsOfInterest: updatedPois
            });

            // Show success message
            Alert.alert(
              t('common.success.title'),
              t('common.success.poi_removed')
            );
          }
        }
      ]
    );
  };

  // Request permissions on mount
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.location.error.location_permission_denied'));
      }
    })();

    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        }
      }
    })();
  }, [t]);

  // Geocode address when user types one (debounced)
  useEffect(() => {
    if (formData.address.length > 3) {
      setIsGeocoding(true);
      const timer = setTimeout(() => {
        Location.geocodeAsync(formData.address)
          .then(results => {
            if (results.length > 0) {
              const { latitude, longitude } = results[0];
              setFormData(prev => ({
                ...prev,
                coordinates: {
                  latitude: latitude.toString(),
                  longitude: longitude.toString()
                }
              }));
            }
            setIsGeocoding(false);
          })
          .catch(() => {
            setIsGeocoding(false);
          });
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [formData.address]);

  useEffect(() => {
    if (poiForm.address.length > 3) {
      setIsPoiGeocoding(true);
      const timer = setTimeout(() => {
        Location.geocodeAsync(poiForm.address)
          .then(results => {
            if (results.length > 0) {
              const { latitude, longitude } = results[0];
              setPoiForm(prev => ({
                ...prev,
                latitude: latitude.toString(),
                longitude: longitude.toString()
              }));
            }
            setIsPoiGeocoding(false);
          })
          .catch(() => {
            setIsPoiGeocoding(false);
          });
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [poiForm.address]);

  // Get current location function - memoized to prevent recreation on each render
  const getCurrentLocation = useCallback(async () => {
    try {
      setIsLoading(true); // Using setIsLoading instead of removed setIsLocating
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          t('common.permissions.location_permission_title'),
          t('common.permissions.location_permission_message')
        );
        setIsLoading(false); // Using setIsLoading instead of removed setIsLocating
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      // Update form data with coordinates
      setFormData({
        ...formData,
        coordinates: {
          latitude: latitude.toString(),
          longitude: longitude.toString()
        }
      });

      // Attempt to get address from coordinates (reverse geocoding)
      try {
        const geocode = await Location.reverseGeocodeAsync({
          latitude,
          longitude
        });

        if (geocode.length > 0) {
          const addressObj = geocode[0];
          const address = [
            addressObj.name,
            addressObj.street,
            addressObj.district,
            addressObj.city,
            addressObj.region,
            addressObj.country
          ].filter(Boolean).join(', ');

          setFormData(prev => ({
            ...prev,
            address
          }));
        }
      } catch (error) {
        console.error('Reverse geocoding error:', error);
      }

      setIsLoading(false); // Using setIsLoading instead of removed setIsLocating
    } catch (error) {
      console.error('Location error:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.errors.location_error')
      );
      setIsLoading(false); // Using setIsLoading instead of removed setIsLocating
    }
  }, [formData, t]);

  // Function to pick main photo - memoized to prevent recreation on each render
  const pickMainPhoto = useCallback(async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled) {
        if (result.assets && result.assets.length > 0) {
          setFormData({...formData, mainPhoto: result.assets[0].uri});
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.errors.title'), t('common.errors.image_pick_error'));
    }
  }, [formData, t]);

  // Media picker function - memoized to prevent recreation on each render
  const pickMedia = useCallback(async (mediaType: string) => {
    try {
      if (mediaType === 'document') {
        const result = await DocumentPicker.getDocumentAsync({
          type: 'application/pdf',
          copyToCacheDirectory: true,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const docUri = result.assets[0].uri;
          const docName = result.assets[0].name || 'Document';

          const newMedia: Media = {
            uri: docUri,
            type: 'document',
            description: docName,
            amenityTag: null,
            tutorial: false
          };

          setFormData({
            ...formData,
            media: [...formData.media, newMedia]
          });

          // Scroll to the end of the media list after adding the new item
          setTimeout(() => {
            mediaScrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }
        return;
      }

      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(t('common.permissions.media_permission_title'), t('common.permissions.media_permission_message'));
        return;
      }

      let result;
      let pickerOptions: ImagePicker.ImagePickerOptions = {
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        quality: 0.8,
      };

      // Adjust options based on media type
      if (mediaType === 'image') {
        pickerOptions.mediaTypes = ImagePicker.MediaTypeOptions.Images;
      } else if (mediaType === 'video') {
        pickerOptions.mediaTypes = ImagePicker.MediaTypeOptions.Videos;
      }

      result = await ImagePicker.launchImageLibraryAsync(pickerOptions);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Add the media to the formData with correct type
        const newMedia: Media = {
          uri: result.assets[0].uri,
          type: mediaType as 'image' | 'video' | 'document',
          description: '',
          amenityTag: null,
          tutorial: false,
          isExisting: false
        };

        setFormData({
          ...formData,
          media: [...formData.media, newMedia]
        });
      }
    } catch (error) {
      console.error('Error picking media:', error);
      Alert.alert(t('common.errors.title'), t('common.errors.media_pick_error'));
    }
  }, [formData, t, mediaScrollViewRef]);

  // Remove media from the list - memoized to prevent recreation on each render
  const removeMedia = useCallback((index: number) => {
    const mediaToRemove = formData.media[index];

    // If this is existing media, we need to delete it via API
    if (mediaToRemove.isExisting && mediaToRemove.id) {
      Alert.alert(
        t('common.alerts.confirm_title'),
        t('common.media.confirm_remove'),
        [
          {
            text: t('common.buttons.cancel'),
            style: 'cancel'
          },
          {
            text: t('common.buttons.remove'),
            style: 'destructive',
            onPress: async () => {
              try {
                setIsLoading(true);
                await deleteFileFromUrl(mediaToRemove.uri as string);
                // Call the API to delete the media
                const response = await httpClient.delete(`/housings/media/delete/${mediaToRemove.id}`);

                if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
                  // Remove from state if API call successful
                  const updatedMedia = [...formData.media];
                  updatedMedia.splice(index, 1);
                  setFormData({
                    ...formData,
                    media: updatedMedia
                  });

                  Alert.alert(
                    t('common.success.title'),
                    t('common.media.removed_success')
                  );
                } else {
                  throw new Error('Failed to delete media');
                }
              } catch (error) {
                console.error('Error deleting media:', error);
                Alert.alert(
                  t('common.errors.title'),
                  t('common.media.delete_error')
                );
              } finally {
                setIsLoading(false);
              }
            }
          }
        ]
      );
    } else {
      // For new media, just remove from state
      const updatedMedia = [...formData.media];
      updatedMedia.splice(index, 1);
      setFormData({
        ...formData,
        media: updatedMedia
      });
    }
  }, [formData, t]);

  // Update media metadata when user changes it - memoized to prevent recreation on each render
  const updateMediaMetadata = useCallback((index: number, updatedMedia: Media) => {
    const mediaArray = [...formData.media];

    // If this is existing media, mark it as modified
    if (mediaArray[index].isExisting) {
      mediaArray[index] = {
        ...updatedMedia,
        modified: true
      };
    } else {
      mediaArray[index] = updatedMedia;
    }

    setFormData({
      ...formData,
      media: mediaArray
    });
  }, [formData]);

  // Removed unused toggleAmenity function

  // Animated style for Publish button
  const animatedPublishStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: publishScale.value }]
    };
  });

  // Handle form submission - memoized to prevent recreation on each render
  const handleSubmit = useCallback(async () => {
    // Inline validation to avoid circular dependency
    if (!formData.title || !formData.description || !formData.capacity || !formData.address) {
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_update_screen.required_fields')
      );
      return;
    }

      setIsLoading(true);
    try {
      // Upload the main photo if it's a new one
      let mainPhotoLink = formData.mainPhoto;
      if (formData.mainPhoto && formData.mainPhoto.startsWith('file://')) {
        mainPhotoLink = await uploadFile(formData.mainPhoto, AppConstants.HOUSING_FIREBASE_FOLDER);
      }

      // First, update any modified existing media via their dedicated endpoints
      const updatePromises = (formData.media || [])
        .filter(media => media && media.isExisting && media.id && media.modified)
        .map(async (media) => {
          // Update via dedicated endpoint
          const updateBody = {
            url: media.uri,
            type: media.type,
            description: media.description,
            amenityTag: media.amenityTag,
            tutorial: media.tutorial
          };

          console.log(`Updating existing media ${media.id} with:`, updateBody);
          await httpClient.put(`/housings/media/update/${media.id}`, updateBody);
        });

      // Wait for all updates to complete
      await Promise.all(updatePromises);

      // Then, handle only NEW media uploads - exclude all existing media
      const newMediaPromises = (formData.media || [])
        .filter(media => media && !media.isExisting)
        .map(async (media) => {
          if (media.uri && media.uri.startsWith('file://')) {
            console.log(`Uploading new media: ${media.type}`);
            const url = await uploadFile(media.uri, AppConstants.HOUSING_FIREBASE_FOLDER);
            return {
              url: url,
              type: media.type,
              description: media.description,
              amenityTag: media.amenityTag,
              tutorial: media.tutorial
            };
          }
          return null;
        });

      const newMediaWithNulls = await Promise.all(newMediaPromises);
      // Filter out any null values
      const newMediaLinks = newMediaWithNulls.filter(item => item !== null);

      console.log("New media for submission:", newMediaLinks);

      // Ensure all POIs have latitude and longitude values
      const processedPOIs = formData.pointsOfInterest.map(poi => {
        // If latitude or longitude is null but location exists, use location values
        if ((poi.latitude === null || poi.longitude === null) && poi.location) {
          return {
            ...poi,
            latitude: poi.location.latitude,
            longitude: poi.location.longitude
          };
        }
        return poi;
      });

      const body = {
        title: formData.title,
        description: formData.description,
        capacity: parseInt(formData.capacity, 10),
        latitude: parseFloat(formData.coordinates.latitude),
        longitude: parseFloat(formData.coordinates.longitude),
        address: formData.address,
        profilePicture: mainPhotoLink,
        housingMedia: newMediaLinks, // Use housingMedia in the request body as the server expects it
        pointsOfInterest: processedPOIs,
        amenities: formData.amenities
      };

      console.log("Submitting update with body:", JSON.stringify(body, null, 2));
      const response = await httpClient.post(`/housings/update/${listingUuid}`, body);

      if (response && typeof response === 'object' && 'code' in response && response.code === 200) {
        Alert.alert(
          t('common.accommodation_update_screen.success'),
          t('common.accommodation_update_screen.success_message')
        );
        navigation.goBack();
      } else {
        throw new Error('Failed to update listing');
      }
    } catch (error) {
      console.error('Error updating listing:', error);
      Alert.alert(
        t('common.errors.title'),
        t('common.accommodation_update_screen.error_message')
      );
    } finally {
      setIsLoading(false);
    }
  }, [formData, t, navigation, listingUuid]);

  // Removed unused validateForm function

  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
      ),
      headerTitle: t('common.accommodation_update_screen.header_title'),
      headerRight: () => (
        <Animated.View style={animatedPublishStyle}>
          <TouchableOpacity
            style={styles.publishButton}
            onPressIn={() => {
              publishScale.value = withSpring(0.95);
            }}
            onPressOut={() => {
              publishScale.value = withSpring(1);
            }}
            onPress={handleSubmit}
            disabled={isLoading || isListingLoading}
          >
            {isLoading || isListingLoading ? (
              <ActivityIndicator size="small" color="blue" />
            ) : (
                <Text style={styles.publishButtonText}>{t('common.accommodation_update_screen.update_button')}</Text>
            )}
          </TouchableOpacity>
        </Animated.View>
      ),
    });
  }, [navigation, publishScale, animatedPublishStyle, handleSubmit, t, isLoading, isListingLoading]);

  // Show loading state while fetching listing data
  if (isListingLoading) {
  return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
        <Text style={styles.loadingText}>{t('common.accommodation_update_screen.loading')}</Text>
          </View>
    );
  }

  // Show error state if listing fetch failed
  if (listingError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('common.errors.title')}</Text>
        <Text style={styles.errorMessage}>{t('common.errors.listing_fetch_failed')}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.retryButtonText}>{t('common.buttons.go_back')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state if no listing data
  if (!listing) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('common.errors.title')}</Text>
        <Text style={styles.errorMessage}>{t('common.errors.listing_not_found')}</Text>
          <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
          >
          <Text style={styles.retryButtonText}>{t('common.buttons.go_back')}</Text>
          </TouchableOpacity>
        </View>
    );
  }

  // Memoize the sections to prevent unnecessary re-renders
  const generalInfoSection = useMemo(() => (
    <CollapsibleSection title={t('common.general_info.title')}>
      <GeneralInformationSection formData={formData} setFormData={setFormData} />
    </CollapsibleSection>
  ), [formData, t]);

  const photoSection = useMemo(() => (
    <CollapsibleSection title={t('common.photo.title')}>
      <PhotoSection formData={formData} pickMainPhoto={pickMainPhoto} />
    </CollapsibleSection>
  ), [formData, pickMainPhoto, t]);

  const locationSection = useMemo(() => (
    <CollapsibleSection title={t('common.location.title')}>
      <LocationSection formData={formData} setFormData={setFormData} isGeocoding={isGeocoding} getCurrentLocation={getCurrentLocation} />
    </CollapsibleSection>
  ), [formData, isGeocoding, getCurrentLocation, t]);

  const mediaSection = useMemo(() => (
    <CollapsibleSection title={t('common.media.title')}>
      <MediaSection
        formData={formData}
        setFormData={setFormData}
        pickMedia={pickMedia}
        removeMedia={removeMedia}
        updateMediaMetadata={updateMediaMetadata}
        mediaScrollViewRef={mediaScrollViewRef}
        availableAmenities={availableAmenities}
      />
    </CollapsibleSection>
  ), [formData, pickMedia, removeMedia, updateMediaMetadata, availableAmenities, t]);

  const poiSection = useMemo(() => (
    <CollapsibleSection title={t('common.accommodation_update_screen.points_of_interest')}>
      <PointsOfInterestSection
        poiForm={poiForm}
        setPoiForm={setPoiForm}
        isPoiGeocoding={isPoiGeocoding}
        handleAddPoi={handleAddPoi}
        formData={formData}
        setFormData={setFormData}
        handleRemovePoi={handleRemovePoi}
      />
    </CollapsibleSection>
  ), [formData, poiForm, isPoiGeocoding, handleAddPoi, handleRemovePoi, t]);

  // Function to handle FAQs changes
  const handleFaqsChange = (faqs: FAQ[]) => {
    setFormData(prev => ({
      ...prev,
      faqs
    }));
  };

  const faqSection = useMemo(() => (
    <CollapsibleSection title={t('common.faq_section.title')}>
      <Text style={styles.sectionSubtitle}>{t('common.faq_section.subtitle')}</Text>
      <FAQSection
        housingUuid={listingUuid}
        initialFaqs={formData.faqs || []}
        onFaqsChange={handleFaqsChange}
      />
    </CollapsibleSection>
  ), [listingUuid, formData.faqs, t]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ScrollView style={styles.scrollContainer}>
        {generalInfoSection}
        {photoSection}
        {locationSection}
        {mediaSection}
        {poiSection}
        {faqSection}

        {useMemo(() => (
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            {isLoading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.submitButtonText}>{t('common.accommodation_update_screen.update_listing')}</Text>
            )}
          </TouchableOpacity>
        ), [handleSubmit, isLoading, t])}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f8f8' },
  scrollContainer: { flex: 1, paddingTop: 16, marginBottom: 16 },
  backButton: { padding: 8 },
  publishButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginRight: 8
  },
  publishButtonText: { color: 'white', fontWeight: '600' },
  submitButton: {
    backgroundColor: '#4A90E2', padding: 16, borderRadius: 8, margin: 16, alignItems: 'center',
  },
  submitButtonText: { color: 'white', fontWeight: '600', fontSize: 16 },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 14,
    color: '#666',
  },
  retryButton: {
    backgroundColor: '#4A90E2',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default AccommodationUpdateScreen;
