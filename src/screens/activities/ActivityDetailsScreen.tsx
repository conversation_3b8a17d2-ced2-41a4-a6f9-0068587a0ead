import { View, Text, ScrollView, Image, StyleSheet, TouchableOpacity, Linking, Platform, Dimensions } from 'react-native';
import React, { useEffect, useState } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
  interpolate,
  Extrapolation,
  SlideInRight,
  FadeIn,
  runOnJS,
} from 'react-native-reanimated';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

// Dummy data simulating a response from Google Places API
const DUMMY_PLACE_DETAILS = {
  id: '1',
  name: 'Crystal Clear Beach',
  formattedAddress: '123 Shoreline Drive, Oceanview, CA 90210',
  location: { lat: 34.052235, lng: -118.243683 },
  phoneNumber: '+****************',
  website: 'https://example.com/crystalclearbeach',
  rating: 4.8,
  userRatingsTotal: 1243,
  priceLevel: 2,
  openingHours: {
    weekdayText: [
      'Monday: 8:00 AM – 6:00 PM',
      'Tuesday: 8:00 AM – 6:00 PM',
      'Wednesday: 8:00 AM – 6:00 PM',
      'Thursday: 8:00 AM – 6:00 PM',
      'Friday: 8:00 AM – 8:00 PM',
      'Saturday: 9:00 AM – 8:00 PM',
      'Sunday: 10:00 AM – 6:00 PM',
    ],
    isOpen: true,
  },
  photos: [
    'https://images.unsplash.com/photo-1520942702018-0862200e6873',
    'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
    'https://images.unsplash.com/photo-1519046904884-53103b34b206',
    'https://images.unsplash.com/photo-1484821582734-6692f748785b',
  ],
  reviews: [
    {
      authorName: 'Emma Johnson',
      rating: 5,
      relativeTimeDescription: '2 weeks ago',
      text: 'Absolutely gorgeous beach! The water is crystal clear as advertised. Great facilities and the staff are very friendly. Highly recommend visiting early in the morning to avoid crowds.',
      profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=60&h=60&fit=crop',
    },
    {
      authorName: 'Michael Chen',
      rating: 4,
      relativeTimeDescription: '1 month ago',
      text: 'Beautiful location with amazing views. Lost one star because the parking can be difficult on weekends. The water is perfect and there are great spots for picnics.',
      profilePhotoUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop',
    },
    {
      authorName: 'Sarah Williams',
      rating: 5,
      relativeTimeDescription: '3 months ago',
      text: 'One of the best beaches I\'ve visited. Clean, well-maintained, and the surrounding area has excellent restaurants and shops. Perfect for families!',
      profilePhotoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop',
    },
  ],
  description: 'Crystal Clear Beach is one of the most picturesque beaches on the coast. Known for its pristine white sand and transparent turquoise waters, it offers a perfect getaway for both relaxation and adventure. The beach features several amenities including loungers, umbrellas, water sports equipment rentals, and beachside cafes. Nature lovers will appreciate the nearby walking trails that offer stunning views of the coastline and opportunities to spot local wildlife. The beach is family-friendly with designated swimming areas monitored by lifeguards during peak seasons.',
  amenities: ['Parking', 'Restrooms', 'Showers', 'Food Vendors', 'Lifeguards', 'Water Sports', 'Picnic Areas', 'Wheelchair Accessible'],
};

const ActivityDetailsScreen = ({ route, navigation }) => {
  // In a real app, you would use route.params.activityId to fetch the data
  // const { activityId } = route.params;
  const [placeDetails, setPlaceDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activePhotoIndex, setActivePhotoIndex] = useState(0);
  const [expanded, setExpanded] = useState(false);
  
  const insets = useSafeAreaInsets();
  
  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentOpacity = useSharedValue(0);
  const scrollY = useSharedValue(0);
  const descriptionHeight = useSharedValue(80);
  
  // Simulate API fetch
  useEffect(() => {
    const fetchPlaceDetails = async () => {
      // In a real app, you would fetch from Google Places API
      // const response = await fetch(`https://maps.googleapis.com/maps/api/place/details/json?place_id=${activityId}&key=YOUR_API_KEY`);
      // const data = await response.json();
      
      // Simulate network delay
      setTimeout(() => {
        setPlaceDetails(DUMMY_PLACE_DETAILS);
        setLoading(false);
        
        // Start animations
        headerOpacity.value = withTiming(1, { duration: 600 });
        contentOpacity.value = withTiming(1, { duration: 800 });
      }, 500);
    };
    
    fetchPlaceDetails();
  }, []);
  
  // Toggle description expansion
  const toggleDescription = () => {
    setExpanded(!expanded);
    descriptionHeight.value = withSpring(
      expanded ? 80 : 1000, 
      { damping: 14 }
    );
  };
  
  // Handle navigation back
  const handleBack = () => {
    // Animation before navigating back
    headerOpacity.value = withTiming(0, { duration: 300 });
    contentOpacity.value = withTiming(0, { duration: 200 }, () => {
      runOnJS(navigation.goBack)();
    });
  };
  
  // Handle opening maps app
  const openMaps = () => {
    if (!placeDetails) return;
    
    const { lat, lng } = placeDetails.location;
    const label = placeDetails.name;
    
    const scheme = Platform.select({ ios: 'maps:0,0?q=', android: 'geo:0,0?q=' });
    const latLng = `${lat},${lng}`;
    const url = Platform.select({
      ios: `${scheme}${label}@${latLng}`,
      android: `${scheme}${latLng}(${label})`
    });
    
    Linking.openURL(url);
  };
  
  // Handle phone call
  const callPhone = () => {
    if (!placeDetails?.phoneNumber) return;
    Linking.openURL(`tel:${placeDetails.phoneNumber}`);
  };
  
  // Handle website visit
  const openWebsite = () => {
    if (!placeDetails?.website) return;
    Linking.openURL(placeDetails.website);
  };
  
  // Change active photo
  const changePhoto = (index) => {
    setActivePhotoIndex(index);
  };
  
  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [
      { translateY: interpolate(headerOpacity.value, [0, 1], [-50, 0]) }
    ]
  }));
  
  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
    transform: [
      { translateY: interpolate(contentOpacity.value, [0, 1], [50, 0]) }
    ]
  }));
  
  const descriptionAnimatedStyle = useAnimatedStyle(() => ({
    height: descriptionHeight.value,
    overflow: 'hidden'
  }));
  
  const headerScrollStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(
        scrollY.value,
        [0, 100],
        [0, 1],
        Extrapolation.CLAMP
      ),
      transform: [
        { translateY: interpolate(
          scrollY.value, 
          [0, 100], 
          [-20, 0],
          Extrapolation.CLAMP
        )}
      ]
    };
  });
  
  // Render loading state
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { paddingTop: insets.top }]}>
        <Animated.View
          entering={FadeIn.duration(800)}
          style={styles.loadingIndicator}
        >
          <FontAwesome name="spinner" size={32} color="#3498DB" />
          <Text style={styles.loadingText}>Loading place details...</Text>
        </Animated.View>
      </View>
    );
  }
  
  // Generate the star rating display
  const renderRating = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;
    
    return (
      <View style={styles.ratingContainer}>
        {[...Array(5)].map((_, i) => (
          <Ionicons
            key={i}
            name={
              i < fullStars
                ? 'star'
                : i === fullStars && hasHalfStar
                ? 'star-half'
                : 'star-outline'
            }
            size={16}
            color="#FFD700"
            style={{ marginRight: 2 }}
          />
        ))}
        <Text style={styles.ratingText}>{rating.toFixed(1)}</Text>
        <Text style={styles.ratingsCount}>
          ({placeDetails.userRatingsTotal} reviews)
        </Text>
      </View>
    );
  };
  
  // Render price level
  const renderPriceLevel = (level) => {
    return (
      <View style={styles.priceLevelContainer}>
        {[...Array(4)].map((_, i) => (
          <Text 
            key={i} 
            style={{
              color: i < level ? '#3498DB' : '#cccccc',
              fontWeight: 'bold',
              marginRight: 1
            }}
          >
            $
          </Text>
        ))}
      </View>
    );
  };
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Floating header for scrolled state */}
      <Animated.View 
        style={[
          styles.floatingHeader, 
          headerScrollStyle,
          { top: insets.top }
        ]}
      >
        <Text style={styles.floatingHeaderTitle} numberOfLines={1}>
          {placeDetails?.name}
        </Text>
      </Animated.View>
      
      {/* Back button */}
      <TouchableOpacity 
        style={[styles.backButton, { top: insets.top + 10 }]} 
        onPress={handleBack}
      >
        <Ionicons name="arrow-back" size={24} color="white" />
      </TouchableOpacity>
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        onScroll={(event) => {
          scrollY.value = event.nativeEvent.contentOffset.y;
        }}
        scrollEventThrottle={16}
      >
        {/* Hero Image Carousel */}
        <View style={styles.imageContainer}>
          <Animated.Image 
            source={{ uri: `${placeDetails?.photos[activePhotoIndex]}?w=800&auto=format` }}
            style={[styles.heroImage, headerAnimatedStyle]}
            resizeMode="cover"
          />
          
          {/* Photo selector dots */}
          <View style={styles.photoIndicators}>
            {placeDetails?.photos.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => changePhoto(index)}
                style={[
                  styles.photoIndicator,
                  activePhotoIndex === index && styles.photoIndicatorActive
                ]}
              />
            ))}
          </View>
          
          {/* Open Now indicator */}
          {placeDetails?.openingHours?.isOpen && (
            <Animated.View 
              entering={SlideInRight.delay(400)}
              style={styles.openNowBadge}
            >
              <Text style={styles.openNowText}>OPEN NOW</Text>
            </Animated.View>
          )}
        </View>
        
        {/* Content */}
        <Animated.View style={[styles.contentContainer, contentAnimatedStyle]}>
          {/* Header info */}
          <View style={styles.headerInfo}>
            <Text style={styles.placeName}>{placeDetails?.name}</Text>
            <View style={styles.ratingRow}>
              {renderRating(placeDetails?.rating)}
              {renderPriceLevel(placeDetails?.priceLevel)}
            </View>
          </View>
          
          {/* Quick actions */}
          <View style={styles.quickActions}>
            <AnimatedTouchable 
              entering={FadeIn.delay(300).duration(500)}
              style={styles.actionButton}
              onPress={openMaps}
            >
              <View style={[styles.actionIcon, { backgroundColor: 'rgba(52, 152, 219, 0.1)' }]}>
                <Ionicons name="navigate" size={22} color="#3498DB" />
              </View>
              <Text style={styles.actionText}>Directions</Text>
            </AnimatedTouchable>
            
            <AnimatedTouchable 
              entering={FadeIn.delay(400).duration(500)}
              style={styles.actionButton}
              onPress={callPhone}
            >
              <View style={[styles.actionIcon, { backgroundColor: 'rgba(46, 204, 113, 0.1)' }]}>
                <Ionicons name="call" size={22} color="#2ecc71" />
              </View>
              <Text style={styles.actionText}>Call</Text>
            </AnimatedTouchable>
            
            <AnimatedTouchable 
              entering={FadeIn.delay(500).duration(500)}
              style={styles.actionButton}
              onPress={openWebsite}
            >
              <View style={[styles.actionIcon, { backgroundColor: 'rgba(231, 76, 60, 0.1)' }]}>
                <Ionicons name="globe" size={22} color="#e74c3c" />
              </View>
              <Text style={styles.actionText}>Website</Text>
            </AnimatedTouchable>
          </View>
          
          {/* Address */}
          <View style={styles.infoSection}>
            <View style={styles.sectionTitleRow}>
              <Ionicons name="location" size={18} color="#3498DB" />
              <Text style={styles.sectionTitle}>Address</Text>
            </View>
            <Text style={styles.addressText}>{placeDetails?.formattedAddress}</Text>
          </View>
          
          {/* Description */}
          <View style={styles.infoSection}>
            <View style={styles.sectionTitleRow}>
              <Ionicons name="information-circle" size={18} color="#3498DB" />
              <Text style={styles.sectionTitle}>About</Text>
            </View>
            <Animated.View style={descriptionAnimatedStyle}>
              <Text style={styles.descriptionText}>
                {placeDetails?.description}
              </Text>
            </Animated.View>
            <TouchableOpacity 
              style={styles.readMoreButton} 
              onPress={toggleDescription}
            >
              <Text style={styles.readMoreText}>
                {expanded ? 'Read less' : 'Read more'}
              </Text>
              <Ionicons 
                name={expanded ? 'chevron-up' : 'chevron-down'} 
                size={16} 
                color="#3498DB" 
              />
            </TouchableOpacity>
          </View>
          
          {/* Hours */}
          <View style={styles.infoSection}>
            <View style={styles.sectionTitleRow}>
              <Ionicons name="time" size={18} color="#3498DB" />
              <Text style={styles.sectionTitle}>Opening Hours</Text>
            </View>
            {placeDetails?.openingHours?.weekdayText.map((day, index) => (
              <Animated.View 
                key={index}
                entering={FadeIn.delay(300 + index * 50).duration(400)}
                style={styles.hourRow}
              >
                <Text style={styles.dayText}>{day.split(': ')[0]}</Text>
                <Text style={styles.timeText}>{day.split(': ')[1]}</Text>
              </Animated.View>
            ))}
          </View>
          
          {/* Amenities */}
          <View style={styles.infoSection}>
            <View style={styles.sectionTitleRow}>
              <MaterialIcons name="emoji-objects" size={18} color="#3498DB" />
              <Text style={styles.sectionTitle}>Amenities</Text>
            </View>
            <View style={styles.amenitiesContainer}>
              {placeDetails?.amenities.map((amenity, index) => (
                <Animated.View 
                  key={index}
                  entering={FadeIn.delay(200 + index * 50).duration(400)}
                  style={styles.amenityTag}
                >
                  <Text style={styles.amenityText}>{amenity}</Text>
                </Animated.View>
              ))}
            </View>
          </View>
          
          {/* Reviews */}
          <View style={styles.infoSection}>
            <View style={styles.sectionTitleRow}>
              <Ionicons name="star" size={18} color="#3498DB" />
              <Text style={styles.sectionTitle}>Reviews</Text>
            </View>
            
            {placeDetails?.reviews.map((review, index) => (
              <Animated.View 
                key={index}
                entering={FadeIn.delay(400 + index * 100).duration(500)}
                style={styles.reviewCard}
              >
                <View style={styles.reviewHeader}>
                  <Image 
                    source={{ uri: review.profilePhotoUrl }}
                    style={styles.reviewerImage}
                  />
                  <View style={styles.reviewerInfo}>
                    <Text style={styles.reviewerName}>{review.authorName}</Text>
                    <Text style={styles.reviewDate}>{review.relativeTimeDescription}</Text>
                  </View>
                  <View style={styles.reviewRating}>
                    <Text style={styles.reviewRatingText}>{review.rating}</Text>
                    <Ionicons name="star" size={12} color="#FFD700" />
                  </View>
                </View>
                <Text style={styles.reviewText}>{review.text}</Text>
              </Animated.View>
            ))}
          </View>
          
          {/* Bottom spacing */}
          <View style={{ height: 40 }} />
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingIndicator: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#3498DB',
    fontWeight: '500',
  },
  backButton: {
    position: 'absolute',
    left: 16,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatingHeader: {
    position: 'absolute',
    zIndex: 5,
    width: '100%',
    height: 50,
    backgroundColor: 'white',
    justifyContent: 'center',
    paddingHorizontal: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  imageContainer: {
    width: '100%',
    height: 300,
    backgroundColor: '#eee',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  photoIndicators: {
    position: 'absolute',
    bottom: 16,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  photoIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  photoIndicatorActive: {
    backgroundColor: 'white',
    width: 12,
    height: 8,
    borderRadius: 4,
  },
  openNowBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#2ecc71',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
  },
  openNowText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  contentContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  headerInfo: {
    marginBottom: 20,
  },
  placeName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  ratingsCount: {
    fontSize: 12,
    color: '#888',
    marginLeft: 4,
  },
  priceLevelContainer: {
    flexDirection: 'row',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 20,
    paddingVertical: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  actionText: {
    fontSize: 12,
    color: '#666',
  },
  infoSection: {
    marginVertical: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginLeft: 6,
  },
  addressText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  descriptionText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  readMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  readMoreText: {
    color: '#3498DB',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  hourRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  dayText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    width: 100,
  },
  timeText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  amenityTag: {
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  amenityText: {
    color: '#3498DB',
    fontSize: 12,
    fontWeight: '500',
  },
  reviewCard: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  reviewerImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  reviewerInfo: {
    marginLeft: 12,
    flex: 1,
    justifyContent: 'center',
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  reviewDate: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  reviewRatingText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 2,
  },
  reviewText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
});

export default ActivityDetailsScreen;