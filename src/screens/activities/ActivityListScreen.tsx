import { View, Text, FlatList, Image, StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  Easing,
  interpolate,
  runOnJS
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';

// Animated components
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

// Dummy data for the list of interesting points
const dummyData = [
  {
    id: '1',
    name: 'Crystal Clear Beach',
    image: 'https://images.unsplash.com/photo-1520942702018-0862200e6873',
    rating: 4.8,
    distance: 2.3,
  },
  {
    id: '2',
    name: 'Mountain Vista Trail',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
    rating: 4.5,
    distance: 5.7,
  },
  {
    id: '3',
    name: 'Historic Downtown',
    image: 'https://images.unsplash.com/photo-1514924013411-cbf25faa35bb',
    rating: 4.2,
    distance: 1.8,
  },
  {
    id: '4',
    name: 'Sunset Cliffs Park',
    image: 'https://images.unsplash.com/photo-1530122037265-a5f1f91d3b99',
    rating: 4.9,
    distance: 3.5,
  },
  {
    id: '5',
    name: 'Botanical Gardens',
    image: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae',
    rating: 4.6,
    distance: 4.2,
  },
  {
    id: '6',
    name: 'City Observation Deck',
    image: 'https://images.unsplash.com/photo-1496588152823-86ff7695e68f',
    rating: 4.3,
    distance: 6.1,
  },
  {
    id: '7',
    name: 'Lakeside Park',
    image: 'https://images.unsplash.com/photo-1539593395743-7da5ee10ff07',
    rating: 4.7,
    distance: 2.8,
  },
  {
    id: '8',
    name: 'Riverfront Walk',
    image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c',
    rating: 4.4,
    distance: 3.2,
  },
];

const ActivityListScreen = ({ navigation }) => {
  const [activities, setActivities] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const headerOpacity = useSharedValue(0);
  const listScale = useSharedValue(0.95);

  // Initial load animation
  useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 600 });
    listScale.value = withSpring(1, { damping: 14 });
    
    // Simulate loading data
    loadData();
  }, []);

  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Simulate a refresh every time the screen comes into focus
      loadData();
      return () => {};
    }, [])
  );

  // Simulate data loading with delay
  const loadData = () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      // Randomize the order for refresh effect
      setActivities([...dummyData].sort(() => Math.random() - 0.5));
      setRefreshing(false);
    }, 1200);
  };

  // Handle pull to refresh
  const onRefresh = useCallback(() => {
    loadData();
  }, []);

  // Handle item press
  const handleItemPress = (item) => {
    // Navigate to detail screen (you would implement this)
    console.log(`Pressed: ${item.name}`);
    navigation.navigate('ActivityDetails', { activityId: item.id });
  };

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
      transform: [
        { translateY: interpolate(headerOpacity.value, [0, 1], [-30, -15]) }
      ]
    };
  });

  const listAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: listScale.value }]
    };
  });

  // Individual item component with animations
  const ActivityItem = ({ item, index }) => {
    const itemOpacity = useSharedValue(0);
    const itemScale = useSharedValue(0.9);
    const pressed = useSharedValue(false);

    // Staggered animation on initial load
    useEffect(() => {
      const delay = index * 100;
      setTimeout(() => {
        itemOpacity.value = withTiming(1, { duration: 500 });
        itemScale.value = withSpring(1);
      }, delay);
    }, []);

    // Animation styles for press feedback
    const animatedItemStyle = useAnimatedStyle(() => {
      return {
        opacity: itemOpacity.value,
        transform: [
          { scale: itemScale.value },
          { scale: pressed.value ? 0.97 : 1 }
        ],
        backgroundColor: pressed.value ? 'rgba(209, 152, 124, 0.1)' : 'white',
      };
    });

    // Generate the star rating display
    const renderRating = () => {
      const fullStars = Math.floor(item.rating);
      const hasHalfStar = item.rating - fullStars >= 0.5;
      
      return (
        <View style={styles.ratingContainer}>
          {[...Array(5)].map((_, i) => (
            <Ionicons
              key={i}
              name={
                i < fullStars
                  ? 'star'
                  : i === fullStars && hasHalfStar
                  ? 'star-half'
                  : 'star-outline'
              }
              size={16}
              color="#FFD700"
              style={{ marginRight: 2 }}
            />
          ))}
          <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
        </View>
      );
    };

    return (
      <AnimatedTouchable
        style={[styles.itemContainer, animatedItemStyle]}
        onPress={() => handleItemPress(item)}
        onPressIn={() => {
          pressed.value = withTiming(true, { duration: 100 });
        }}
        onPressOut={() => {
          pressed.value = withTiming(false, { duration: 200 });
        }}
      >
        <Image 
          source={{ uri: `${item.image}?w=500&auto=format&fit=crop` }} 
          style={styles.itemImage} 
          resizeMode="cover"
        />
        <View style={styles.itemDetails}>
          <Text style={styles.itemName}>{item.name}</Text>
          {renderRating()}
          <View style={styles.distanceContainer}>
            <Ionicons name="location" size={16} color="#3498DB" />
            <Text style={styles.distanceText}>{item.distance.toFixed(1)} km away</Text>
          </View>
        </View>
      </AnimatedTouchable>
    );
  };

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <View>
          <Text style={styles.headerTitle}>Discover</Text>
          <Text style={styles.headerSubtitle}>Interesting places nearby</Text>
        </View>
        <TouchableOpacity
          style={styles.mapButton}
          onPress={() => navigation.navigate('ActivityMap')}
        >
          <Ionicons name="map" size={16} color="white" style={{ marginRight: 8 }} />
          <Text style={styles.mapButtonText}>View in map</Text>
        </TouchableOpacity>
      </Animated.View>
      
      <Animated.View style={[styles.listContainer, listAnimatedStyle]}>
        <FlatList
          data={activities}
          keyExtractor={(item) => item.id}
          renderItem={({ item, index }) => <ActivityItem item={item} index={index} />}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#3498DB"
              colors={["#3498DB", "#D1987C"]}
            />
          }
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    paddingTop: 40,
    backgroundColor: '#D1987C',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  listContainer: {
    flex: 1,
    marginTop: -20,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    backgroundColor: '#f8f9fa',
    overflow: 'hidden',
  },
  listContent: {
    padding: 16,
    paddingTop: 24,
  },
  itemContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  itemImage: {
    width: '100%',
    height: 180,
  },
  itemDetails: {
    padding: 16,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distanceText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  separator: {
    height: 16,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3498DB',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  mapButtonText: {
    color: 'white',
    fontSize: 16,
  },
});

export default ActivityListScreen;