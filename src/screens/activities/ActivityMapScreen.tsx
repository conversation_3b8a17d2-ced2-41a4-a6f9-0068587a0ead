import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions, Platform, TouchableOpacity } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withSpring,
  Easing,
  interpolateColor,
  runOnJS
} from 'react-native-reanimated';
import { MaterialIcons } from '@expo/vector-icons';
import useListingStore from '@/stores/useListingStore';
import PointsOfInterestPreview from '@/components/PointsOfInterestPreview';
import { openInMaps } from '@/utils/util';
import PoiMarker from '@/components/PoiMarker';
import ListingMarker from '@/components/ListingMarker';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');

interface POI {
  name: string;
  address: string;
  placeId?: string;
  types?: string[];
  photo?: string;
  latitude?: number;
  longitude?: number;
  location?: {
    latitude: number;
    longitude: number;
  };
}

interface POIWithLocation extends POI {
  latitude: number;
  longitude: number;
}

interface AnimatedMarkerProps {
  poi: POIWithLocation;
  isSelected: boolean;
  onPress: (poi: POIWithLocation) => void;
}

// Animated marker component using Reanimated
const AnimatedMarker: React.FC<AnimatedMarkerProps> = ({ poi, isSelected, onPress }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      backgroundColor: isSelected ? '#D1987C' : '#3498DB',
    };
  });

  // Animate when selected status changes
  React.useEffect(() => {
    if (isSelected) {
      scale.value = withSequence(
        withTiming(1.3, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );
    }
  }, [isSelected]);

  return (
    <Marker
      coordinate={{
        latitude: poi.latitude,
        longitude: poi.longitude
      }}
      onPress={(event) => {
        event.stopPropagation();
        onPress(poi);
      }}
      tracksViewChanges={false}
    >
      <Animated.View style={[styles.markerContainer, animatedStyle]}>
        <MaterialIcons name="home" size={18} color="white" />
        <Text style={styles.markerText}>{poi.name.charAt(0)}</Text>
      </Animated.View>
    </Marker>
  );
};

const ActivityMapScreen = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const {
    setListing,
    setIsLoading,
    setIsError,
    listing,
    isLoading,
    isError
  } = useListingStore();

  const bottomSheetRef = useRef<BottomSheet>(null);
  const mapRef = useRef<MapView>(null);
  const insets = useSafeAreaInsets();
  const [selectedPOI, setSelectedPOI] = useState<POIWithLocation | null>(null);
  // Initialize bottom sheet to be open if there are POIs, closed otherwise
  const [bottomSheetIndex, setBottomSheetIndex] = useState(-1); // Start with -1 (will be updated in useEffect)

  // Button animation
  const buttonScale = useSharedValue(1);

  const snapPoints = React.useMemo(() => ['25%', '75%'], []);

  // Helper function to check if there are POIs
  const hasPOIs = useCallback(() => {
    return Boolean(listing?.pointsOfInterest && listing.pointsOfInterest.length > 0);
  }, [listing?.pointsOfInterest]);

  const animatedButtonStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }]
    };
  });

  // Set the initial map region based on listing location when it's available
  useEffect(() => {
    if (listing && listing.latitude && listing.longitude) {
      // Initialize map at listing coordinates
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          latitude: listing.latitude,
          longitude: listing.longitude,
          latitudeDelta: 0.02,
          longitudeDelta: 0.02,
        }, 500);
      }
    }
  }, [listing]);

  // Update bottom sheet index when listing or POIs change
  useEffect(() => {
    // Set bottom sheet index based on POIs availability
    if (hasPOIs()) {
      // Open the bottom sheet to the first snap point (25%)
      setBottomSheetIndex(0);
    } else {
      // Close the bottom sheet if no POIs
      setBottomSheetIndex(-1);
    }
  }, [hasPOIs]);

  const handleMarkerPress = useCallback((poi: POIWithLocation) => {
    console.log('Marker Pressed:', poi);
    setSelectedPOI(poi);

    // Animate map to center on selected marker
    mapRef.current?.animateToRegion({
      latitude: poi.latitude,
      longitude: poi.longitude,
      latitudeDelta: 0.02,
      longitudeDelta: 0.02,
    }, 500);

    // Ensure bottom sheet opens to full height when a marker is selected
    bottomSheetRef.current?.snapToIndex(1);

    // Open in default maps app
    openInMaps(poi);
  }, []);

  const handlePOIPress = useCallback((poi: POI) => {
    console.log('POI Pressed:', poi);
    if (!listing?.pointsOfInterest) return;

    // Find the corresponding POI with location from listing.pointsOfInterest
    const poiWithLocation = listing.pointsOfInterest.find(
      p => p.name === poi.name && p.address === poi.address
    ) as POIWithLocation;

    if (poiWithLocation) {
      setSelectedPOI(poiWithLocation);

      // Center map on this poi
      mapRef.current?.animateToRegion({
        latitude: poiWithLocation.latitude,
        longitude: poiWithLocation.longitude,
        latitudeDelta: 0.0009,
        longitudeDelta: 0.0009,
      }, 500);

      // Open bottom sheet to full height
      bottomSheetRef.current?.snapToIndex(1);
    }
  }, [listing?.pointsOfInterest]);

  // Handlers for bottom sheet events
  const handleSheetChanges = useCallback((index: number) => {
    console.log('handleSheetChanges', index);
    setBottomSheetIndex(index);
  }, []);

  // Prevent closing the bottom sheet if there are POIs
  const handleClose = () => {
    if (hasPOIs()) {
      // If there are POIs, prevent closing and snap back to index 0
      bottomSheetRef.current?.snapToIndex(0);
      return false;
    }
    // Allow closing if no POIs
    return true;
  };

  const onLocationButtonPress = useCallback(() => {
    // Animate button when pressed
    buttonScale.value = withSequence(
      withTiming(0.85, { duration: 150 }),
      withTiming(1, { duration: 150 })
    );

    // Center the map on the listing's location
    if (listing && listing.latitude && listing.longitude) {
      mapRef.current?.animateToRegion({
        latitude: listing.latitude,
        longitude: listing.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      }, 500);
    }
  }, [buttonScale, listing]);

  // Calculate the map bounds to fit all points
  const fitMapToMarkers = useCallback(() => {
    if (!listing || !listing.pointsOfInterest || listing.pointsOfInterest.length === 0) return;

    // Create array of all coordinates including the listing and POIs
    const coordinates = [
      { latitude: listing.latitude, longitude: listing.longitude },
      ...listing.pointsOfInterest.map(poi => ({
        latitude: poi.latitude,
        longitude: poi.longitude
      }))
    ];

    // Use the map's fitToCoordinates method
    if (mapRef.current) {
      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 50, right: 50, bottom: 200, left: 50 },
        animated: true
      });
    }
  }, [listing]);

  // Fit map to markers on initial load
  useEffect(() => {
    if (listing && listing.pointsOfInterest && listing.pointsOfInterest.length > 0) {
      // Small delay to ensure map is ready
      const timer = setTimeout(() => {
        fitMapToMarkers();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [listing, fitMapToMarkers]);

  if (!listing) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text>{t('common.activity_map_screen.loading')}</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <View style={styles.container}>
        <MapView
          ref={mapRef}
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          initialRegion={{
            latitude: listing?.latitude || 37.78825,
            longitude: listing?.longitude || -122.4324,
            latitudeDelta: 0.02,
            longitudeDelta: 0.02,
          }}
          showsUserLocation
          showsMyLocationButton={false}
          customMapStyle={[
            {
              elementType: 'labels',
              stylers: [{ visibility: 'off' }],
            },
          ]}
          showsPointsOfInterest={false}
        >
          {/* Primary listing marker */}
          <ListingMarker
            listing={{
              latitude: listing.latitude,
              longitude: listing.longitude,
              title: listing.title || t('common.activity_map_screen.accommodation'),
              address: listing.address || ''
            }}
            onPress={(listingData) => {
              // Open in maps
              openInMaps(listingData);
            }}
          />

          {/* Points of interest markers */}
          {listing.pointsOfInterest && listing.pointsOfInterest.length > 0 ? (
            <>
              {console.log('POIs to render:', JSON.stringify(listing.pointsOfInterest))}
              {listing.pointsOfInterest.map((poi: any) => {
                // Debug each POI
                console.log('POI data:', JSON.stringify(poi));
                console.log('POI has latitude?', Boolean(poi.latitude));
                console.log('POI has longitude?', Boolean(poi.longitude));

                // Check if location is nested
                const hasNestedLocation = poi.location && typeof poi.location === 'object';
                console.log('POI has nested location?', hasNestedLocation);

                // Create a properly structured POI object
                const poiWithLocation: POIWithLocation = {
                  ...poi,
                  // If latitude/longitude are nested in location, extract them
                  latitude: hasNestedLocation && poi.location?.latitude ?
                    Number(poi.location.latitude) :
                    poi.latitude ? Number(poi.latitude) : 0,
                  longitude: hasNestedLocation && poi.location?.longitude ?
                    Number(poi.location.longitude) :
                    poi.longitude ? Number(poi.longitude) : 0
                };

                console.log('Processed POI:', JSON.stringify(poiWithLocation));

                // Only render if we have valid coordinates
                if (poiWithLocation.latitude && poiWithLocation.longitude) {
                  return (
                    <PoiMarker
                      key={poi.placeId || poi.name}
                      markerId={poi.placeId || poi.name}
                      poi={poiWithLocation}
                      onPress={() => handleMarkerPress(poiWithLocation)}
                    />
                  );
                }
                return null;
              })}
            </>
          ) : (
            <>{console.log('No POIs to render')}</>
          )}
        </MapView>

        {/* Current Location Button with Reanimated */}
        <Animated.View
          style={[
            styles.currentLocationButton,
            { top: insets.top + 10 },
            animatedButtonStyle
          ]}
        >
          <TouchableOpacity
            style={styles.buttonInner}
            onPress={onLocationButtonPress}
            activeOpacity={0.8}
          >
            <MaterialIcons name="place" size={24} color="#3498DB" style={{ fontWeight: 'bold' }} />
          </TouchableOpacity>
        </Animated.View>

        {/* Fit to Markers Button */}
        <Animated.View
          style={[
            styles.fitMarkersButton,
            { top: insets.top + 65 },
          ]}
        >
          <TouchableOpacity
            style={styles.buttonInner}
            onPress={fitMapToMarkers}
            activeOpacity={0.8}
          >
            <MaterialIcons name="fit-screen" size={22} color="#3498DB" style={{ fontWeight: 'bold' }} />
          </TouchableOpacity>
        </Animated.View>

        <BottomSheet
          ref={bottomSheetRef}
          index={bottomSheetIndex}
          snapPoints={snapPoints}
          onChange={handleSheetChanges}
          onClose={handleClose}
          enablePanDownToClose={!hasPOIs()}
          style={styles.bottomSheet}
          handleStyle={styles.bottomSheetHandle}
          handleIndicatorStyle={styles.bottomSheetIndicator}
        >
          <BottomSheetScrollView
            contentContainerStyle={styles.scrollContent}
          >
            <View style={styles.bottomSheetContent}>
              <Text style={styles.bottomSheetTitle}>{t('common.activity_map_screen.title')}</Text>
              <PointsOfInterestPreview
                listing={listing}
                onPOIPress={handlePOIPress}
              />
            </View>
          </BottomSheetScrollView>
        </BottomSheet>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 16,
  },
  bottomSheetContent: {
    padding: 16,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  markerContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#3498DB',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  listingMarkerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#D1987C',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  markerText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  bottomSheetBackground: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 16,
  },
  bottomSheetHandle: {
    paddingTop: 8,
    paddingBottom: 0,
  },
  bottomSheetIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#D1987C',
    borderRadius: 2,
  },
  bottomSheetHeader: {
    paddingHorizontal: 20,
    paddingTop: 8,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  bottomSheetSubtitle: {
    fontSize: 13,
    color: '#666',
    marginTop: 4,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 30,
  },
  currentLocationButton: {
    position: 'absolute',
    right: 16,
    backgroundColor: 'white',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  fitMarkersButton: {
    position: 'absolute',
    right: 16,
    backgroundColor: 'white',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  buttonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  poiCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginVertical: 6,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedPoiCard: {
    borderWidth: 2,
    borderColor: '#D1987C',
  },
  poiIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#3498DB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  poiIconText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  poiTextContainer: {
    flex: 1,
  },
  poiName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  poiAddress: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  noPoisContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  noPoisText: {
    fontSize: 16,
    color: '#999',
    marginTop: 8,
  },
});

export default ActivityMapScreen;