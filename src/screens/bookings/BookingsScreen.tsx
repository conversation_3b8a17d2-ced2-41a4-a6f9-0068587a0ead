import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInUp, FadeInRight, FadeIn, Easing } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useTranslation } from 'react-i18next';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Booking {
  uuid: string;
  housing: {
    uuid: string;
  };
  client?: {
    displayName: string;
    phoneNumber: string;
    email: string;
  };
  arrivalDate: string;
  departureDate: string;
  adultCount: number;
  childCount: number;
}

interface Housing {
  uuid: string;
  title: string;
  coverImage?: string;
}

interface ApiResponse<T> {
  data: T;
}

type RootStackParamList = {
  BookingDetails: { bookingUuid: string };
  BookingCreation: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Helper function to format date for display with localization
const formatDateOnly = (dateTimeString: string, locale?: string): string => {
  if (!dateTimeString) return '';

  try {
    const datePart = dateTimeString.split(' ')[0];
    const dateLocale = locale === 'fr' ? fr : undefined;
    const pattern = locale === 'fr' ? 'd MMM' : 'MMM d';
    return format(parseISO(datePart), pattern, { locale: dateLocale });
  } catch (error) {
    // Fallback to basic formatting
    const datePart = dateTimeString.split(' ')[0];
    const date = new Date(datePart);
    return date.toLocaleDateString(locale || 'en-US', {
      month: 'short',
      day: 'numeric'
    });
  }
};

// Helper function to format time for display
const formatTimeOnly = (dateTimeString: string): string => {
  if (!dateTimeString || !dateTimeString.includes(' ')) return '';

  const timePart = dateTimeString.split(' ')[1];
  if (!timePart) return '';

  // Remove seconds from time (HH:MM:SS -> HH:MM)
  return timePart.split(':').slice(0, 2).join(':');
};

const fetchBookings = async (): Promise<Booking[]> => {
  try {
    const response: any = await httpClient.get('/housings/reservations/getall');
    return response.data as Booking[] || [];
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return [];
  }
};

const fetchHousingDetails = async (uuid: string): Promise<Housing> => {
  try {
    console.log('fetching housing details for', uuid);
    const response: any = await httpClient.get(`/housings/getone/${uuid}`);
    return response.data as Housing;
  } catch (error) {
    console.error('Error fetching housing details:', error);
    return {
      uuid,
      title: 'Unknown Housing',
      coverImage: 'https://via.placeholder.com/100'
    };
  }
};

interface BookingCardProps {
  booking: Booking;
  index: number;
  onPress: (booking: Booking) => void;
}

const BookingCard = ({ booking, index, onPress }: BookingCardProps) => {
  const { t, i18n } = useTranslation();
  const { data: housing, isLoading, isError } = useQuery({
    queryKey: ['housing', booking.housing.uuid],
    queryFn: () => fetchHousingDetails(booking.housing.uuid),
    enabled: !!booking.housing.uuid,
  });

  if (isLoading) {
    return <ActivityIndicator size="small" color="#3498DB" />;
  }

  if (isError || !housing) {
    return <Text>{t('error')}</Text>;
  }

  return (
    <Animated.View
      entering={FadeIn.duration(300)
        .delay(index * 150)
        .easing(Easing.out(Easing.ease))
        .withInitialValues({ transform: [{ translateY: 20 }] })}
    >
      <TouchableOpacity 
        onPress={() => onPress(booking)} 
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['#3498DB', '#D1987C']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.card}
        >
          <Image 
            source={{ uri: housing.coverImage || 'https://via.placeholder.com/100' }} 
            style={styles.cardImage} 
            resizeMode="cover" 
          />
          <View style={styles.cardContent}>
            <View style={styles.headerSection}>
              <Text style={styles.title} numberOfLines={1}>{housing.title}</Text>
              <View style={styles.guestBadge}>
                <Ionicons name="people" size={12} color="#fff" />
                <Text style={styles.guestCount}>
                  {booking.adultCount + booking.childCount}
                </Text>
              </View>
            </View>

            <Text style={styles.clientName}>
              {booking.client ? booking.client.displayName : t('unknownClient')}
            </Text>
            <Text style={styles.clientContact} numberOfLines={1}>
              {booking.client ? booking.client.email : t('noEmail')}
            </Text>

            <View style={styles.dateSection}>
              <View style={styles.dateRow}>
                <Text style={styles.dateLabel}>{t('arrival')}: </Text>
                <Text style={styles.dateValue}>
                  {formatDateOnly(booking.arrivalDate, i18n.language)} {formatTimeOnly(booking.arrivalDate)}
                </Text>
              </View>
              <View style={styles.dateRow}>
                <Text style={styles.dateLabel}>{t('departure')}: </Text>
                <Text style={styles.dateValue}>
                  {formatDateOnly(booking.departureDate, i18n.language)} {formatTimeOnly(booking.departureDate)}
                </Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const BookingsScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  const { t } = useTranslation();
  const { data: bookings, isLoading, isError, refetch } = useQuery({ 
    queryKey: ['bookings'], 
    queryFn: fetchBookings 
  });

  useFocusEffect(
    React.useCallback(() => {
      refetch();
      // Status bar style is handled globally by SystemBars in App.tsx
    }, [])
  );
  
  useEffect(() => {
    navigation.setOptions({
      title: t('title'),
    });
  }, [t]);

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#3498DB" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>{t('error')}</Text>
        <TouchableOpacity onPress={() => refetch()} style={styles.retryButton}>
          <Text style={styles.retryText}>{t('retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={bookings}
        keyExtractor={(item) => item.uuid}
        renderItem={({ item, index }) => (
          <BookingCard 
            booking={item} 
            index={index} 
            onPress={(booking) => navigation.navigate('BookingDetails', { bookingUuid: booking.uuid })} 
          />
        )}
        refreshing={isLoading}
        onRefresh={refetch}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={<Text style={styles.emptyText}>{t('noBookings')}</Text>}
      />
      <Animated.View style={styles.fab} entering={FadeInUp.delay(500).duration(500)}>
        <TouchableOpacity 
          style={styles.fabButton} 
          onPress={() => navigation.navigate('BookingCreation')}
        >
          <Ionicons name="add" size={28} color="#fff" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa', padding: 12 },
  listContent: {
    paddingBottom: 80,
    paddingHorizontal: 8,
    paddingTop: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#e74c3c',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#3498DB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  card: {
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 16,
    height: 140,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 10,
    elevation: 5,
  },
  cardImage: {
    width: 100,
    height: '100%',
  },
  cardContent: {
    flex: 1,
    padding: 14,
    justifyContent: 'center',
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    marginRight: 8,
  },
  guestBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.25)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  guestCount: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 4,
  },
  clientName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 3,
  },
  clientContact: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.85)',
    marginBottom: 8,
  },
  dateSection: {
    marginTop: 4,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 3,
  },
  dateLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
    minWidth: 55,
  },
  dateValue: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
    flex: 1,
  },
  fab: {
    position: 'absolute',
    bottom: 25,
    right: 25,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3498DB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 6,
  },
});

export default BookingsScreen;
