import { useNavigation, useFocusEffect } from '@react-navigation/native';
import React, { useMemo, useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  FlatList,
  Image,
  StatusBar,
  Platform,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSpring,
  FadeIn,
  FadeOut,
  SlideInRight,
  Layout
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { collection, query, orderBy, onSnapshot } from 'firebase/firestore';
import { firestoreDatabase } from 'firebaseConfig';
import { useTranslation } from 'react-i18next';

// Types
interface Message {
  id: string;
  userName?: string;
  lastMessage?: string;
  timestamp?: number;
  avatarUrl?: string;
  isOnline?: boolean;
  unreadCount?: number;
  conversationId?: string;
  participants?: Array<{
    displayName: string;
    photoURL?: string;
    online?: boolean;
  }>;
}

interface ChatListItemProps {
  item: Message;
  onPress: (id: string) => void;
  currentUserName: string;
  isNew?: boolean;
}

// Helper function for time formatting
const getRelativeTime = (timestamp?: number, t: any): string => {
  if (!timestamp) return t('chat.unknown');

  const now = new Date();
  const messageDate = new Date(timestamp * 1000); // Convert from seconds to milliseconds
  const diffTime = now.getTime() - messageDate.getTime(); // Difference in milliseconds
  const diffSeconds = Math.floor(diffTime / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 10) return t('chat.justNow');
  if (diffMinutes < 1) return t('chat.secondsAgo', { seconds: diffSeconds });
  if (diffHours < 1) return t('chat.minutesAgo', { minutes: diffMinutes });
  if (diffDays < 1) return t('chat.hoursAgo', { hours: diffHours });
  if (diffDays < 7) return t('chat.daysAgo', { days: diffDays });

  // Format the date properly
  return messageDate.toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

// Get initials from name
const getInitials = (name: string): string => {
  if (!name) return '?';
  
  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// Chat List Item Component
const ChatListItem: React.FC<ChatListItemProps> = React.memo(({ 
  item, 
  onPress, 
  currentUserName,
  isNew = false
}) => {
  const { t } = useTranslation();
  const scale = useSharedValue(1);
  const [lastMessage, setLastMessage] = useState(item.lastMessage || t('chat.noMessages'));
  const [timestamp, setTimestamp] = useState(item.timestamp || Math.floor(Date.now() / 1000));
  const [unreadCount, setUnreadCount] = useState(item.unreadCount || 0);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const participantName = item.participants
    ?.filter(p => p.displayName !== currentUserName)
    .map(p => p.displayName)
    .join(', ');

  const isAnyParticipantOnline = item.participants?.some(p => 
    p.displayName !== currentUserName && p.online
  );

  const truncatedMessage = lastMessage.length > 40 
    ? `${lastMessage.substring(0, 40)}...` 
    : lastMessage;

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    scale.value = withTiming(0.95, { duration: 100 });
    setTimeout(() => {
      scale.value = withSpring(1);
      onPress(item.conversationId);
    }, 100);
  };

  const participantAvatar = item.participants?.find(
    p => p.displayName !== currentUserName
  )?.photoURL;

  const participantInitials = getInitials(participantName || '');

  // Listen for new messages in this conversation
  useEffect(() => {
    if (item.conversationId) {
      const path = `conversations/${item.conversationId}/messages`;
      const q = query(
        collection(firestoreDatabase, path),
        orderBy('timestamp', 'desc')
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        if (!snapshot.empty) {
          const latestMessage = snapshot.docs[0].data();
          setLastMessage(latestMessage.content);
          setTimestamp(latestMessage.timestamp);

          if (latestMessage.senderDisplayName !== currentUserName) {
            setUnreadCount(prev => prev + 1);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        }
      });

      return () => unsubscribe();
    }
  }, [item.conversationId, currentUserName]);

  return (
    <Animated.View 
      entering={isNew ? FadeIn.duration(400).delay(200) : undefined}
      layout={Layout.springify()}
      style={animatedStyles}
    >
      <TouchableOpacity 
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={[
          styles.chatItem,
          unreadCount > 0 ? styles.unreadChatItem : {}
        ]}>
          <View style={styles.avatarContainer}>
            {participantAvatar ? (
              <Image
                source={{ uri: participantAvatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.initialsContainer]}>
                <Text style={styles.initialsText}>{participantInitials}</Text>
              </View>
            )}
            {isAnyParticipantOnline && <View style={styles.onlineIndicator} />}
          </View>
          
          <View style={styles.messageContent}>
            <View style={styles.messageHeader}>
              <Text style={styles.userName} numberOfLines={1}>{participantName || t('chat.unknown')}</Text>
              <Text style={styles.timestamp}>{getRelativeTime(timestamp, t)}</Text>
            </View>
            
            <View style={styles.messagePreview}>
              <Text 
                numberOfLines={1} 
                ellipsizeMode="tail" 
                style={[
                  styles.lastMessage,
                  unreadCount > 0 ? styles.unreadMessage : {}
                ]}
              >
                {truncatedMessage}
              </Text>
              
              {/* {unreadCount > 0 && (
                <View style={styles.badgeContainer}>
                  <Text style={styles.badgeText}>{unreadCount}</Text>
                </View>
              )} */}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Search Bar Component
const SearchBar = ({ value, onChangeText }) => {
  const { t } = useTranslation();
  const inputRef = useRef(null);
  const focused = useSharedValue(false);
  
  const containerStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: focused.value 
        ? 'rgba(142, 142, 147, 0.18)' 
        : 'rgba(142, 142, 147, 0.12)',
      borderRadius: 10,
    };
  });
  
  return (
    <Animated.View style={[styles.searchContainer, containerStyle]}>
      <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
      <TextInput
        ref={inputRef}
        style={styles.searchInput}
        placeholder={t('chat.search')}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor="#999"
        onFocus={() => {
          focused.value = withTiming(true);
          Haptics.selectionAsync();
        }}
        onBlur={() => {
          focused.value = withTiming(false);
        }}
      />
      {value.length > 0 && (
        <TouchableOpacity 
          onPress={() => {
            onChangeText('');
            inputRef.current?.blur();
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          style={styles.clearButton}
        >
          <Ionicons name="close-circle" size={18} color="#999" />
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

// Empty State Component
const EmptyState = ({ searchQuery }) => {
  const { t } = useTranslation();
  return (
    <Animated.View 
      entering={FadeIn.duration(400)}
      style={styles.emptyContainer}
    >
      <Ionicons 
        name={searchQuery ? "search-outline" : "chatbubble-ellipses-outline"} 
        size={70} 
        color="#DEC48F" 
      />
      <Text style={styles.emptyTitle}>
        {searchQuery ? t('chat.noSearchResults') : t('chat.emptyTitle')}
      </Text>
      <Text style={styles.emptyText}>
        {searchQuery 
          ? t('chat.searchNoResults', { query: searchQuery })
          : t('chat.emptyText')
        }
      </Text>
    </Animated.View>
  );
};

// Main Component
const ChatListScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { setConversationId } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [conversations, setConversations] = useState([]);
  const [newMessageIds, setNewMessageIds] = useState(new Set());
  const refreshAnim = useSharedValue(0);
  const firestoreDatabase = null; // Replace with your Firebase reference
  
  // Animated styles for pull-to-refresh
  const refreshIconStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${refreshAnim.value * 360}deg` }],
    };
  });

  const { 
    data: messages = [], 
    refetch, 
    isFetching,
    isError,
    error 
  } = useQuery({
    queryKey: ['conversations'],
    queryFn: async () => {
      try {
        const res = await httpClient.get('/chat/conversations');
        return Array.isArray(res.data) ? res.data : [];
      } catch (err) {
        console.error('Failed to fetch conversations:', err);
        return [];
      }
    },
    refetchOnWindowFocus: true,
    retry: 2,
    retryDelay: 1000,
  });

  const getUserInfo = async () => {
    try {
      const userInfo = await httpClient.get('users/authenticate/info');
      setDisplayName(userInfo.data.displayName);
      return userInfo;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  React.useEffect(() => {
    getUserInfo();
  }, []);

  // Set up listener for new messages
  useFocusEffect(
    useCallback(() => {
      // This would be your actual implementation with Firebase
      if (messages && messages.length) {
        const conversationIds = messages.map(msg => msg.conversationId);
        console.log("Conversation IDs: ", conversationIds);
        // For each conversation, set up a listener
        const unsubscribes = conversationIds.map(convId => {
          if (!convId) return () => {};
          
          // This would be your actual path with your database
          const path = `conversations/${convId}/messages`;
          
          // If we had proper Firestore access, we'd do something like this:
          // const q = query(
          //   collection(firestoreDatabase, path),
          //   orderBy('timestamp', 'desc'),
          //   limit(1)
          // );
          
          // return onSnapshot(q, (snapshot) => {
          //   // If there's a new message
          //   if (!snapshot.empty) {
          //     const latestMessage = snapshot.docs[0].data();
          //     
          //     // Update the conversations list with the new message
          //     setConversations(prev => {
          //       const updated = [...prev];
          //       const index = updated.findIndex(conv => conv.conversationId === convId);
          //       
          //       if (index !== -1) {
          //         updated[index] = {
          //           ...updated[index],
          //           lastMessage: latestMessage.content,
          //           timestamp: latestMessage.timestamp,
          //         };
          //         
          //         // If it's not from the current user, mark as unread
          //         if (latestMessage.senderDisplayName !== displayName) {
          //           updated[index].unreadCount = (updated[index].unreadCount || 0) + 1;
          //           setNewMessageIds(prev => new Set(prev).add(convId));
          //           Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          //         }
          //       }
          //       
          //       return updated;
          //     });
          //   }
          // });
          
          // Mock return for now
          return () => {};
        });

        return () => {
          unsubscribes.forEach(unsub => unsub && unsub());
        };
      }
    }, [messages, displayName])
  );

  // Process messages when they change
  React.useEffect(() => {
    if (Array.isArray(messages)) {
      // Process messages to include last message and timestamp
      const processedConversations = messages.map(conversation => {
        // This would need to be adjusted based on your actual data structure
        return {
          ...conversation,
          // Add placeholder values that would normally come from your Firestore listeners
          lastMessage: conversation.lastMessage || "Start a conversation",
          timestamp: conversation.timestamp || Math.floor(Date.now() / 1000),
          unreadCount: conversation.unreadCount || 0
        };
      });
      
      setConversations(processedConversations);
    }
  }, [messages]);

  // Memoized filtered conversations
  const filteredConversations = useMemo(() => {
    if (!Array.isArray(conversations)) return [];
    
    return conversations.filter(conversation => {
      const participantNames = conversation.participants
        ?.filter(p => p.displayName !== displayName)
        .map(p => p.displayName.toLowerCase())
        .join(' ');
      
      const query = searchQuery.toLowerCase();
      
      return (
        participantNames?.includes(query) ||
        (conversation.lastMessage || '').toLowerCase().includes(query)
      );
    }).sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0)); // Sort by newest first
  }, [conversations, searchQuery, displayName]);

  const handleMessagePress = (id) => {
    try {
      // Handle message press
      console.log('Message pressed:', id);
      setConversationId(id);
      
      // Remove from new messages if it was there
      if (newMessageIds.has(id)) {
        setNewMessageIds(prev => {
          const updated = new Set(prev);
          updated.delete(id);
          return updated;
        });
        
        // Mark as read in the conversations list
        setConversations(prev => {
          const updated = [...prev];
          const index = updated.findIndex(conv => conv.conversationId === id);
          
          if (index !== -1) {
            updated[index] = {
              ...updated[index],
              unreadCount: 0
            };
          }
          
          return updated;
        });
      }
      
      navigation.navigate('ChatRoom' as never);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const renderItem = ({ item, index }) => {
    if (!item || !item.conversationId) return null;
    
    const isNew = newMessageIds.has(item.conversationId);
    
    return (
      <ChatListItem 
        item={item} 
        onPress={handleMessagePress} 
        currentUserName={displayName}
        isNew={isNew}
      />
    );
  };

  const handleRefresh = async () => {
    // Start rotation animation
    refreshAnim.value = withTiming(1, { duration: 1000 });
    
    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Perform refresh
    await refetch();
    
    // Reset animation
    refreshAnim.value = 0;
  };

  const getUnreadMessageCount = () => {
    if (!Array.isArray(conversations)) return 0;
    return conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);
  };

  const unreadCount = getUnreadMessageCount();

  return (
    <SafeAreaView style={styles.container}>

      
      {/* Search Bar */}
      <SearchBar 
        value={searchQuery}
        onChangeText={setSearchQuery}
      />
      
      {/* Error state */}
      {isError && (
        <Animated.View 
          entering={FadeIn.duration(300)}
          style={styles.errorContainer}
        >
          <Text style={styles.errorText}>
            {t('chat.error')}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton} 
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              refetch();
            }}
          >
            <Text style={styles.retryText}>{t('chat.retry')}</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Loading state */}
      {isFetching && filteredConversations.length === 0 && (
        <Animated.View 
          entering={FadeIn.duration(400)}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#DEC48F" />
          <Text style={styles.loadingText}>{t('chat.loading')}</Text>
        </Animated.View>
      )}

      {/* Empty state */}
      {!isFetching && filteredConversations.length === 0 && !isError && (
        <EmptyState searchQuery={searchQuery} />
      )}

      {/* Messages List */}
      <FlatList
        data={filteredConversations}
        renderItem={renderItem}
        keyExtractor={(item) => item?.conversationId?.toString() || Math.random().toString()}
        contentContainerStyle={[
          styles.listContainer,
          filteredConversations.length === 0 && styles.emptyList
        ]}
        showsVerticalScrollIndicator={false}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={5}
        refreshControl={
          <RefreshControl 
            refreshing={isFetching} 
            onRefresh={handleRefresh}
            colors={['#DEC48F']}
            tintColor="#DEC48F"
          />
        }
      />
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  searchContainer: {
    marginHorizontal: 16,
    marginBottom: 10,
    marginTop: 6,
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 10,
    color: '#000',
  },
  clearButton: {
    padding: 5,
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 34,
    fontWeight: 'bold',
    color: '#000',
  },
  subtitle: {
    fontSize: 16,
    color: '#DEC48F',
    marginTop: 4,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 10,
    backgroundColor: '#F7F7F7',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadChatItem: {
    backgroundColor: '#f9f5ed',
    borderLeftWidth: 3,
    borderLeftColor: '#DEC48F',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#DEC48F', // Fallback color
  },
  initialsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  messageContent: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 13,
    color: '#888',
  },
  messagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    marginRight: 8,
  },
  unreadMessage: {
    color: '#333',
    fontWeight: '500',
  },
  badgeContainer: {
    backgroundColor: '#DEC48F',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 3,
    minWidth: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 300,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    maxWidth: width * 0.7,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: '#DEC48F',
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
});

export default ChatListScreen;