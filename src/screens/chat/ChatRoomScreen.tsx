import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Text,
  Platform,
} from 'react-native';
import { GiftedChat, IMessage, Send, Bubble, Time, InputToolbar } from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { collection, query, orderBy, onSnapshot, doc } from 'firebase/firestore';
import { firestoreDatabase } from '../../../firebaseConfig';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

interface ChatRoomScreenProps {
  navigation: any; // Replace with proper navigation type if available
}

const ChatRoomScreen: React.FC<ChatRoomScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const hostId = 1, hostName = "Marc", hostAvatar = require('../../../assets/avatar.png');
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [displayName, setDisplayName] = useState('');
  const insets = useSafeAreaInsets();
  const { userInfo, conversationId } = useAuthStore();

  interface UserInfoResponse {
    data: {
      displayName: string;
      [key: string]: any;
    }
  }

  interface SendMessageParams {
    conversationId: string;
    content: string;
  }

  interface SendMessageResponse {
    data: any; // Replace 'any' with actual response type if known
  }

  const getUserInfo = async () => {
    try {
      const userInfo = await httpClient.get<UserInfoResponse>('users/authenticate/info');
      setDisplayName(userInfo.data.displayName);
      console.log("displayName: " , userInfo.data.displayName);
      return userInfo;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  useEffect(() => {
      getUserInfo();
    }, []);

  useEffect(() => {
    //console.log("Conversation ID: ", conversationId);
    if (conversationId) {
      console.log("Conversation ID: ", conversationId);
      const path = `conversations/${conversationId}/messages`;
      console.log("Path: ", path);
      const q = query(
        collection(firestoreDatabase, path),
        orderBy('timestamp', 'desc')
      );

      
      const unsubscribe = onSnapshot(q, 
        (snapshot) => {
          console.log("Snapshot: ", snapshot.docs);
          const messagesFirestore = snapshot.docs.map((doc) => {
            
            const firebaseData = doc.data();
            console.log("Doc: ", firebaseData);
            const data = {
              _id: firebaseData?.messageId,
              text: firebaseData?.content || '', // Ensure text exists
              createdAt: new Date(firebaseData?.timestamp * 1000) || new Date(),
              user: {
                _id: firebaseData?.senderDisplayName === displayName ? 1 : 2,
                name:  firebaseData?.senderDisplayName,
                //avatar: 'https://placeimg.com/140/140/any',
              },
              ...firebaseData,
            };

            return data;
          });

          console.log("Messages: ", messagesFirestore);

          setMessages(messagesFirestore); // Reverse to show newest at bottom
        },
        (error) => {
          console.error(t('chat.room.error'), error);
        }
      );

      return () => unsubscribe();
    }
  }, [conversationId, firestoreDatabase, t]);

  useEffect(() => {
    navigation.setOptions({
      title: t('chat.room.title'),
    })
  }, [navigation, t]);
  // Define the mutation for sending messages
  const sendMessageMutation = useMutation<SendMessageResponse, Error, SendMessageParams>({
    mutationFn: async ({ conversationId, content }) => {
      const response = await httpClient.post<SendMessageResponse>(`/chat/send-message/${conversationId}`, { content });
      console.log("Response: ", response);
      return response;
    },
    onSuccess: (data) => {
      // Optionally, you can update the query cache or perform other actions on success
      console.log(t('chat.room.messageSent'), data);
    },
    onError: (error) => {
      // Handle error
      console.error('Error sending message:', error);
    },
  });

  const onSend = useCallback((newMessages: IMessage[] = []) => {
    console.log(newMessages);
    // setMessages(previousMessages =>
    //   GiftedChat.append(previousMessages, newMessages)
    // );

    // Send the message to the server
    newMessages.forEach(message => {
      sendMessageMutation.mutate({
        conversationId,
        content: message.text,
      });

      // Add the message to Firestore
      // addDoc(collection(firestore, `conversations/${conversationId}/messages`), {
      //   ...message,
      //   createdAt: new Date(),
      // });
    });
  }, [conversationId]);



  // Custom bubble component
  const renderBubble = (props: any) => (
    <View>
      {props.currentMessage.user.name && (
        <Text style={props.position === 'left' ? styles.userNameLeft : styles.userNameRight}>
          {props.currentMessage.user.name}
        </Text>
      )}
      <Bubble
        {...props}
        wrapperStyle={{
          left: {
            backgroundColor: '#E5C1B0',
          },
          right: {
            backgroundColor: '#007AFF',
          },
        }}
        textStyle={{
          left: {
            color: '#000',
          },
          right: {
            color: '#fff',
          },
        }}
      />
    </View>
  );

  // Custom send button
  const renderSend = (props: any) => (
    <Send
      {...props}
      containerStyle={styles.sendContainer}
    >
      <View style={styles.sendButton}>
        <Ionicons name="paper-plane-outline" size={24} color="#007AFF" />
      </View>
    </Send>
  );

  // Custom input toolbar
  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.inputPrimary}
    />
  );

  // Custom time component
  const renderTime = (props: any) => (
    <Time
      {...props}
      timeTextStyle={{
        left: styles.timeText,
        right: styles.timeText,
      }}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* {renderHeader()} */}
      <GiftedChat
        messages={messages}
        onSend={onSend}
        user={{
          _id: 1,
        }}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={renderInputToolbar}
        renderTime={renderTime}
        renderAvatar={null}
        showUserAvatar={false}
        alwaysShowSend
        infiniteScroll
        keyboardShouldPersistTaps="handled"
        //bottomOffset={Platform.OS === 'ios' ? insets.bottom : 0}
        textInputProps={{
          placeholder: t('chat.room.typeMessage'),
          placeholderTextColor: '#666',
          multiline: true,
        }}
        timeFormat="HH:mm"
        dateFormat="LL"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  inputToolbar: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 4,
  },
  sendButton: {
    padding: 8,
  },
  sendButtonText: {
    color: '#007AFF',
    fontSize: 24,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  userNameLeft: {
    color: '#DEC48F',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 10,
    marginBottom: 2,
  },
  userNameRight: {
    color: '#2C3E50',
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 10,
    marginBottom: 2,
    textAlign: 'right',
  },
});

export default ChatRoomScreen;