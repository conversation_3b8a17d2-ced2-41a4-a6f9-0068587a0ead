import { useAuthStore } from '@/stores/useAuthStore';
import { httpClient } from '@/utils/http';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  FlatList,
  Image,
  StatusBar,
  Platform,
  TouchableOpacity,
} from 'react-native';

// Types
interface Message {
  id: string;
  userName: string;
  lastMessage: string;
  timestamp: string;
  avatarUrl?: string;
  isOnline: boolean;
  unreadCount?: number;
}

interface ChatListItemProps {
  item: Message;
  onPress: (id: string) => void;
}

// Helper function for time formatting
const getRelativeTime = (timestamp: string): string => {
  // Implement proper time difference logic here
  return '3 min ago'; // Placeholder
};

// Chat List Item Component
const ChatListItem: React.FC<ChatListItemProps> = React.memo(({ item, onPress }) => {
  const { id, userName, lastMessage, timestamp, avatarUrl, isOnline } = item;
  
  return (
    <TouchableOpacity onPress={() => onPress(id)}>
      <View style={styles.chatItem}>
        <View style={styles.avatarContainer}>
          <Image
            source={avatarUrl ? { uri: avatarUrl } : null}  // require('../../../assets/avatar.png')}
            style={styles.avatar}
          />
          {isOnline && <View style={styles.onlineIndicator} />}
        </View>
        <View style={styles.messageContent}>
          <View style={styles.messageHeader}>
            <Text style={styles.userName}>{userName}</Text>
            <Text style={styles.timestamp}>{getRelativeTime(timestamp)}</Text>
          </View>
          <Text 
            numberOfLines={1} 
            ellipsizeMode="tail" 
            style={styles.lastMessage}
          >
            {lastMessage}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
});

// Main Component
const GuestChatListScreen: React.FC = () => {
  const navigation = useNavigation();
  const { userInfo } = useAuthStore();
  let user = JSON.parse(userInfo);
  const [searchQuery, setSearchQuery] = React.useState<string>('');
  const [messages, setMessages] = React.useState<Message[]>([]);

  const exampleMessages: Message[] = [{
    id: "msg1",
    userName: "Alice",
    lastMessage: "Hey, how's it going?",
    timestamp: "2025-02-20T12:34:56Z",
    avatarUrl: "https://example.com/avatars/alice.png",
    isOnline: true,
    unreadCount: 3,
  },
  {
    id: "msg2",
    userName: "Bob",
    lastMessage: "See you later.",
    timestamp: "2025-02-20T11:22:33Z",
    isOnline: false,
  },
  {
    id: "msg3",
    userName: "Charlie",
    lastMessage: "What's up?",
    timestamp: "2025-02-20T10:15:30Z",
    avatarUrl: "https://example.com/avatars/charlie.png",
    isOnline: true,
  },
  {
    id: "msg4",
    userName: "Dana",
    lastMessage: "Good morning!",
    timestamp: "2025-02-20T09:00:00Z",
    isOnline: false,
    unreadCount: 1,
  }];

  // Memoized filtered messages
  const filteredMessages = useMemo(() => {
    return messages.filter(message =>
      message.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      message.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [messages, searchQuery]);

  const handleMessagePress = (id: string) => {
    // Handle message press
    console.log('Message pressed:', id);
    navigation.navigate('ChatRoom' as never);
  };

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['guest-conversations'],
    queryFn: async () => {
      const response = await httpClient.get('/chat/conversations');
      return response.data;
    }
  });

  const initHostContact = () => {
    const res = httpClient.post(`/chat/contact-host/${user?.housing?.id}`);
    console.log("res", res);

  }

  const renderItem = ({ item }: { item: Message }) => (
    <ChatListItem item={item} onPress={handleMessagePress} />
  );
  console.log("userInfo", userInfo);
  console.log("user", user);
  return (
    <SafeAreaView style={styles.container}>
      
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      {!data && (
        <TouchableOpacity style={styles.button} onPress={initHostContact}>
          <Text style={styles.buttonText}>Contact Host</Text>
        </TouchableOpacity>
      )}

      {/* Messages List */}
      <FlatList
        data={data}
        renderItem={(item) => <Text>{item.index}</Text>}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={5}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchInput: {
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 34,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  chatItem: {
    backgroundColor: '#aaaaff44',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  messageContent: {
    flex: 1,
    marginLeft: 12,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  timestamp: {
    fontSize: 14,
    color: '#666',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  button: {
    backgroundColor: '#D1987C',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },
});

export default GuestChatListScreen;