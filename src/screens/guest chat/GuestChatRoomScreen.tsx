import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Text,
  Platform,
} from 'react-native';
import { GiftedChat, IMessage, Send, Bubble, Time, InputToolbar } from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { collection, query, orderBy, onSnapshot, doc } from 'firebase/firestore';
import { firestoreDatabase } from '../../../firebaseConfig';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

const GuestChatRoomScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const hostId = 1;
  const hostName = "Marc";
  const hostAvatar = require('../../../assets/avatar.png');

  interface SendMessageParams {
    conversationId: string;
    content: string;
  }

  interface SendMessageResponse {
    data: any; // Replace 'any' with actual response type if known
  }

  const [messages, setMessages] = useState<IMessage[]>([]);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [displayName, setDisplayName] = useState('');
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const { userInfo, reservation } = useAuthStore();
  let user = JSON.parse(userInfo);
  const reservationInfo = JSON.parse(reservation);
  console.log("reservationInfo: ", reservationInfo?.housing?.id);
  interface UserInfoResponse {
    data: {
      displayName: string;
      [key: string]: any;
    }
  }

  const getUserInfo = async () => {
    try {
      const userInfo = await httpClient.get<UserInfoResponse>('users/authenticate/info');
      setDisplayName(userInfo.data.displayName);
      console.log("displayName: " , userInfo.data.displayName);
      return userInfo;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  const initConversation = async () => {
    const res: any = await httpClient.post(`/chat/contact-host/${reservationInfo?.uuid}`);
    console.log("Conversation: " ,res);
    setConversationId(res.data.conversationId);
  }

  useEffect(() => {
    const init = async () => {
      await getUserInfo();
      await initConversation();
    };
    init();
  }, []);

  useEffect(() => {
    if (!displayName) return; // Only proceed if displayName is available
    //console.log("Conversation ID: ", conversationId);
    if (conversationId) {
      console.log("Conversation ID: ", conversationId);
      const path = `conversations/${conversationId}/messages`;
      console.log("Path: ", path);
      const q = query(
        collection(firestoreDatabase, path),
        orderBy('timestamp', 'desc')
      );

      
      const unsubscribe = onSnapshot(q, 
        (snapshot) => {
          console.log("Snapshot: ", snapshot.docs);
          const messagesFirestore = snapshot.docs.map((doc) => {
            
            const firebaseData = doc.data();
            console.log("Doc: ", firebaseData);
            const data = {
              _id: firebaseData?.messageId,
              text: firebaseData?.content || '', // Ensure text exists
              createdAt: new Date(firebaseData?.timestamp * 1000) || new Date(),
              user: {
                _id: firebaseData?.senderDisplayName !== displayName ? 1 : 2,
                name:  firebaseData?.senderDisplayName,
                //avatar: 'https://placeimg.com/140/140/any',
              },
              ...firebaseData,
            };

            return data;
          });

          console.log("Messages: ", messagesFirestore);

          setMessages(messagesFirestore); // Reverse to show newest at bottom
        },
        (error) => {
          console.error("Error fetching messages:", error);
        }
      );

      return () => unsubscribe();
    }
  }, [conversationId, firestoreDatabase, displayName]);

  // Define the mutation for sending messages
  const sendMessageMutation = useMutation<SendMessageResponse, Error, SendMessageParams>({
    mutationFn: async ({ conversationId, content }) => {
      if (!conversationId) {
        throw new Error(t('chat.error.conversation_required'));
      }
      const response = await httpClient.post<SendMessageResponse>(`/chat/send-message/${conversationId}`, { content });
      console.log("Response: ", response);
      return response;
    },
    onSuccess: (data) => {
      console.log(t('chat.success.message_sent'));
    },
    onError: (error) => {
      console.error(t('chat.error.send_failed'));
    },
  });

  const onSend = useCallback((newMessages: IMessage[] = []) => {
    if (!conversationId) return;

    newMessages.forEach(message => {
      sendMessageMutation.mutate({
        conversationId,
        content: message.text,
      });
    });
  }, [conversationId]);



  // Custom bubble component
  const renderBubble = (props: any) => (
    <View>
      {props.currentMessage.user.name && (
        <Text style={props.position === 'left' ? styles.userNameLeft : styles.userNameRight}>
          {props.currentMessage.user.name}
        </Text>
      )}
      <Bubble
        {...props}
        wrapperStyle={{
          left: {
            backgroundColor: '#E5C1B0',
          },
          right: {
            backgroundColor: '#007AFF',
          },
        }}
        textStyle={{
          left: {
            color: '#000',
          },
          right: {
            color: '#fff',
          },
        }}
      />
    </View>
  );

  // Custom send button
  const renderSend = (props: any) => (
    <Send
      {...props}
      containerStyle={styles.sendContainer}
    >
      <View style={styles.sendButton}>
        <Ionicons name="paper-plane-outline" size={24} color="#007AFF" />
      </View>
    </Send>
  );

  // Custom input toolbar
  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.inputPrimary}
    />
  );

  // Custom time component with locale support
  const renderTime = (props: any) => {
    const formatDate = (date: Date) => {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const messageDate = new Date(date);
      const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate());
      
      if (messageDay.getTime() === today.getTime()) {
        return t('today');
      } else if (messageDay.getTime() === yesterday.getTime()) {
        return t('yesterday');
      }
      return messageDate.toLocaleDateString(i18n.language, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    return (
      <Time
        {...props}
        timeTextStyle={{
          left: styles.timeText,
          right: styles.timeText,
        }}
        timeFormat="HH:mm"
        dateFormat="LL"
        formatDate={formatDate}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <GiftedChat
        messages={messages}
        onSend={onSend}
        user={{
          _id: 1,
        }}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={renderInputToolbar}
        renderTime={renderTime}
        renderAvatar={null}
        showUserAvatar={false}
        alwaysShowSend
        infiniteScroll
        keyboardShouldPersistTaps="handled"
        bottomOffset={Platform.OS === 'ios' ? insets.bottom : 0}
        textInputProps={{
          placeholder: t('common.chat.input_placeholder'),
          placeholderTextColor: '#666',
          multiline: true,
        }}
        timeFormat="HH:mm"
        dateFormat="LL"
        locale={i18n.language}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  inputToolbar: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 4,
  },
  sendButton: {
    padding: 8,
  },
  sendButtonText: {
    color: '#007AFF',
    fontSize: 24,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  userNameLeft: {
    color: '#DEC48F',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 10,
    marginBottom: 2,
  },
  userNameRight: {
    color: '#2C3E50',
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 10,
    marginBottom: 2,
    textAlign: 'right',
  },
});

export default GuestChatRoomScreen;