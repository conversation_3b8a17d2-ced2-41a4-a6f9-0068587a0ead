import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  FlatList,
  Modal,
  ActivityIndicator,
  BackHandler,
  ToastAndroid,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Pdf from 'react-native-pdf';
import { httpClient } from '@/utils/http';
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/stores/useAuthStore';
import { AppConstants } from '@/constants/default';
import { storage } from '@/utils/storage';
import PointsOfInterestPreview from '@/components/PointsOfInterestPreview';
import useListingStore from '@/stores/useListingStore';
import InlinePlayer from '@/components/InlinePlayer';
import { useTranslation } from 'react-i18next';
import { MaterialIcons, Ionicons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import FAQDisplaySection from '@/components/FAQDisplaySection';
import ReservationInfoCard from '@/components/ReservationInfoCard';

interface Media {
  type: 'image' | 'video' | 'audio' | 'pdf' | 'document';
  url: string;
  description?: string;
  title?: string;
  amenityTag?: string;
  tutorial?: boolean;
}

interface Amenity {
  id: string;
  icon: {
    library: 'MaterialIcons' | 'Ionicons' | 'FontAwesome5' | 'MaterialCommunityIcons';
    name: string;
    color?: string;
  };
  title: {
    en: string;
    fr: string;
  };
}

interface FAQ {
  id?: number;
  uuid: string;
  question: string;
  answer: string;
}

interface Listing {
  title: string;
  description: string;
  coverImage: string;
  media: Media[];
  amenities: string[];
  pointsOfInterest: any[];
  faqs?: FAQ[];
}

const UserAccommodationScreen = () => {
  const navigation = useNavigation();
  const { t, i18n } = useTranslation();
  const { setUserInfo, setReservation } = useAuthStore();
  const {
    setListing,
    setIsLoading,
    setIsError,
  } = useListingStore();

    // State hooks grouped together
    const [activeImageIndex, setActiveImageIndex] = useState(0);
    const [pdfMedia, setPdfMedia] = useState<Media | null>(null);
    const [mediaModalVisible, setMediaModalVisible] = useState(false);
    const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
    const [selectedImage, setSelectedImage] = useState<Media | null>(null);
    const [inlineMedia, setInlineMedia] = useState<Media | null>(null);
    const [selectedAmenity, setSelectedAmenity] = useState<string | null>(null);
    const [isReservationExpanded, setIsReservationExpanded] = useState(false);

    // Fetch user reservation info and listing data in one place
    const { data: listing, isLoading, isError, refetch } = useQuery({
      queryKey: ['house'],
      queryFn: async () => {
        try {
          setIsLoading(true);
          const userInfo: any = await httpClient.get('users/authenticate/info');
          console.log("AppConstants.USER_CODE_KEY: " , AppConstants.USER_CODE_KEY);

          const code = storage.getString(AppConstants.USER_CODE_KEY);
          const zustandCode = useAuthStore.getState().userCode;
          console.log("UserInfo: " , userInfo);
          console.log("UserCode: " , code);
          console.log("ZustandCode: " , zustandCode);

          const reservation = userInfo.data.reservations.find((reservation: any) => reservation.reservationCode === code);

          if (!reservation) {
            throw new Error('Reservation not found');
          }

          setReservation(JSON.stringify(reservation));
          setListing(reservation.housing);
          setIsError(false);
          console.log("Reservation: " , reservation);
          setUserInfo(JSON.stringify(userInfo));
          return reservation.housing;
        } catch (error) {
          console.error('Error fetching data:', error);
          setIsError(true);
          throw error;
        } finally {
          setIsLoading(false);
        }
      },
    });

    // Effect for setting navigation options
    useLayoutEffect(() => {
      if (!listing) return;

      navigation.setOptions({
        headerTitle: listing.title,
      });
    }, [navigation, listing?.title]);

    useEffect(() => {
      let backPressCount = 0;
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        if (backPressCount === 0) {
          ToastAndroid.show('Press back again to exit', ToastAndroid.SHORT);
          backPressCount++;
          setTimeout(() => (backPressCount = 0), 2000);
          return true;
        }
        BackHandler.exitApp();
        return false;
      });

      return () => backHandler.remove();
    }, []);


    // Professional amenities with vector icons
    const amenities: Amenity[] = [
      { id: 'wifi', icon: { library: 'MaterialIcons', name: 'wifi', color: '#4CAF50' }, title: { en: 'Wi-Fi', fr: 'Wi-Fi' } },
      { id: 'parking', icon: { library: 'MaterialIcons', name: 'local-parking', color: '#2196F3' }, title: { en: 'Parking', fr: 'Parking' } },
      { id: 'air_conditioning', icon: { library: 'MaterialIcons', name: 'ac-unit', color: '#00BCD4' }, title: { en: 'Air Conditioning', fr: 'Climatisation' } },
      { id: 'heating', icon: { library: 'MaterialCommunityIcons', name: 'radiator', color: '#FF5722' }, title: { en: 'Heating', fr: 'Chauffage' } },
      { id: 'kitchen', icon: { library: 'MaterialIcons', name: 'kitchen', color: '#795548' }, title: { en: 'Equipped Kitchen', fr: 'Cuisine équipée' } },
      { id: 'tv', icon: { library: 'MaterialIcons', name: 'tv', color: '#9C27B0' }, title: { en: 'TV', fr: 'Télévision' } },
      { id: 'washing_machine', icon: { library: 'MaterialCommunityIcons', name: 'washing-machine', color: '#607D8B' }, title: { en: 'Washing Machine', fr: 'Machine à laver' } },
      { id: 'dryer', icon: { library: 'MaterialCommunityIcons', name: 'tumble-dryer', color: '#9E9E9E' }, title: { en: 'Dryer', fr: 'Sèche-linge' } },
      { id: 'dishwasher', icon: { library: 'MaterialCommunityIcons', name: 'dishwasher', color: '#3F51B5' }, title: { en: 'Dishwasher', fr: 'Lave-vaisselle' } },
      { id: 'pool', icon: { library: 'MaterialCommunityIcons', name: 'pool', color: '#00BCD4' }, title: { en: 'Pool', fr: 'Piscine' } },
      { id: 'hot_tub', icon: { library: 'MaterialCommunityIcons', name: 'hot-tub', color: '#FF9800' }, title: { en: 'Hot Tub', fr: 'Jacuzzi' } },
      { id: 'gym', icon: { library: 'MaterialCommunityIcons', name: 'dumbbell', color: '#F44336' }, title: { en: 'Gym', fr: 'Salle de sport' } },
      { id: 'workspace', icon: { library: 'MaterialIcons', name: 'work', color: '#673AB7' }, title: { en: 'Workspace', fr: 'Espace de travail' } },
      { id: 'balcony', icon: { library: 'MaterialCommunityIcons', name: 'balcony', color: '#4CAF50' }, title: { en: 'Balcony / Terrace', fr: 'Balcon / Terrasse' } },
      { id: 'garden', icon: { library: 'MaterialCommunityIcons', name: 'tree', color: '#4CAF50' }, title: { en: 'Garden', fr: 'Jardin' } },
      { id: 'fireplace', icon: { library: 'MaterialCommunityIcons', name: 'fireplace', color: '#FF5722' }, title: { en: 'Fireplace', fr: 'Cheminée' } },
      { id: 'bbq', icon: { library: 'MaterialCommunityIcons', name: 'grill', color: '#795548' }, title: { en: 'BBQ Area', fr: 'Espace barbecue' } },
      { id: 'safe', icon: { library: 'MaterialIcons', name: 'security', color: '#607D8B' }, title: { en: 'Safe', fr: 'Coffre-fort' } },
      { id: 'elevator', icon: { library: 'MaterialIcons', name: 'elevator', color: '#9E9E9E' }, title: { en: 'Elevator', fr: 'Ascenseur' } },
      { id: 'pet_friendly', icon: { library: 'MaterialIcons', name: 'pets', color: '#FF9800' }, title: { en: 'Pet Friendly', fr: 'Animaux acceptés' } },
      { id: 'leisure_equipment', icon: { library: 'MaterialCommunityIcons', name: 'gamepad-variant', color: '#E91E63' }, title: { en: 'Leisure Equipment', fr: 'Équipements de loisirs' } },
      { id: 'comfort_equipment', icon: { library: 'MaterialCommunityIcons', name: 'sofa', color: '#8BC34A' }, title: { en: 'Comfort Equipment', fr: 'Équipements de confort' } },
      { id: 'rules', icon: { library: 'MaterialIcons', name: 'rule', color: '#FF5722' }, title: { en: 'Rules', fr: 'Règles' } }
    ];

    const filteredAmenities = amenities.filter(amenity => listing?.amenities.includes(amenity.id));
    console.log("Listing Amenities: " , listing?.amenities);

    // Helper function to render amenity icons
    const renderAmenityIcon = (amenity: Amenity, size: number = 24, overrideColor?: string) => {
      const { library, name, color } = amenity.icon;
      const iconColor = overrideColor || color || '#333';

      switch (library) {
        case 'MaterialIcons':
          return <MaterialIcons name={name as any} size={size} color={iconColor} />;
        case 'Ionicons':
          return <Ionicons name={name as any} size={size} color={iconColor} />;
        case 'FontAwesome5':
          return <FontAwesome5 name={name as any} size={size} color={iconColor} />;
        case 'MaterialCommunityIcons':
          return <MaterialCommunityIcons name={name as any} size={size} color={iconColor} />;
        default:
          return <MaterialIcons name="help-outline" size={size} color={iconColor} />;
      }
    };

    const handleMediaPress = (media: Media) => {
      if (media.type === 'pdf' || media.type === 'document') {
        setPdfMedia(media);
        setMediaModalVisible(true);
      } else if (media.type === 'image') {
        setSelectedImage(media);
        setImagePreviewVisible(true);
      } else {
        setInlineMedia(media);
      }
    };



    // Get carousel media (non-tutorial images and videos)
    const handleCarouselMedia = () => {
      if (!listing) return [];
      let media: Media[] = [{ type: 'image', url: listing.coverImage }];
      listing.media.forEach((medium: Media) => {
        if ((medium.type === 'image' || medium.type === 'video') && !medium.tutorial) {
          media.push(medium);
        }
      });
      return media;
    }

    // Get media for selected amenity or tutorial media
    const getFilteredMedia = () => {
      if (!listing) return [];

      if (selectedAmenity) {
        // Return media with matching amenityTag
        return listing.media.filter((medium: Media) =>
          medium.amenityTag === selectedAmenity
        );
      } else {
        // Return tutorial media with no amenityTag
        return listing.media.filter((medium: Media) =>
          medium.tutorial && !medium.amenityTag
        );
      }
    };

    // Handle amenity selection
    const handleAmenityPress = (amenityId: string) => {
      setSelectedAmenity(selectedAmenity === amenityId ? null : amenityId);
    };

    const carouselMedia = handleCarouselMedia();
    const filteredMedia = getFilteredMedia();
    console.log("Carousel Media: " , carouselMedia);
    console.log("Filtered Media: " , filteredMedia);



    const renderCarouselItem = ({ item }: { item: Media }) => {
      // Find amenity name if amenityTag exists
      const amenity = item.amenityTag ? amenities.find(a => a.id === item.amenityTag) : null;

      return (
        <View style={styles.carouselItemContainer}>
          {item.type === 'video' ? (
            <Video
              source={{ uri: item.url }}
              style={styles.carouselImage}
              resizeMode={ResizeMode.COVER}
              shouldPlay={false}
              isLooping={false}
              useNativeControls
            />
          ) : (
            <Image source={{ uri: item.url }} style={styles.carouselImage} />
          )}

          {/* Image count indicator */}
          <View style={styles.imageCountIndicator}>
            <Text style={styles.imageCountText}>
              {carouselMedia.indexOf(item) + 1}/{carouselMedia.length}
            </Text>
          </View>

          {/* Amenity chip */}
          {amenity && (
            <View style={styles.amenityChip}>
              <View style={styles.amenityChipContent}>
                {renderAmenityIcon(amenity, 16, '#fff')}
                <Text style={styles.amenityChipText}>
                  {amenity.title[i18n.language as 'en' | 'fr']}
                </Text>
              </View>
            </View>
          )}
        </View>
      );
    };

    const renderDotIndicator = () => (
      <View style={styles.dotContainer}>
        {carouselMedia.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor: index === activeImageIndex
                  ? '#fff'
                  : 'rgba(255, 255, 255, 0.5)',
              },
            ]}
          />
        ))}
      </View>
    );

    const renderMediaItem = ({ item }: { item: Media }) => {
      console.log('Rendering media item:', item);
      return (
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => handleMediaPress(item)}
        >
          <View style={styles.mediaIconContainer}>
            {item.type === 'image' ? (
              <Image
                source={{ uri: item.url }}
                style={styles.mediaThumbnail}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.mediaIconWrapper}>
                <MaterialIcons name="description" size={24} color="#666" />
              </View>
            )}
          </View>
          <Text style={styles.mediaText}>{item.description}</Text>
        </TouchableOpacity>
      );
    }

    // Handle loading state
    if (isLoading) {
      return (
        <SafeAreaView style={styles.container}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={{ textAlign: 'center' }}>{t('common.accommodation_details_screen.loading')}</Text>
        </SafeAreaView>
      );
    }

    // Handle error state
    if (isError) {
      return (
        <SafeAreaView style={styles.container}>
          <Text style={{ textAlign: 'center' }}>
            {t('common.accommodation_details_screen.error')}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>{t('common.housing_select.tryAgain')}</Text>
          </TouchableOpacity>
        </SafeAreaView>
      );
    }

    // Handle empty state
    if (!listing) {
      return (
        <SafeAreaView style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('common.accommodation_details_screen.no_listings')}</Text>
        </SafeAreaView>
      );
    }

    return (
      <SafeAreaView style={styles.container}>
        

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Media Carousel */}
          <View style={styles.carouselContainer}>
            <FlatList
              data={carouselMedia}
              renderItem={renderCarouselItem}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={(event) => {
                const slideIndex = Math.floor(
                  event.nativeEvent.contentOffset.x /
                  event.nativeEvent.layoutMeasurement.width
                );
                setActiveImageIndex(slideIndex);
              }}
            />
            {renderDotIndicator()}
          </View>

          {/* Listing Details */}
          <View style={styles.detailsContainer}>
            <Text style={styles.title}>
              {listing?.title}
            </Text>

            {/* Reservation Info Card */}
            {useAuthStore.getState().reservation && (
              <ReservationInfoCard
                reservation={JSON.parse(useAuthStore.getState().reservation || '{}')}
                expanded={isReservationExpanded}
                onToggleExpand={() => setIsReservationExpanded(!isReservationExpanded)}
              />
            )}

            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{t('common.accommodation_details_screen.about_accommodation')}</Text>
              <Text style={styles.description}>
                {listing?.description}
              </Text>
            </View>

            {/* Amenities Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{t('common.accommodation_details_screen.amenities')}</Text>
              <View style={styles.amenitiesContainer}>
                {amenities.map((amenity) => {
                  const hasMedia = listing?.media.some((medium: Media) =>
                    medium.amenityTag === amenity.id
                  );
                  if (!hasMedia) return null;

                  return (
                    <TouchableOpacity
                      key={amenity.id}
                      style={[
                        styles.amenityItem,
                        selectedAmenity === amenity.id && styles.selectedAmenity
                      ]}
                      onPress={() => handleAmenityPress(amenity.id)}
                    >
                      <View style={styles.amenityIconContainer}>
                        {renderAmenityIcon(amenity, 28)}
                      </View>
                      <Text style={styles.amenityText}>{amenity.title[i18n.language as 'en' | 'fr']}</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Media Section */}
            {filteredMedia.length > 0 && (
              <View style={styles.sectionContainer}>
                <Text style={styles.sectionTitle}>
                  {selectedAmenity
                    ? `${t('common.accommodation_details_screen.medias')} ${amenities.find(a => a.id === selectedAmenity)?.title[i18n.language as 'en' | 'fr']}`
                    : t('common.accommodation_details_screen.medias')
                  }
                </Text>
                <View style={styles.mediaContainer}>
                  <FlatList
                    data={filteredMedia}
                    renderItem={renderMediaItem}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                  />
                </View>
              </View>
            )}

            {/* Inline Player for Audio/Video */}
            {inlineMedia && (
              <InlinePlayer
                media={inlineMedia}
                onClose={() => setInlineMedia(null)}
              />
            )}

            {/* POI List Preview */}
            <PointsOfInterestPreview listing={listing} onPOIPress={() => {}} />

            {/* FAQ Section */}
            {listing.faqs && listing.faqs.length > 0 && (
              <View style={styles.sectionContainer}>
                <FAQDisplaySection faqs={listing.faqs} />
              </View>
            )}
          </View>
        </ScrollView>

        {/* PDF Media Modal */}
        {mediaModalVisible && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'white',
            zIndex: 9999,
          }}>
            <SafeAreaView style={{ flex: 1 }}>
              {/* Header */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: 16,
                borderBottomWidth: 1,
                borderBottomColor: '#eaeaea',
                backgroundColor: 'white',
              }}>
                {/* Close Button */}
                <TouchableOpacity
                  onPress={() => {
                    console.log('Close button pressed');
                    setMediaModalVisible(false);
                    setPdfMedia(null);
                  }}
                  style={{
                    backgroundColor: '#f0f0f0',
                    borderRadius: 20,
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <View style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <MaterialIcons name="close" size={22} color="#333" />
                  </View>
                </TouchableOpacity>

                {/* Title */}
                <Text style={{
                  fontSize: 18,
                  fontWeight: '600',
                  flex: 1,
                  textAlign: 'center',
                  marginHorizontal: 10,
                }}>
                  {pdfMedia?.title || pdfMedia?.description || 'Document'}
                </Text>

                {/* Placeholder for symmetry */}
                <View style={{ width: 40 }} />
              </View>

              {/* PDF Content */}
              {pdfMedia && (
                <View style={{ flex: 1 }}>
                  <Pdf
                    source={{ uri: pdfMedia.url, cache: true }}
                    style={{ flex: 1 }}
                    trustAllCerts={false}
                    onLoadComplete={(numberOfPages) => console.log(`PDF loaded with ${numberOfPages} pages`)}
                    onError={(error) => {
                      console.error("PDF Error:", error);
                      Alert.alert("Error", "Failed to load PDF. Please try again.");
                    }}
                  />
                </View>
              )}
            </SafeAreaView>
          </View>
        )}

        {/* Image Preview */}
        {imagePreviewVisible && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'white',
            zIndex: 9999,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <StatusBar barStyle="light-content" backgroundColor="black" />

            {/* Close button */}
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 40,
                right: 20,
                backgroundColor: 'rgba(100, 100, 100, 0.8)',
                width: 50,
                height: 50,
                borderRadius: 25,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 10000,
              }}
              onPress={() => {
                console.log('Image close button pressed');
                setImagePreviewVisible(false);
                setSelectedImage(null);
              }}
            >
              <View style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <MaterialIcons name="close" size={28} color="white" />
              </View>
            </TouchableOpacity>

            {/* Image */}
            <Image
              source={{ uri: selectedImage?.url }}
              style={{
                width: Dimensions.get('window').width,
                height: Dimensions.get('window').height * 0.8,
                resizeMode: 'contain',
              }}
            />

            {/* Description */}
            {selectedImage?.description && (
              <Text style={{
                position: 'absolute',
                bottom: 40,
                left: 0,
                right: 0,
                color: 'white',
                fontSize: 16,
                textAlign: 'center',
                paddingHorizontal: 20,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                paddingVertical: 10,
              }}>
                {selectedImage.description}
              </Text>
            )}
          </View>
        )}
      </SafeAreaView>
    );
  };

  const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#fff' },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#eaeaea',
    },
    headerTitle: { fontSize: 18, fontWeight: '600', color: '#333', flex: 1, textAlign: 'center' },
    backButton: { width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center' },
    backButtonText: { fontSize: 24, color: '#333' },
    optionsButton: { justifyContent: 'center', alignItems: 'center' },
    optionsButtonText: { fontSize: 13, color: '#528dcf', fontWeight: '600', marginRight: 2 },
    carouselContainer: { height: 300, position: 'relative' },
    carouselItemContainer: {
      width: Dimensions.get('window').width,
      height: 300,
      position: 'relative',
    },
    carouselImage: {
      width: '100%',
      height: '100%',
    },
    imageCountIndicator: {
      position: 'absolute',
      right: 16,
      bottom: 16,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    imageCountText: { color: '#fff', fontSize: 14, fontWeight: '600' },
    amenityChip: {
      position: 'absolute',
      left: 16,
      bottom: 16,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      maxWidth: '60%',
    },
    amenityChipContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
    },
    amenityChipText: {
      color: '#fff',
      fontSize: 14,
      fontWeight: '600',
      flexShrink: 1,
    },
    dotContainer: {
      position: 'absolute',
      bottom: 16,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    dot: { width: 8, height: 8, borderRadius: 4, marginHorizontal: 4 },
    detailsContainer: { padding: 16 },
    title: { fontSize: 20, fontWeight: 'bold', color: '#333', marginBottom: 16 },
    sectionContainer: { marginTop: 24 },
    sectionTitle: { fontSize: 18, fontWeight: 'bold', color: '#333', marginBottom: 16 },
    description: { fontSize: 16, color: '#666', lineHeight: 24 },
    readMoreText: { fontSize: 16, color: '#0066CC', marginTop: 8 },
    mediaContainer: { flexDirection: 'row', marginBottom: 16 },
    mediaItem: { alignItems: 'center', marginRight: 16 },
    mediaIconContainer: { width: 64, height: 64, borderRadius: 8, backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center', marginBottom: 8 },
    mediaIconWrapper: { justifyContent: 'center', alignItems: 'center' },
    mediaThumbnail: { width: '100%', height: '100%', borderRadius: 8 },
    mediaText: { fontSize: 12, color: '#333', textAlign: 'center' },
    amenitiesContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
    amenityItem: { width: '33%', alignItems: 'center', marginBottom: 20 },
    amenityIconContainer: { width: 60, height: 60, borderRadius: 30, backgroundColor: '#e6f2ff', justifyContent: 'center', alignItems: 'center', marginBottom: 8 },
    amenityText: { fontSize: 12, color: '#333', textAlign: 'center' },
    showAllButton: { backgroundColor: '#f8f8f8', paddingVertical: 12, borderRadius: 8, alignItems: 'center' },
    showAllButtonText: { fontSize: 16, color: '#333', fontWeight: '500' },
    showDeleteButton: { backgroundColor: '#dd9393', paddingVertical: 12, borderRadius: 8, alignItems: 'center' },
    showDeleteButtonText: { fontSize: 16, color: '#ffffff', fontWeight: '500' },
    // Modal Styles for PDF
    modalContainer: { flex: 1, backgroundColor: '#fff' },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#eaeaea',
    },
    modalTitle: { fontSize: 18, fontWeight: '600', color: '#333', flex: 1, textAlign: 'center' },
    closeButton: { width: 40, height: 40, justifyContent: 'center', alignItems: 'center' },
    closeButtonText: { fontSize: 28, color: '#333' },
    placeholder: { width: 40 },
    mediaContentContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000' },
    mediaModalContent: { width: '100%', height: '100%' },
    poiListContainer: {
      marginTop: 15,
      marginBottom: 10,
      backgroundColor: '#f9f9f9',
      borderRadius: 8,
      padding: 10,
    },
    poiListTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 10,
      color: '#333',
    },
    poiItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'white',
      marginBottom: 8,
      borderRadius: 6,
      borderLeftWidth: 4,
      borderLeftColor: '#4A90E2',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    poiItemContent: {
      flex: 1,
      padding: 12,
    },
    poiName: {
      fontSize: 15,
      fontWeight: '600',
    },
    poiAddress: {
      fontSize: 13,
      color: '#666',
      marginTop: 3,
    },
    retryButton: {
      backgroundColor: '#D1987C',
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      marginTop: 16,
      alignSelf: 'center',
    },
    retryButtonText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: '600',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    emptyText: {
      fontSize: 16,
      color: '#666',
      textAlign: 'center',
    },
    selectedAmenity: {
      backgroundColor: '#e6f2ff',
      borderColor: '#4A90E2',
      borderWidth: 1,
    },
    imagePreviewContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: '#000000',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999,
    },
    imagePreviewOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'transparent',
      zIndex: 1,
    },
    imagePreviewCloseButton: {
      position: 'absolute',
      top: Platform.OS === 'ios' ? 50 : 30,
      right: 20,
      zIndex: 10000,
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: 'rgba(104, 104, 104, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.5,
      shadowRadius: 4,
      elevation: 8,
    },
    imagePreviewCloseText: {
      color: 'white',
      fontSize: 24,
      fontWeight: 'bold',
    },
    imagePreview: {
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height * 0.8,
      backgroundColor: 'transparent',
    },
    imagePreviewDescription: {
      color: 'white',
      fontSize: 16,
      textAlign: 'center',
      paddingHorizontal: 20,
      position: 'absolute',
      bottom: 40,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      paddingVertical: 10,
      zIndex: 5,
    },
  });

export default UserAccommodationScreen;
