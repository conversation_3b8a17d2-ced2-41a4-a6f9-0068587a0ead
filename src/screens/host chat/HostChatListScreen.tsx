import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React, { useMemo, useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TextInput,
  FlatList,
  SectionList,
  Image,
  StatusBar,
  Platform,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  FadeIn,
  FadeOut,
  SlideInRight,
  Layout,
  withRepeat,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { collection, query, orderBy, onSnapshot } from 'firebase/firestore';
import { firestoreDatabase } from 'firebaseConfig';
import { useTranslation } from 'react-i18next';

// Types
interface Message {
  id: string;
  userName?: string;
  lastMessage?: string;
  timestamp?: number;
  avatarUrl?: string;
  isOnline?: boolean;
  unreadCount?: number;
  conversationId?: string;
  participants?: Array<{
    displayName: string;
    photoURL?: string;
    online?: boolean;
  }>;
}

interface Conversation {
  conversationId: string;
  lastMessage?: string;
  timestamp?: number;
  unreadCount?: number;
  context?: 'cohost' | 'service' | 'reservation';
  participants?: Array<{
    displayName: string;
    photoURL?: string;
    online?: boolean;
  }>;
}

interface ChatListItemProps {
  item: Conversation;
  onPress: (id: string) => void;
  currentUserName: string;
  isNew?: boolean;
}

interface UserInfoResponse {
  data: {
    displayName: string;
    [key: string]: any;
  }
}

// Helper function for time formatting
const getRelativeTime = (timestamp?: number, t?: any): string => {
  if (!timestamp) return t ? t('chat.unknown') : 'Unknown';

  const now = new Date();
  const messageDate = new Date(timestamp * 1000); // Convert from seconds to milliseconds
  const diffTime = now.getTime() - messageDate.getTime(); // Difference in milliseconds
  const diffSeconds = Math.floor(diffTime / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (t) {
    if (diffSeconds < 10) return t('chat.justNow');
    if (diffMinutes < 1) return t('chat.secondsAgo', { seconds: diffSeconds });
    if (diffHours < 1) return t('chat.minutesAgo', { minutes: diffMinutes });
    if (diffDays < 1) return t('chat.hoursAgo', { hours: diffHours });
    if (diffDays < 7) return t('chat.daysAgo', { days: diffDays });
  } else {
    if (diffSeconds < 10) return 'Just now';
    if (diffMinutes < 1) return `${diffSeconds}s ago`;
    if (diffHours < 1) return `${diffMinutes}m ago`;
    if (diffDays < 1) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
  }

  // Format the date properly
  return messageDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Get initials from name
const getInitials = (name: string): string => {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// Chat List Item Component
const ChatListItem: React.FC<ChatListItemProps> = React.memo(({
  item,
  onPress,
  currentUserName,
  isNew = false
}) => {
  const { t, i18n } = useTranslation();
  const scale = useSharedValue(1);
  const [lastMessage, setLastMessage] = useState(item.lastMessage || t('common.hostChat.noMessages'));
  const [timestamp, setTimestamp] = useState(item.timestamp || Math.floor(Date.now() / 1000));
  const [unreadCount, setUnreadCount] = useState(item.unreadCount || 0);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });
  console.log("Item: ", item);

  const participantName = item.participants
    ?.filter(p => p.displayName !== currentUserName)
    .map(p => p.displayName)
    .join(', ');

  const isAnyParticipantOnline = item.participants?.some(p =>
    p.displayName !== currentUserName && p.online
  );

  const truncatedMessage = lastMessage.length > 40
    ? `${lastMessage.substring(0, 40)}...`
    : lastMessage;

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    scale.value = withTiming(0.95, { duration: 100 });
    setTimeout(() => {
      scale.value = withSpring(1);
      onPress(item.conversationId);
    }, 100);
  };

  const participantAvatar = item.participants?.find(
    p => p.displayName !== currentUserName
  )?.photoURL;

  const participantInitials = getInitials(participantName || '');

  // Listen for new messages in this conversation
  useEffect(() => {
    if (item.conversationId) {
      const path = `conversations/${item.conversationId}/messages`;
      const q = query(
        collection(firestoreDatabase, path),
        orderBy('timestamp', 'desc')
      );

      const unsubscribe = onSnapshot(q, (snapshot) => {
        if (!snapshot.empty) {
          const latestMessage = snapshot.docs[0].data();
          setLastMessage(latestMessage.content);
          setTimestamp(latestMessage.timestamp);

          if (latestMessage.senderDisplayName !== currentUserName) {
            setUnreadCount(prev => prev + 1);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        }
      });

      return () => unsubscribe();
    }
  }, [item.conversationId, currentUserName]);

  const formatTime = (timestamp: number) => {
    if (!timestamp) return '';

    const now = new Date();
    const messageTime = new Date(timestamp * 1000); // Convert from seconds to milliseconds
    const diffMs = now.getTime() - messageTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return t('common.hostChat.justNow');
    if (diffMins < 60) return t('common.hostChat.minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('common.hostChat.hoursAgo', { count: diffHours });
    if (diffDays < 7) return t('common.hostChat.daysAgo', { count: diffDays });

    // For messages older than a week, show the date using the current i18n locale
    return messageTime.toLocaleDateString(i18n.language, {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Animated.View
      entering={FadeIn.duration(300)}
      exiting={FadeOut.duration(300)}
      layout={Layout}
      style={animatedStyles}
    >
      <TouchableOpacity
        onPress={handlePress}
        style={[styles.chatItem, isNew && styles.newChatItem]}
      >
        <View style={styles.avatarContainer}>
          {participantAvatar ? (
            <Image
              source={{ uri: participantAvatar }}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.initialsContainer}>
              <Text style={styles.initials}>{participantInitials}</Text>
            </View>
          )}
          {isAnyParticipantOnline && (
            <View style={styles.onlineIndicator}>
              <Text style={styles.onlineText}>{t('common.hostChat.online')}</Text>
            </View>
          )}
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.messageHeader}>
            <Text style={styles.userName} numberOfLines={1}>{participantName || t('common.hostChat.unknown')}</Text>
            <Text style={styles.timestamp}>{formatTime(timestamp)}</Text>
          </View>

          <View style={styles.messagePreviewContainer}>
            <Text
              style={[styles.messagePreview, isNew && styles.unreadMessage]}
              numberOfLines={1}
            >
              {truncatedMessage}
            </Text>

            {unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>{unreadCount}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Search Bar Component
type SearchBarProps = {
  value: string;
  onChangeText: (text: string) => void;
};

const SearchBar = ({ value, onChangeText }: SearchBarProps) => {
  const { t } = useTranslation();
  const inputRef = useRef<TextInput | null>(null);
  const focused = useSharedValue(false);

  const containerStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: focused.value
        ? 'rgba(142, 142, 147, 0.18)'
        : 'rgba(142, 142, 147, 0.12)',
      borderRadius: 10,
    };
  });

  return (
    <Animated.View style={[styles.searchContainer, containerStyle]}>
      <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
      <TextInput
        ref={inputRef}
        style={styles.searchInput}
        placeholder={t('common.hostChat.searchPlaceholder')}
        placeholderTextColor="#999"
        value={value}
        onChangeText={onChangeText}
        onFocus={() => {
          focused.value = true as any;
          Haptics.selectionAsync();
        }}
        onBlur={() => {
          focused.value = false as any;
        }}
      />
      {value.length > 0 && (
        <TouchableOpacity
          onPress={() => {
            onChangeText('');
            inputRef.current?.blur();
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          style={styles.clearButton}
        >
          <Ionicons name="close" size={18} color="#999" />
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

// Empty State Component
type EmptyStateProps = {
  searchQuery: string;
};

const EmptyState = ({ searchQuery }: EmptyStateProps) => {
  const { t } = useTranslation();
  return (
    <Animated.View
      entering={FadeIn.duration(400)}
      style={styles.emptyContainer}
    >
      <Ionicons
        name={searchQuery ? "search-outline" : "chatbubble-ellipses-outline"}
        size={70}
        color="#DEC48F"
      />
      <Text style={styles.emptyTitle}>
        {searchQuery ? t('chat.noSearchResults') : t('chat.emptyTitle')}
      </Text>
      <Text style={styles.emptyText}>
        {searchQuery
          ? t('chat.searchNoResults', { query: searchQuery })
          : t('chat.emptyText')
        }
      </Text>
    </Animated.View>
  );
};

// Section Header Component
type SectionHeaderProps = {
  title: string;
  count: number;
};

const SectionHeader: React.FC<SectionHeaderProps> = ({ title, count }) => {
  const { t } = useTranslation();

  // Get the translated context title
  const getContextTitle = (context: string) => {
    switch (context) {
      case 'cohost':
        return t('common.hostChat.contexts.cohost');
      case 'service':
        return t('common.hostChat.contexts.service');
      case 'reservation':
        return t('common.hostChat.contexts.reservation');
      default:
        return context;
    }
  };

  return (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{getContextTitle(title)}</Text>
      <View style={styles.sectionCount}>
        <Text style={styles.sectionCountText}>{count}</Text>
      </View>
    </View>
  );
};

// Custom Hook for Fetching Conversations
const useConversations = () => {
  const { data: conversations = [], refetch, isFetching, isError, error } = useQuery({
    queryKey: ['conversations'],
    queryFn: async () => {
      try {
        const response = await httpClient.get<UserInfoResponse>('/chat/conversations');
        console.log("Conversations: ", response.data);
        return Array.isArray(response.data) ? response.data : [];
      } catch (err) {
        console.error('Failed to fetch conversations:', err);
        return [];
      }
    },
    refetchOnWindowFocus: true,
    retry: 2,
    retryDelay: 1000,
  });

  return { conversations, refetch, isFetching, isError, error };
};

// Custom Hook for Managing Firestore Listeners
const useFirestoreListeners = (
  conversationIds: string[],
  currentUserName: string,
  setConversations: React.Dispatch<React.SetStateAction<Conversation[]>>
) => {
  useFocusEffect(
    useCallback(() => {
      if (conversationIds && conversationIds.length) {
        const unsubscribes = conversationIds.map(convId => {
          if (!convId) return () => {};

          const path = `conversations/${convId}/messages`;
          const q = query(
            collection(firestoreDatabase, path),
            orderBy('timestamp', 'desc')
          );

          const unsubscribe = onSnapshot(q, (snapshot) => {
            if (!snapshot.empty) {
              const latestMessage = snapshot.docs[0].data();
              setConversations(prev => {
                const updated = [...prev];
                const index = updated.findIndex(conv => conv.conversationId === convId);

                if (index !== -1) {
                  // Only increment unread count if the message is new and from another user
                  const isNewMessage = latestMessage.timestamp > (updated[index].timestamp || 0);
                  const isFromOtherUser = latestMessage.senderDisplayName !== currentUserName;

                  updated[index] = {
                    ...updated[index],
                    lastMessage: latestMessage.content,
                    timestamp: latestMessage.timestamp,
                    unreadCount: isNewMessage && isFromOtherUser
                      ? (updated[index].unreadCount || 0) + 1
                      : updated[index].unreadCount || 0,
                  };
                }

                return updated;
              });
            }
          });

          return unsubscribe;
        });

        return () => {
          unsubscribes.forEach(unsub => unsub && unsub());
        };
      }
      return undefined;
    }, [conversationIds, currentUserName, setConversations])
  );
};

// Main Component
const HostChatListScreen: React.FC = () => {
  // 1. Translation and Navigation Hooks
  const { t } = useTranslation();
  const navigation = useNavigation<NativeStackNavigationProp<any>>();

  // 2. State Hooks
  const [searchQuery, setSearchQuery] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [newMessageIds, setNewMessageIds] = useState(new Set<string>());

  // 3. Store and Service Hooks
  const { setConversationId } = useAuthStore();

  // 4. Animation Hooks
  const refreshAnim = useSharedValue(0);
  const emptyOpacity = useSharedValue(1);
  const emptyTranslateY = useSharedValue(1);

  // 5. Data Fetching Hooks
  const { conversations: fetchedConversations, refetch, isFetching, isError } = useConversations();

  // 6. Memoized Values
  const conversationIds = useMemo(() =>
    fetchedConversations.map(conv => conv.conversationId),
    [fetchedConversations]
  );

  // 7. Memoized Computed Values
  const filteredConversations = useMemo(() => {
    if (!Array.isArray(conversations)) return [];

    return conversations.filter(conversation => {
      const participantNames = conversation.participants
        ?.filter(p => p.displayName !== displayName)
        .map(p => p.displayName.toLowerCase())
        .join(' ');

      const query = searchQuery.toLowerCase();

      return (
        participantNames?.includes(query) ||
        (conversation.lastMessage || '').toLowerCase().includes(query)
      );
    }).sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
  }, [conversations, searchQuery, displayName]);

  // Group conversations by context
  const groupedConversations = useMemo(() => {
    if (!Array.isArray(filteredConversations)) return {
      cohost: [],
      service: [],
      reservation: []
    };

    return filteredConversations.reduce((acc, conversation) => {
      const context = conversation.context || 'cohost'; // Default to cohost if no context
      if (!acc[context]) {
        acc[context] = [];
      }
      acc[context].push(conversation);
      return acc;
    }, {
      cohost: [],
      service: [],
      reservation: []
    } as Record<string, Conversation[]>);
  }, [filteredConversations]);

  const unreadCount = useMemo(() => {
    if (!Array.isArray(conversations)) return 0;
    return conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);
  }, [conversations]);

  // 8. Helper Functions
  const getUserInfo = async () => {
    try {
      const response = await httpClient.get<UserInfoResponse>('users/authenticate/info');
      setDisplayName(response.data.displayName);
      console.log("displayName: ", response.data.displayName);
      return response;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  // 9. Callback Functions
  const handleMessagePress = useCallback((id: string) => {
    try {
      setConversationId(id);

      if (newMessageIds.has(id)) {
        setNewMessageIds(prev => {
          const updated = new Set(prev);
          updated.delete(id);
          return updated;
        });

        setConversations(prev => {
          const updated = [...prev];
          const index = updated.findIndex(conv => conv.conversationId === id);

          if (index !== -1) {
            updated[index] = {
              ...updated[index],
              unreadCount: 0
            };
          }

          return updated;
        });
      }

      navigation.navigate('HostChatRoom');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, [newMessageIds, setConversationId, navigation]);

  const renderItem = useCallback(({ item, index }: { item: Conversation, index: number }) => {
    if (!item || !item.conversationId) return null;

    const isNew = newMessageIds.has(item.conversationId);

    return (
      <ChatListItem
        item={item}
        onPress={handleMessagePress}
        currentUserName={displayName}
        isNew={isNew}
      />
    );
  }, [newMessageIds, handleMessagePress, displayName]);

  // 10. Effects
  useEffect(() => {
    getUserInfo();
  }, []);

  useFocusEffect(
    useCallback(() => {
      console.log("Screen focused, refreshing conversations");
      refetch();
    }, [refetch])
  );

  useFirestoreListeners(conversationIds, displayName, setConversations);

  useEffect(() => {
    if (Array.isArray(fetchedConversations)) {
      const processedConversations = fetchedConversations.map(conversation => ({
        ...conversation,
        lastMessage: conversation.lastMessage || t('chat.noMessages') || '',
        timestamp: conversation.timestamp || Math.floor(Date.now() / 1000),
        unreadCount: conversation.unreadCount || 0
      }));

      setConversations(processedConversations);
    }
  }, [fetchedConversations, t]);

  useEffect(() => {
    if (filteredConversations.length === 0 && !isFetching && !isError) {
      emptyOpacity.value = 0;
      emptyTranslateY.value = 20;

      emptyOpacity.value = withTiming(1, { duration: 400 });
      emptyTranslateY.value = withTiming(0, { duration: 400 });
    }
  }, [filteredConversations.length, isFetching, isError]);

  return (
    <SafeAreaView style={styles.container}>
      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
      />

      {/* Error state */}
      {isError && (
        <Animated.View
          entering={FadeIn.duration(300)}
          style={styles.errorContainer}
        >
          <Text style={styles.errorText}>
            {t('common.hostChat.loadError')}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              refetch();
            }}
          >
            <Text style={styles.retryText}>{t('common.hostChat.retry')}</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Loading state */}
      {isFetching && filteredConversations.length === 0 && (
        <Animated.View
          entering={FadeIn.duration(400)}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#DEC48F" />
          <Text style={styles.loadingText}>{t('chat.loading')}</Text>
        </Animated.View>
      )}

      {/* Empty state */}
      {filteredConversations.length === 0 && !isFetching && !isError && (
        <Animated.View
          style={[
            styles.emptyContainer,
            {
              opacity: emptyOpacity,
              transform: [{ translateY: emptyTranslateY }]
            }
          ]}
        >
          <Ionicons name="chatbubble-ellipses-outline" size={60} color="#DDD" />
          <Text style={styles.emptyText}>
            {t('common.hostChat.noConversations')}
          </Text>
        </Animated.View>
      )}

      {/* Messages List */}
      <SectionList
        sections={[
          {
            title: 'cohost',
            data: groupedConversations.cohost.length > 0 ? groupedConversations.cohost : []
          },
          {
            title: 'service',
            data: groupedConversations.service.length > 0 ? groupedConversations.service : []
          },
          {
            title: 'reservation',
            data: groupedConversations.reservation.length > 0 ? groupedConversations.reservation : []
          }
        ].filter(section => section.data.length > 0)} // Only show sections with data
        renderItem={({ item }) => renderItem({ item, index: 0 })}
        renderSectionHeader={({ section }) => (
          <SectionHeader title={section.title} count={section.data.length} />
        )}
        keyExtractor={(item) => item?.conversationId?.toString() || Math.random().toString()}
        contentContainerStyle={[
          styles.listContainer,
          filteredConversations.length === 0 && styles.emptyList
        ]}
        showsVerticalScrollIndicator={false}
        initialNumToRender={10}
        stickySectionHeadersEnabled={false}
        refreshControl={
          <RefreshControl
            refreshing={isFetching}
            onRefresh={() => {
              refreshAnim.value = withTiming(1, { duration: 1000 });
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              refetch();
              refreshAnim.value = 0;
            }}
            colors={['#DEC48F']}
            tintColor="#DEC48F"
          />
        }
      />
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: '#f8f8f8',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textTransform: 'capitalize',
  },
  sectionCount: {
    backgroundColor: '#DEC48F',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    minWidth: 24,
    alignItems: 'center',
  },
  sectionCountText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  searchContainer: {
    marginHorizontal: 16,
    marginBottom: 10,
    marginTop: 6,
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 10,
    color: '#000',
  },
  clearButton: {
    padding: 5,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 10,
    backgroundColor: '#F7F7F7',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadChatItem: {
    backgroundColor: '#f9f5ed',
    borderLeftWidth: 3,
    borderLeftColor: '#DEC48F',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#DEC48F', // Fallback color
  },
  initialsContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#DEC48F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  initials: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  onlineText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 13,
    color: '#888',
  },
  messagePreviewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  messagePreview: {
    fontSize: 14,
    color: '#666',
    flex: 1,
    marginRight: 8,
  },
  unreadMessage: {
    color: '#333',
    fontWeight: '500',
  },
  unreadBadge: {
    backgroundColor: '#DEC48F',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 3,
    minWidth: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unreadCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 300,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginVertical: 12,
    textAlign: 'center',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: '#DEC48F',
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },

  newChatItem: {
    backgroundColor: 'rgba(222, 196, 143, 0.1)',
    borderLeftWidth: 3,
    borderLeftColor: '#DEC48F',
  },
});

export default HostChatListScreen;