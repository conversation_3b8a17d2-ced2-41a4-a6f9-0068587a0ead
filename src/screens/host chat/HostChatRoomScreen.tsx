import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Text,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { GiftedChat, IMessage, Send, Bubble, Time, InputToolbar } from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { collection, query, orderBy, onSnapshot, doc } from 'firebase/firestore';
import { firestoreDatabase } from '../../../firebaseConfig';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import 'dayjs/locale/fr';
import dayjs from 'dayjs';

// Define interfaces for conversation data
interface Participant {
  id: number;
  displayName: string;
  userType: string;
  photoURL?: string;
  online?: boolean;
}

interface ConversationDetails {
  conversationId: string | number;
  participants: Participant[];
  housingId?: number;
  context?: 'cohost' | 'service' | 'reservation';
  lastMessage?: string;
  timestamp?: number;
  unreadCount?: number;
}

interface ConversationResponse {
  data: ConversationDetails;
  code: number;
  status: string;
}

interface UserInfoResponse {
  data: {
    displayName: string;
    [key: string]: any;
  }
}

const HostChatRoomScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<any>();
  const { userInfo, conversationId } = useAuthStore();
  const [displayName, setDisplayName] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Fetch conversation details
  const { data: conversationDetails, isLoading: isLoadingConversation } = useQuery<ConversationDetails>({
    queryKey: ['conversation', conversationId],
    queryFn: async () => {
      try {
        const response = await httpClient.get<ConversationResponse>(`/chat/conversations/${conversationId}`);
        return response.data;
      } catch (error) {
        console.error('Failed to fetch conversation details:', error);
        throw error;
      }
    },
    enabled: !!conversationId,
  });

  const getUserInfo = async () => {
    try {
      const userInfo = await httpClient.get<UserInfoResponse>('users/authenticate/info');
      setDisplayName(userInfo.data.displayName);
      return userInfo;
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      return null;
    }
  };

  // Set the navigation header title based on participants
  useEffect(() => {
    const init = async () => {
      await getUserInfo();
    };
    init();
  }, []);

  // Update the navigation header when conversation details are loaded
  useEffect(() => {
    if (conversationDetails && displayName) {
      // Filter out the current user from participants
      const otherParticipants = conversationDetails.participants.filter(
        p => p.displayName !== displayName
      );

      // Create a title from other participants' names
      const title = otherParticipants.map(p => p.displayName).join(', ');

      // Set the navigation header title
      navigation.setOptions({
        title: title || t('common.hostChat.title'),
      });
    }
  }, [conversationDetails, displayName, navigation, t]);

  useEffect(() => {
    if (!displayName) return; // Only proceed if displayName is available
    //console.log("Conversation ID: ", conversationId);
    if (conversationId) {
      console.log("Conversation ID: ", conversationId);
      const path = `conversations/${conversationId}/messages`;
      console.log("Path: ", path);
      const q = query(
        collection(firestoreDatabase, path),
        orderBy('timestamp', 'desc')
      );


      const unsubscribe = onSnapshot(q,
        (snapshot) => {
          console.log("Snapshot: ", snapshot.docs);
          const messagesFirestore = snapshot.docs.map((doc) => {

            const firebaseData = doc.data();
            console.log("senderDisplayName: ", firebaseData.senderDisplayName);
            console.log("Display name: ", displayName)
            const data = {
              _id: firebaseData?.messageId,
              text: firebaseData?.content || '', // Ensure text exists
              createdAt: new Date(firebaseData?.timestamp * 1000) || new Date(),
              user: {
                _id: firebaseData?.senderDisplayName === displayName ? 1 : 2,
                name:  firebaseData?.senderDisplayName,
                //avatar: 'https://placeimg.com/140/140/any',
              },
              ...firebaseData,
            };

            return data;
          });

          console.log("Messages: ", messagesFirestore);

          setMessages(messagesFirestore); // Reverse to show newest at bottom
        },
        (error) => {
          console.error("Error fetching messages:", error);
        }
      );

      return () => unsubscribe();
    }
  }, [conversationId, firestoreDatabase, displayName]);

  interface SendMessageParams {
    conversationId: string;
    content: string;
  }

  interface SendMessageResponse {
    data: any; // Replace 'any' with actual response type if known
  }

  // Define the mutation for sending messages
  const sendMessageMutation = useMutation<SendMessageResponse, Error, SendMessageParams>({
    mutationFn: async ({ conversationId, content }) => {
      const response = await httpClient.post<SendMessageResponse>(`/chat/send-message/${conversationId}`, { content });
      console.log("Response: ", response);
      return response;
    },
    onSuccess: (data, variables) => {
      // Optionally, you can update the query cache or perform other actions on success
      console.log('Message sent successfully:', data);
    },
    onError: (error, variables) => {
      // Handle error
      console.error('Error sending message:', error);
    },
  });

  const onSend = useCallback((newMessages: IMessage[] = []) => {
    console.log('Sending messages:', newMessages);

    if (!conversationId) {
      console.error('No conversation ID available');
      return;
    }

    // Send the message to the server
    newMessages.forEach(message => {
      console.log('Sending message to conversation:', conversationId);

      sendMessageMutation.mutate({
        conversationId,
        content: message.text,
      }, {
        onSuccess: () => {
          console.log('Message sent successfully');
        },
        onError: (error) => {
          console.error('Error sending message:', error);
          // Show error to user
          Alert.alert(
            t('common.error'),
            t('common.hostChat.messageSendError'),
            [{ text: t('common.ok') }]
          );
        }
      });
    });
  }, [conversationId, t]);

  // Loading component
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#DEC48F" />
      <Text style={styles.loadingText}>{t('common.hostChat.loading')}</Text>
    </View>
  );

  // Custom bubble component
  const renderBubble = (props: any) => (
    <View>
      {props.currentMessage.user.name && (
        <Text style={props.position === 'left' ? styles.userNameLeft : styles.userNameRight}>
          {props.currentMessage.user.name}
        </Text>
      )}
      <Bubble
        {...props}
        wrapperStyle={{
          left: {
            backgroundColor: '#E5C1B0',
          },
          right: {
            backgroundColor: '#007AFF',
          },
        }}
        textStyle={{
          left: {
            color: '#000',
          },
          right: {
            color: '#fff',
          },
        }}
      />
    </View>
  );

  // Custom send button
  const renderSend = (props: any) => (
    <Send
      {...props}
      containerStyle={styles.sendContainer}
    >
      <View style={styles.sendButton}>
        <Ionicons name="paper-plane-outline" size={24} color="#007AFF" />
      </View>
    </Send>
  );

  // Custom input toolbar
  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={styles.inputToolbar}
      primaryStyle={styles.inputPrimary}
    />
  );

  // Custom time component
  const renderTime = (props: any) => (
    <Time
      {...props}
      timeTextStyle={{
        left: styles.timeText,
        right: styles.timeText,
      }}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      {isLoadingConversation ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#DEC48F" />
          <Text style={styles.loadingText}>{t('common.hostChat.loading')}</Text>
        </View>
      ) : (
        <GiftedChat
          messages={messages}
          onSend={onSend}
          user={{
            _id: 1,
          }}
          renderBubble={renderBubble}
          renderSend={renderSend}
          renderInputToolbar={renderInputToolbar}
          renderTime={(props) => (
            <Time
              {...props}
              timeTextStyle={{
                left: styles.timeText,
                right: styles.timeText,
              }}
              timeFormat="HH:mm"
            />
          )}
          renderAvatar={null}
          showUserAvatar={false}
          alwaysShowSend
          infiniteScroll
          keyboardShouldPersistTaps="handled"
          bottomOffset={Platform.OS === 'ios' ? insets.bottom : 0}
          textInputProps={{
            placeholder: t('common.hostChat.input_placeholder'),
            placeholderTextColor: '#666',
            multiline: true,
          }}
          timeFormat="HH:mm"
          dateFormat="LL"
          locale={i18n.language}
          dateFormatCalendar={{
            sameDay: '[Aujourd\'hui]',
            nextDay: '[Demain]',
            nextWeek: 'dddd',
            lastDay: '[Hier]',
            lastWeek: '[la semaine dernière]',
            sameElse: 'LL'
          }}
          // Ensure input is always enabled
          disableComposer={false}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  inputToolbar: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 4,
  },
  sendButton: {
    padding: 8,
  },
  sendButtonText: {
    color: '#007AFF',
    fontSize: 24,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  userNameLeft: {
    color: '#DEC48F',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 10,
    marginBottom: 2,
  },
  userNameRight: {
    color: '#2C3E50',
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 10,
    marginBottom: 2,
    textAlign: 'right',
  },
});

export default HostChatRoomScreen;