import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  interpolate,
  withSpring,
  withDelay,
  withSequence,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from '@expo/vector-icons/Feather';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useTranslation } from 'react-i18next';
import { useFocusEffect } from '@react-navigation/native';

// Service type based on the provided structure
type Media = {
  description: string;
  type: string;
  url: string;
};

type Professional = {
  avatar: string;
  displayName: string;
  email: string;
  gender: string | null;
  phoneNumber: string;
  uuid: string;
};

type Ratings = {
  average: number;
  count: number;
};

type Service = {
  active: boolean;
  address: string;
  category: string;
  companyName: string;
  coverImage: string;
  description: string;
  email: string;
  latitude: string;
  longitude: string;
  media: Media[];
  phoneNumber: string;
  price: number;
  professional: Professional;
  ratings: Ratings;
  title: string;
  uuid: string;
};

// API response type
type ApiResponse = {
  data: Service;
};

// Types
type ProServiceDetailsScreenProps = {
  navigation: any;
  route: any;
};

const ProServiceDetailsScreen: React.FC<ProServiceDetailsScreenProps> = ({ navigation, route }) => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const serviceUuid: string = route.params.serviceUuid;
  const queryClient = useQueryClient();
  
  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentOpacity = useSharedValue(0);
  const mapOpacity = useSharedValue(0);
  const toolsScale = useSharedValue(0.95);
  
  // State for delete loading
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Fetch service details from the server
  const { data: service, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['offering', serviceUuid],
    queryFn: async () => {
      const response = await httpClient.get<ApiResponse>('/offerings/getone/' + serviceUuid);
      console.log("Service retrieved: ", response.data);
      return response.data;
    }
  });
  
  // Refresh data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, [refetch])
  );
  
  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      return httpClient.delete(`/offerings/delete/${serviceUuid}`);
    },
    onSuccess: () => {
      Alert.alert(
        t('common.success.title'),
        t('pro.services.details.delete_success')
      );
      queryClient.invalidateQueries({ queryKey: ['offerings'] });
      navigation.goBack();
    },
    onError: (err: any) => {
      console.error("Delete error:", err);
      Alert.alert(
        t('common.errors.title'),
        err.response?.data?.message || t('pro.services.details.delete_error')
      );
    },
    onSettled: () => {
      setIsDeleting(false);
    }
  });
  
  // Start animations when component mounts
  useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 600, easing: Easing.out(Easing.ease) });
    contentOpacity.value = withTiming(1, { duration: 800, easing: Easing.out(Easing.ease) });
    mapOpacity.value = withDelay(400, withTiming(1, { duration: 800 }));
    toolsScale.value = withSpring(1, { damping: 12, stiffness: 90 });
  }, []);
  
  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [
      { translateY: interpolate(headerOpacity.value, [0, 1], [-20, 0]) }
    ]
  }));
  
  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
    transform: [
      { translateY: interpolate(contentOpacity.value, [0, 1], [20, 0]) }
    ]
  }));
  
  const mapAnimatedStyle = useAnimatedStyle(() => ({
    opacity: mapOpacity.value
  }));
  
  const toolsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: toolsScale.value }]
  }));
  
  // Render star rating
  const renderRating = (rating: number | undefined, maxRating: number = 5) => {
    const safeRating = rating || 0;
    return (
      <View style={styles.ratingContainer}>
        {[...Array(maxRating)].map((_, index) => (
          <Icon
            key={`star-${index}`}
            name="star"
            size={18}
            color={index < safeRating ? '#FFC107' : '#E0E0E0'}
          />
        ))}
        <Text style={styles.ratingCount}>({service?.ratings?.count || 0})</Text>
      </View>
    );
  };
  
  // Handle back button press
  const handleBackPress = () => {
    // Animate out
    headerOpacity.value = withTiming(0, { duration: 300 });
    contentOpacity.value = withTiming(0, { duration: 300 });
    
    // Wait for animation to complete before navigating back
    setTimeout(() => navigation.goBack(), 300);
  };
  
  const handleUpdatePress = () => {
    navigation.navigate('ProServicesUpdate', { serviceUuid: serviceUuid });
  };

  const handleDeletePress = () => {
    Alert.alert(
      t('pro.services.details.delete_confirm_title'),
      t('pro.services.details.delete_confirm_message'),
      [
        { text: t('common.buttons.cancel'), style: 'cancel' },
        {
          text: t('common.buttons.delete'),
          style: 'destructive',
          onPress: () => {
            setIsDeleting(true);
            deleteMutation.mutate();
          },
        },
      ]
    );
  };
  
  // Loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
      </View>
    );
  }
  
  // Error state
  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('services.details.error')}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text>{t('services.details.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // No service found
  if (!service) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('services.details.notFound')}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text>{t('services.details.goBack')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      {/* Status bar style is handled globally by SystemBars in App.tsx */}
      
      {/* Header with Tools Image and Back Button */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity style={[styles.backButton, { top: insets.top + 16 }]} onPress={handleBackPress}>
          <Icon name="arrow-left" size={24} color="#000" />
        </TouchableOpacity>
        <Image 
          source={{ uri: service.coverImage }} 
          style={styles.headerImage}
          resizeMode="cover"
        />
      </Animated.View>
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: insets.bottom + 20 }}
      >
        {/* Title Section */}
        <Animated.View style={[styles.titleSection, headerAnimatedStyle]}>
          <View style={styles.titleRow}>
            <Text style={styles.title}>{service.title}</Text>
            <View style={styles.priceTag}>
              <Text style={styles.priceText}>{service.price}€ / h</Text>
            </View>
          </View>
          
          <View style={styles.ratingRow}>
            {renderRating(service?.ratings?.average)}
            <View style={styles.distanceRow}>
              <Icon name="navigation" size={14} color="#2196F3" />
              <Text style={styles.distanceText}>{service.address}</Text>
            </View>
          </View>
          
          <View style={styles.servicesList}>
              <Text style={styles.serviceText}>
                {service.description}
              </Text>
          </View>
        </Animated.View>
        
        {/* Information Section */}
        <Animated.View style={[styles.section, contentAnimatedStyle]}>
          <Text style={styles.sectionTitle}>{t('services.details.information')}</Text>
          <View style={styles.infoContainer}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.email')}</Text>
              <Text style={styles.infoValue}>{service.email}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.phone')}</Text>
              <Text style={styles.infoValue}>{service.phoneNumber}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.name')}</Text>
              <Text style={styles.infoValue}>{service?.professional?.displayName || '-'}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.company')}</Text>
              <Text style={styles.infoValue}>{service?.companyName || '-'}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.gender')}</Text>
              <Text style={styles.infoValue}>{service?.professional?.gender || '-'}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.category')}</Text>
              <Text style={styles.infoValue}>{service?.category}</Text>
            </View>
          </View>
        </Animated.View>
        
        {/* Media Section */}
        {service.media && service.media.length > 0 && (
          <Animated.View style={[styles.section, contentAnimatedStyle]}>
            <Text style={styles.sectionTitle}>{t('services.details.media')}</Text>
            <ScrollView 
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.achievementsContainer}
            >
              {service.media.map((media: Media, index: number) => (
                <View key={`achievement-${index}`} style={styles.achievementWrapper}>
                  <TouchableOpacity style={styles.achievementCard}>
                    {media.type === 'image' ? (
                      <Image 
                        source={{ uri: media.url }} 
                        style={styles.achievementImage} 
                        resizeMode="cover"
                      />
                    ) : (
                      <View style={[styles.achievementImage, styles.documentBackground]}>
                        <Text style={styles.documentText}>
                          {media.description && media.description.split('.').pop()}
                        </Text>
                      </View>
                    )}
                    
                    {/* Overlay icon based on media type */}
                    <View style={styles.mediaTypeIcon}>
                      <Icon 
                        name={
                          media.type === 'video' ? 'play' : 
                          media.type === 'document' ? 'file-text' : 
                          'image'
                        } 
                        size={20} 
                        color="#FFF" 
                      />
                    </View>
                  </TouchableOpacity>
                  
                  {/* Description if available */}
                  {media.description && (
                    <Text style={styles.mediaDescription} numberOfLines={2}>
                      {media.description}
                    </Text>
                  )}
                </View>
              ))}
            </ScrollView>
          </Animated.View>
        )}
        
        {/* Area of Intervention */}
        <Animated.View style={[styles.section, contentAnimatedStyle]}>
          <Text style={styles.sectionTitle}>{t('services.details.location')}</Text>
          <Text style={styles.areaDescription}>{service.address}</Text>
          
          {service.latitude && service.longitude && 
           !isNaN(parseFloat(service.latitude)) && 
           !isNaN(parseFloat(service.longitude)) && (
            <Animated.View style={[styles.mapContainer, mapAnimatedStyle]}>
              <MapView
                style={styles.map}
                provider={PROVIDER_GOOGLE}
                initialRegion={{
                  latitude: parseFloat(service.latitude),
                  longitude: parseFloat(service.longitude),
                  latitudeDelta: 0.06,
                  longitudeDelta: 0.06,
                }}
                provider="google"
              >
                <Marker 
                  coordinate={{
                    latitude: parseFloat(service.latitude),
                    longitude: parseFloat(service.longitude),
                  }}
                />
              </MapView>
            </Animated.View>
          )}
        </Animated.View>

        {/* --- Action Buttons --- */}
        <Animated.View style={[styles.actionButtonContainer, contentAnimatedStyle]}>
           <TouchableOpacity
            style={[styles.actionButton, styles.updateButton]}
            onPress={handleUpdatePress}
            disabled={isDeleting}
          >
             <Icon name="edit" size={18} color="#fff" style={styles.buttonIcon} />
             <Text style={styles.actionButtonText}>{t('common.buttons.update')}</Text>
          </TouchableOpacity>

           <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDeletePress}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Icon name="trash-2" size={18} color="#fff" style={styles.buttonIcon} />
                <Text style={styles.actionButtonText}>{t('common.buttons.delete')}</Text>
              </>
            )}
          </TouchableOpacity>
        </Animated.View>
        {/* --- End Action Buttons --- */}

      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF5252',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#4682B4',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  header: {
    height: 200,
    marginBottom: 8,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerImage: {
    width: '100%',
    height: '100%',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  titleSection: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  priceTag: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  priceText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingCount: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
  },
  distanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distanceText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
  },
  servicesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  serviceText: {
    fontSize: 14,
    color: '#757575',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 12,
  },
  infoContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  infoLabel: {
    fontSize: 15,
    color: '#757575',
  },
  infoValue: {
    fontSize: 15,
    color: '#212121',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginHorizontal: 12,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  amenityTag: {
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  amenityText: {
    fontSize: 14,
    color: '#616161',
  },
  achievementsContainer: {
    paddingVertical: 8,
  },
  achievementWrapper: {
    width: 140,
    marginRight: 16,
  },
  achievementCard: {
    height: 100,
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#F1F1F1',
  },
  achievementImage: {
    width: '100%',
    height: '100%',
  },
  mediaTypeIcon: {
    position: 'absolute',
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    top: '50%',
    left: '50%',
    marginLeft: -18,
    marginTop: -18,
  },
  mediaDescription: {
    fontSize: 12,
    color: '#757575',
    marginTop: 6,
    textAlign: 'center',
  },
  documentBackground: {
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentText: {
    fontSize: 12,
    color: '#424242',
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  playButton: {
    position: 'absolute',
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    top: '50%',
    left: '50%',
    marginLeft: -18,
    marginTop: -18,
  },
  areaDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 12,
  },
  mapContainer: {
    height: 180,
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 8,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  contactButtonContainer: {
    padding: 16,
    backgroundColor: 'transparent',
  },
  contactButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  contactButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    marginTop: 24,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
    marginHorizontal: 8,
  },
  updateButton: {
    backgroundColor: '#2196F3',
  },
  deleteButton: {
    backgroundColor: '#FF5252',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
});

export default ProServiceDetailsScreen;