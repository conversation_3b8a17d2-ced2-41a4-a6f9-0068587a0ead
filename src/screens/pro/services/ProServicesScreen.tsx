import React, { useCallback, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Host from '../../../../assets/Host.jpg';
import Animated, { FadeIn, FadeInUp, FadeOut } from 'react-native-reanimated';
import { Feather, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useAuthStore } from '@/stores/useAuthStore';
import { useTranslation } from 'react-i18next';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import * as Location from 'expo-location';

const { width } = Dimensions.get('window');

interface CategoryButtonProps {
  icon: string;
  label: string;
  isSelected: boolean;
}

interface ServiceData {
  uuid: string;
  title: string;
  description: string;
  price: number;
  companyName: string;
  address: string;
  longitude: string;
  latitude: string;
  category: string;
  coverImage: string;
  media: Array<{
    url: string;
    type: string;
    description: string;
  }>;
  ratings: {
    average: number;
    count: number;
  };
  professional: {
    uuid: string;
    displayName: string;
    gender: string | null;
    avatar: string;
    phoneNumber: string;
    email: string;
  };
}

// Function to calculate distance between two coordinates in kilometers
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  // Return distance with 1 decimal precision
  return Math.round(R * c * 10) / 10;
};

const CategoryButton = ({ icon, label, isSelected }: CategoryButtonProps) => {
  return (
    <TouchableOpacity style={styles.categoryButton}>
      <View style={styles.categoryIconContainer}>
        {/* Icon would be imported from your assets */}
        <Text style={styles.categoryIcon}>{icon}</Text>
      </View>
      <Text style={styles.categoryLabel}>{label}</Text>
    </TouchableOpacity>
  );
};

interface ServiceCardProps {
  service: ServiceData;
  userLocation: Location.LocationObject | null;
}

const ServiceCard = ({ service, userLocation }: ServiceCardProps) => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const { t } = useTranslation();
  const { title, description, price, coverImage, ratings, latitude, longitude } = service;
  
  // Calculate distance if user location is available
  const distance = userLocation ? 
    calculateDistance(
      userLocation.coords.latitude,
      userLocation.coords.longitude,
      parseFloat(latitude),
      parseFloat(longitude)
    ) : 
    null;
  
  // Format description to limit length
  const formattedDescription = description.length > 100 
    ? `${description.substring(0, 97)}...` 
    : description;
  
  return (
    <View style={styles.serviceCard}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => navigation.navigate('ProServiceDetails', { serviceUuid: service.uuid })}
      >
        <View style={styles.serviceCardContent}>
          <Image 
            source={{ uri: coverImage }} 
            style={styles.serviceImage} 
            resizeMode="cover"
          />
          
          <View style={styles.serviceInfo}>
            <View style={styles.serviceHeader}>
              <Text style={styles.serviceTitle} numberOfLines={1} ellipsizeMode="tail">
                {title}
              </Text>
              {distance !== null && (
                <View style={styles.distanceContainer}>
                  <MaterialIcons name="location-on" size={14} color="#888" style={styles.distanceIcon} />
                  <Text style={styles.distanceText}>
                    {t('services.serviceCard.distance', { distance })}
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={styles.serviceDescription} numberOfLines={2}>
              {formattedDescription}
            </Text>
            
            <View style={styles.serviceFooter}>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Text key={star} style={styles.starIcon}>
                    {star <= ratings.average ? '★' : '☆'}
                  </Text>
                ))}
                <Text style={styles.reviewCount}>
                  ( {ratings.count === 1 
                    ? t('services.serviceCard.reviews_one', { count: ratings.count }) 
                    : t('services.serviceCard.reviews_other', { count: ratings.count })} )
                </Text>
              </View>
              
              <LinearGradient
                colors={['#3498DB', '#D1987C']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.priceContainer}
              >
                <Text style={styles.priceText}>{price} €/h</Text>
              </LinearGradient>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const ProServicesScreen = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { setToken } = useAuthStore();
  const [refreshing, setRefreshing] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  
  const categoryData = [
    { icon: '🔧', label: t('services.categories.diy'), isSelected: true },
    { icon: '🌱', label: t('services.categories.gardening'), isSelected: false },
    { icon: '🧹', label: t('services.categories.housekeeping'), isSelected: false },
  ];
  
  // Get user location
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access location was denied');
        return;
      }

      try {
        const location = await Location.getCurrentPositionAsync({ accuracy: Location.Accuracy.Balanced });
        setUserLocation(location);
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);
  
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['listings'],
    queryFn: async () => {
      const response = await httpClient.get('/offerings/getall?onlyMine=1');
      return response.data;
    }
  });

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Refresh location when pulling to refresh
    Location.getCurrentPositionAsync({ accuracy: Location.Accuracy.Balanced })
      .then(location => setUserLocation(location))
      .catch(error => console.error('Error refreshing location:', error));
    
    refetch().finally(() => setRefreshing(false));
  }, [refetch]);

  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, [])
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('services.error')}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text>{t('services.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Animated.View
        style={styles.emptyContainer}
        entering={FadeIn}
        exiting={FadeOut}
      >
        <Text style={styles.emptyText}>{t('services.noListings')}</Text>
        <Animated.View style={styles.fab} entering={FadeInUp.delay(500).duration(500)}>
          <TouchableOpacity style={styles.fabButton} onPress={() => navigation.navigate('ProServiceCreation' as never)}>
            <Ionicons name="add" size={28} color="#fff" />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>

      
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search-outline" size={20} color="#888" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder={t('services.search')}
            placeholderTextColor="#AAA"
          />
        </View>
      </View>

      {/* <View style={styles.categoryHeader}>
        <Text style={styles.categoryTitle}>{t('services.category')}</Text>
        <TouchableOpacity>
          <Text style={styles.viewAllText}>{t('services.viewAll')}</Text>
        </TouchableOpacity>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoryScrollView}
        contentContainerStyle={styles.categoryScrollContent}
      >
        {categoryData.map((category, index) => (
          <CategoryButton
            key={index}
            icon={category.icon}
            label={category.label}
            isSelected={category.isSelected}
          />
        ))}
      </ScrollView> */}

      <FlatList
        data={data}
        keyExtractor={(item) => item.uuid}
        showsVerticalScrollIndicator={false}
        style={styles.servicesScrollView}
        contentContainerStyle={styles.servicesScrollContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        renderItem={({ item }) => (
          <ServiceCard
            service={item}
            userLocation={userLocation}
          />
        )}
      />
      <Animated.View style={styles.fab} entering={FadeInUp.delay(500).duration(500)}>
        <TouchableOpacity style={styles.fabButton} onPress={() => navigation.navigate('ProServiceCreation' as never)}>
          <Ionicons name="add" size={28} color="#fff" />
        </TouchableOpacity>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 15,
    marginBottom: 20,
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    height: 50,
    backgroundColor: '#E8EEF4',
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginRight: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filtersButton: {
    width: 50,
    height: 50,
    backgroundColor: '#E8EEF4',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersIcon: {
    width: 20,
    height: 20,
    tintColor: '#888',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  viewAllText: {
    fontSize: 14,
    color: '#888',
  },
  categoryScrollView: {
    marginBottom: 20,
  },
  categoryScrollContent: {
    paddingHorizontal: 15,
  },
  categoryButton: {
    alignItems: 'center',
    marginHorizontal: 5,
    width: 80,
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E8EEF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryIcon: {
    fontSize: 20,
  },
  categoryLabel: {
    fontSize: 14,
    color: '#333',
  },
  servicesScrollView: {
    flex: 1,
  },
  servicesScrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  serviceCard: {
    backgroundColor: '#FFF',
    borderRadius: 15,
    marginBottom: 15,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  serviceCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceImage: {
    width: width * 0.25,
    height: width * 0.25,
    minWidth: 90,
    minHeight: 90,
    maxWidth: 120,
    maxHeight: 120,
    borderRadius: 10,
    marginRight: 15,
  },
  serviceInfo: {
    flex: 1,
    minHeight: 90,
    justifyContent: 'space-between',
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E67E22',
    flex: 1,
    paddingRight: 8,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distanceIcon: {
    marginRight: 2,
  },
  distanceText: {
    fontSize: 12,
    color: '#888',
  },
  serviceDescription: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
    marginBottom: 10,
    flex: 1,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  starIcon: {
    fontSize: 16,
    color: '#FFD700',
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 12,
    color: '#888',
    marginLeft: 3,
  },
  priceContainer: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 18,
  },
  priceText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  fab: {
    position: 'absolute',
    bottom: 25,
    right: 25,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3498DB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 6,
  },
  button: {
    backgroundColor: '#927675', // Red color that's commonly used for logout/exit
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    alignSelf: 'center',
    margin: 10,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#E8EEF4',
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#888',
    textAlign: 'center',
  },
});

export default ProServicesScreen;