import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, StatusBar, Dimensions, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useMutation, useQuery } from '@tanstack/react-query';
import Animated, { FadeIn, Layout } from 'react-native-reanimated';
import LottieView from 'lottie-react-native';
import { WebView } from 'react-native-webview';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';

import { httpClient } from '@/utils/http';
import SubscriptionPlanCard from '@components/shared/SubscriptionPlanCard';
import SubscriptionButton from '@components/shared/SubscriptionButton';
import SuccessModal from '@components/shared/SuccessModal';

const { width, height } = Dimensions.get('window');

const ProSubscriptionPlansScreen = () => {
  const { t } = useTranslation();
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentUrl, setPaymentUrl] = useState(null);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [isWebViewLoading, setIsWebViewLoading] = useState(true);
  const navigation = useNavigation();

  // Fetch subscription plans
  const { 
    data: plans, 
    isLoading, 
    isError, 
    refetch 
  } = useQuery({
    queryKey: ['subscriptionPlans'],
    queryFn: async () => {
      const response = await httpClient.get('/payments/plans/getall');
      console.log(response.data);
      return response.data;
    }
  });

  // Fetch user reservation info and listing data in one place
  const { data, isLoading: checkingPayment, refetch: reCheckPayment } = useQuery({
    queryKey: ['paymentstatus', sessionId],
    queryFn: async () => {
      if (sessionId) {
        const paymentStatus: any = await httpClient.get(`/public/payments/check/${sessionId}`);
        console.log(paymentStatus.data);
        if (paymentStatus.code === 200 && paymentStatus.data.status === 'SUCCESSFUL' && paymentStatus.data.isCompleted === true) {
          setIsSuccessModalVisible(true);
        }
        return paymentStatus.data;
      } else {
        return null;
      }
    },
  });

  // Checkout mutation
  const checkoutMutation = useMutation({
    mutationFn: (planUuid) => httpClient.post(`/payments/subscriptions/checkout/${planUuid}`),
    onSuccess: (data) => {
      console.log("Payment URL:", data.data.checkoutUrl);
      setPaymentUrl(data.data.checkoutUrl);
      setSessionId(data.data.sessionId);
    },
    onError: (error) => {
      console.error("Checkout error:", error);
    }
  });

  useEffect(() => {
    if (sessionId) {
      reCheckPayment();
    }
  }, [sessionId]);

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
  };

  const handleCheckout = () => {
    checkoutMutation.mutate(selectedPlan.uuid);
  };

  const handlePaymentComplete = () => {
    setPaymentUrl(null);
    setIsSuccessModalVisible(true);

    // Hide success modal after 1.5 seconds and navigate back
    setTimeout(() => {
      setIsSuccessModalVisible(false);
      navigation.goBack();
    }, 1500);
  };

  const cancelPayment = () => {
    
    setPaymentUrl(null);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>{t('pro.subscription.loading')}</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('pro.subscription.error')}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>{t('common.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isSuccessModalVisible) {
    return (
      <SafeAreaView style={styles.container}>
        
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <View style={styles.lottieContainer}>
                <LottieView
                  source={require('../../../../assets/animations/success-animation.json')}
                  autoPlay
                  loop={false}
                  style={styles.lottieAnimation}
                />
              </View>
              
              <Text style={styles.titleText}>{t('pro.subscription.success.title')}</Text>
              
              <Text style={styles.subtitleText}>
                {t('pro.subscription.success.message')}
              </Text>
              
              {/* <TouchableOpacity 
                style={styles.continueButton}
                onPress={onClose}
              >
                <Text style={styles.continueButtonText}>Continue</Text>
              </TouchableOpacity> */}
            </View>
          </View>
      </SafeAreaView>
    )
  }

  // Show payment webview when paymentUrl is available
  if (paymentUrl) {
    return (
      <SafeAreaView style={styles.container}>
        
        
        <View style={styles.webviewHeader}>
          <TouchableOpacity onPress={cancelPayment} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.webviewTitle}>{t('pro.subscription.payment.title')}</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.webviewContainer}>
          {isWebViewLoading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color="#3498DB" />
              <Text style={styles.loadingText}>{t('pro.subscription.payment.loading')}</Text>
            </View>
          )}
          <WebView
            source={{ uri: paymentUrl }}
            style={styles.webview}
            onNavigationStateChange={(navState) => {
              console.log("WebView navigating to:", navState.url);
              // Logic to detect payment completion
              if (navState.url.includes('success')) {
                handlePaymentComplete();
              }
            }}
            onLoadStart={() => setIsWebViewLoading(true)}
            onLoadEnd={() => setIsWebViewLoading(false)}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error:', nativeEvent);
              setIsWebViewLoading(false);
            }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            scalesPageToFit={true}
            bounces={false}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Show plan selection screen when no payment is in progress
  return (
    <SafeAreaView style={styles.container}>
      
      
      <Animated.View 
        entering={FadeIn}
        layout={Layout}
        style={styles.headerContainer}
      >
        <Text style={styles.headerTitle}>{t('pro.subscription.plans.title')}</Text>
        <Text style={styles.headerSubtitle}>
          {t('pro.subscription.plans.subtitle')}
        </Text>
      </Animated.View>

      <View style={styles.plansContainer}>
        {plans.map((plan) => (
          <SubscriptionPlanCard
            key={plan.uuid}
            plan={plan}
            isSelected={selectedPlan?.uuid === plan.uuid}
            onSelect={() => handlePlanSelect(plan)}
          />
        ))}
      </View>

      <SubscriptionButton 
        isDisabled={!selectedPlan || checkoutMutation.isPending}
        onPress={handleCheckout}
        isPending={checkoutMutation.isPending}
        text={t('pro.subscription.button.proceed_checkout')}
      />

      <SuccessModal 
        visible={isSuccessModalVisible}
        onClose={() => setIsSuccessModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ebf5ff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ebf5ff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#3498DB',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ebf5ff',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#e74c3c',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#3498DB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  headerTitle: {
    color: '#DEC48F',
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 10,
  },
  headerSubtitle: {
    color: '#3498DB',
    fontSize: 16,
  },
  plansContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  webviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 2,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
  },
  webviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 30,
  },
  webviewContainer: {
    flex: 1,
    position: 'relative',
  },
  webview: {
    flex: 1,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  lottieContainer: {
    width: 200,
    height: 200,
    marginBottom: 20,
  },
  lottieAnimation: {
    width: '100%',
    height: '100%',
  },
  titleText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 25,
    textAlign: 'center',
  },
  continueButton: {
    backgroundColor: '#3498DB',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 50,
    elevation: 2,
  },
  continueButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
});

export default ProSubscriptionPlansScreen;