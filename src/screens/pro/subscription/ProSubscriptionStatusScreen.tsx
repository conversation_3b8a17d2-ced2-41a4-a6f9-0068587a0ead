import React, { useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, RefreshControl, ActivityIndicator, Platform, FlatList, Switch } from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { 
  FadeInDown, 
  FadeInRight, 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  interpolate,
  Extrapolation
} from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import * as Haptics from 'expo-haptics';
import { useTranslation } from 'react-i18next';

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

const ProSubscriptionStatusScreen = () => {
  const navigation = useNavigation();
  const scrollY = useSharedValue(0);
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  
  // Fetch subscription plans
  const {
    data: subscriptionsData,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['subscription'],
    queryFn: async () => {
      const response = await httpClient.get('/payments/subscriptions/getall');
      console.log(response.data);
      return response.data;
    }
  });
  
  // Mutation for updating auto-renewal status
  const updateAutoRenewalMutation = useMutation({
    mutationFn: async ({ subscriptionUuid, autoRenew }: { subscriptionUuid: string, autoRenew: boolean }) => {
      return await httpClient.patch(`/payments/subscriptions/${subscriptionUuid}/auto-renewal`, {
        autoRenew
      });
    },
    onSuccess: () => {
      // Invalidate and refetch subscription data
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    }
  });
  
  // Handle auto-renewal toggle
  const handleAutoRenewalToggle = useCallback((subscriptionUuid: string, currentValue: boolean) => {
    updateAutoRenewalMutation.mutate({
      subscriptionUuid,
      autoRenew: !currentValue
    });
  }, [updateAutoRenewalMutation]);
  
  // Auto-refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );
  
  // Handle pull to refresh
  const [refreshing, setRefreshing] = React.useState(false);
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  // Button press animation
  const buttonScale = useSharedValue(1);
  
  const buttonStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }],
    };
  });

  const onPressIn = () => {
    buttonScale.value = withSpring(0.95);
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const onPressOut = () => {
    buttonScale.value = withSpring(1);
  };

  // Header animation based on scroll
  const headerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      scrollY.value,
      [0, 100],
      [1, 0.9],
      Extrapolation.CLAMP
    );
    
    const translateY = interpolate(
      scrollY.value,
      [0, 100],
      [0, -10],
      Extrapolation.CLAMP
    );

    return {
      opacity,
      transform: [{ translateY }],
    };
  });

  // Function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Navigate to subscription screen
  const goToSubscription = () => {
    if (Platform.OS === 'ios') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    navigation.navigate('ProSubscriptionPlans' as never);
  };

  // Render item for FlatList
  const renderItem = useCallback(({ item, index }) => (
    <Animated.View 
      style={styles.subscriptionCard}
      entering={FadeInRight.delay(index * 200).springify()}
    >
      <View style={styles.cardHeader}>
        <View style={[
          styles.statusIndicator, 
          item.active ? styles.activeIndicator : styles.inactiveIndicator
        ]} />
        <Text style={styles.planName}>{item.plan.name}</Text>
      </View>
      
      <View style={styles.cardBody}>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('pro.subscription.status.price')}</Text>
          <Text style={styles.infoValue}>${item.plan.price}/month</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('pro.subscription.status.started')}</Text>
          <Text style={styles.infoValue}>{formatDate(item.startDate)}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('pro.subscription.status.renews')}</Text>
          <Text style={styles.infoValue}>{formatDate(item.endDate)}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('pro.subscription.status.auto_renew')}</Text>
          <View style={styles.switchContainer}>
            <Switch
              value={item.autoRenew}
              onValueChange={() => handleAutoRenewalToggle(item.id, item.autoRenew)}
              trackColor={{ false: '#767577', true: '#81b0ff' }}
              thumbColor={item.autoRenew ? '#4A90E2' : '#f4f3f4'}
            />
          </View>
        </View>
      </View>
      
      <View style={styles.cardFooter}>
        <Text style={styles.subscriptionId}>
          {t('pro.subscription.status.id')} {item.stripeSubscriptionId}
        </Text>
      </View>
    </Animated.View>
  ), [t, handleAutoRenewalToggle]);

  // Key extractor for FlatList
  const keyExtractor = useCallback((item) => item.id.toString(), []);

  // Render for empty list
  const ListEmptyComponent = useCallback(() => (
    <Animated.View 
      style={styles.emptyContainer}
      entering={FadeInDown.delay(300).springify()}
    >
      <Text style={styles.emptyText}>{t('pro.subscription.status.empty')}</Text>
    </Animated.View>
  ), [t]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>

        
        {/* Header */}
        {/* <Animated.View style={[styles.header, headerStyle]}>
          <Text style={styles.headerTitle}>Your Subscriptions</Text>
          <Text style={styles.headerSubtitle}>Manage your pro plans</Text>
        </Animated.View> */}

        {/* Content */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3498DB" />
            <Text style={styles.loadingText}>{t('pro.subscription.status.loading')}</Text>
          </View>
        ) : isError ? (
          <Animated.View 
            style={styles.errorContainer}
            entering={FadeInDown.delay(300).springify()}
          >
            <Text style={styles.errorText}>{t('pro.subscription.status.error')}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
              <Text style={styles.retryButtonText}>{t('pro.subscription.status.retry')}</Text>
            </TouchableOpacity>
          </Animated.View>
        ) : (
          <FlatList
            data={subscriptionsData}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl 
                refreshing={refreshing} 
                onRefresh={onRefresh}
                tintColor="#3498DB"
                colors={["#3498DB"]}
              />
            }
            onScroll={(event) => {
              scrollY.value = event.nativeEvent.contentOffset.y;
            }}
            scrollEventThrottle={16}
            ListEmptyComponent={ListEmptyComponent}
          />
        )}

        {/* Action Button */}
        <AnimatedTouchable
          style={[styles.button, buttonStyle]}
          onPress={goToSubscription}
          onPressIn={onPressIn}
          onPressOut={onPressOut}
          activeOpacity={0.9}
        >
          <Text style={styles.buttonText}>{t('pro.subscription.status.manage')}</Text>
        </AnimatedTouchable>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C3E50',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#2C3E50',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#DEC48F',
    opacity: 0.9,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
    flexGrow: 1, // Ensures content takes full height for empty state
  },
  subscriptionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3498DB',
    padding: 16,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  activeIndicator: {
    backgroundColor: '#2ECC71',
  },
  inactiveIndicator: {
    backgroundColor: '#E74C3C',
  },
  planName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  cardBody: {
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
  },
  cardFooter: {
    backgroundColor: '#F8F9FA',
    padding: 12,
    alignItems: 'center',
  },
  subscriptionId: {
    fontSize: 12,
    color: '#95A5A6',
  },
  button: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    backgroundColor: '#D1987C',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  loadingContainer: {
    padding: 30,
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#DEC48F',
  },
  errorContainer: {
    padding: 30,
    alignItems: 'center',
    backgroundColor: 'rgba(231, 76, 60, 0.1)',
    borderRadius: 12,
    margin: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#E74C3C',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#3498DB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
    borderRadius: 12,
    margin: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#3498DB',
  },
  switchContainer: {
    transform: [{ scale: 0.8 }],
  },
});

export default ProSubscriptionStatusScreen;