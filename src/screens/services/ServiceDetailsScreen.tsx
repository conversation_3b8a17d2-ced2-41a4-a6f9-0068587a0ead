import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Platform,
  Linking,
  ActivityIndicator,
  Modal,
  Alert,
  FlatList,
  TextInput
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  interpolate,
  withSpring,
  withDelay,
  withSequence,
  withRepeat,
  runOnJS,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from '@expo/vector-icons/Feather';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import WebView from 'react-native-webview';
import LottieView from 'lottie-react-native';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/stores/useAuthStore';

import InlinePlayer from '@/components/InlinePlayer';
import Pdf from 'react-native-pdf';


// Service type based on the provided structure
type Media = {
  description: string;
  type: string;
  url: string;
};

type Professional = {
  avatar: string;
  displayName: string;
  email: string;
  gender: string | null;
  phoneNumber: string;
  uuid: string;
};

type Ratings = {
  average: number;
  count: number;
};

type Service = {
  active: boolean;
  address: string;
  category: string;
  companyName: string;
  coverImage: string;
  description: string;
  email: string;
  latitude: string;
  longitude: string;
  media: Media[];
  phoneNumber: string;
  price: number;
  professional: Professional;
  ratings: Ratings;
  title: string;
  uuid: string;
};

// Types
type ServiceDetailsScreenProps = {
  navigation: any;
  route: any;
};

const ServiceDetailsScreen: React.FC<ServiceDetailsScreenProps> = ({ navigation, route }) => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const queryClient = useQueryClient()
  const serviceUuid: string = route.params.serviceUuid;
  const { setConversationId } = useAuthStore();

  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentOpacity = useSharedValue(0);
  const mapOpacity = useSharedValue(0);
  const toolsScale = useSharedValue(0.95);
  const ratingModalTranslateY = useSharedValue(0);
  const ratingModalHeight = useSharedValue(0);

  // Payment state
  const [paymentUrl, setPaymentUrl] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [isPaymentModalVisible, setIsPaymentModalVisible] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  const [isWebViewLoading, setIsWebViewLoading] = useState(true);
  const [pdfMedia, setPdfMedia] = useState(null);
  const [mediaModalVisible, setMediaModalVisible] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalUrl, setImageModalUrl] = useState('');
  const [inlineMedia, setInlineMedia] = useState(null);

  // Rating state
  const [isRatingModalVisible, setIsRatingModalVisible] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [ratingFeedback, setRatingFeedback] = useState('');
  const [isRatingSubmitting, setIsRatingSubmitting] = useState(false);
  const [isRatingSuccess, setIsRatingSuccess] = useState(false);
  
  // Animation values for rating stars
  const ratingScale = useSharedValue(1);
  const successOpacity = useSharedValue(0);

  // Start animations when component mounts
  useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 600, easing: Easing.out(Easing.ease) });
    contentOpacity.value = withTiming(1, { duration: 800, easing: Easing.out(Easing.ease) });
    mapOpacity.value = withDelay(400, withTiming(1, { duration: 800 }));
    toolsScale.value = withSpring(1, { damping: 12, stiffness: 90 });
  }, []);

  const { data: service, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['offering', serviceUuid],
    queryFn: async () => {
      const platform = Platform.OS;
      const response = await httpClient.get(`/offerings/getone/${serviceUuid}?platform=${platform}`);
      console.log("Service retrieved: ", response.data);
      return response.data;
    }
  });

  // Mutation to initiate payment
  const paymentMutation = useMutation({
    mutationFn: (proUuid) => httpClient.post(`/payments/host-purchase/${proUuid}`),
    onSuccess: (data) => {
      console.log("Payment URL:", data.data.checkoutUrl);
      setPaymentUrl(data.data.checkoutUrl);
      setSessionId(data.data.sessionId);
      setIsPaymentModalVisible(true);
    },
    onError: (error) => {
      console.error("Payment error:", error);
    },
  });

  // Query to check payment status
  const { data: paymentStatus, refetch: reCheckPayment } = useQuery({
    queryKey: ['paymentstatus', sessionId],
    queryFn: async () => {
      if (sessionId) {
        const response = await httpClient.get(`/public/payments/check/${sessionId}`);
        console.log("Payment status:", response.data);
        if (response.data.status === 'SUCCESSFUL' && response.data.isCompleted) {
          queryClient.invalidateQueries({ queryKey: ['offering', serviceUuid] });
          setIsSuccessModalVisible(true);
        }
        return response.data;
      }
      return null;
    },
  });

  useEffect(() => {
    if (sessionId) {
      reCheckPayment();
    }
  }, [sessionId]);

  const handlePaymentComplete = () => {
    queryClient.invalidateQueries({ queryKey: ['offering', serviceUuid] });
    setPaymentUrl(null);
    setIsPaymentModalVisible(false);
    setIsSuccessModalVisible(true);

    setTimeout(() => {
      setIsSuccessModalVisible(false);
    }, 1500);
  };

  const cancelPayment = () => {
    setPaymentUrl(null);
    setIsPaymentModalVisible(false);
  };

  const handleMediaPress = (media) => {
    if (media.type === 'pdf' || media.type === 'document' || media.type === 'image') {
      setPdfMedia(media);
      setMediaModalVisible(true);
    } else {
      setInlineMedia(media);
    }
  };

  const closeMediaModal = () => {
    setMediaModalVisible(false);
    setPdfMedia(null);
  };

  const renderMediaContent = () => {
    if (!pdfMedia) return null;
    if (pdfMedia.type === 'image') {
      return (
        <Image
          source={{ uri: pdfMedia.url }}
          style={{  width: '100%', height: '100%' }}
          resizeMode="contain"
        />
      );
    }
    return (
      <Pdf
          source={{ uri: pdfMedia.url, cache: true }}
          style={styles.mediaModalContent}
          trustAllCerts={false}
          onLoadComplete={(numberOfPages, filePath) =>
            console.log(`Number of pages: ${numberOfPages}`)
          }
          onPageChanged={(page, numberOfPages) =>
            console.log(`Current page: ${page}`)
          }
          onError={(error) => {
            console.log(error);
          }}
          onPressLink={(uri) => {
            console.log(`Link pressed: ${uri}`);
          }}
        />
    );
  };

  const renderMediaItem = ({ item }) => {
    console.log('Rendering media item:', item);
    if (item.type === 'image') {
      return (
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => handleMediaPress(item)}
        >
          <View style={styles.mediaIconContainer}>
            <Image source={{ uri: item.url }} style={styles.mediaIconContainer  } />
          </View>
          <Text style={styles.mediaText}>{item.description}</Text>
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity
        style={styles.mediaItem}
        onPress={() => handleMediaPress(item)}
      >
        <View style={styles.mediaIconContainer}>
          <Text style={styles.mediaIcon}>
            {item.type === 'video' ? '🎬' : 
            item.type === 'audio' ? '🔊' : 
            item.type === 'image' ? '🌄' : ''}
          </Text>
        </View>
        <Text style={styles.mediaText}>{item.description}</Text>
      </TouchableOpacity>
    );
  }

  // Handle contact button press
  const handleContactPress = async () => {
    toolsScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    
    try {
      // Call the API to get a conversation ID
      const response = await httpClient.post(`/chat/contact-pro/${service.professional.uuid}`);
      
      if (response.data && response.data.conversationId) {
        // Set the conversation ID in the store
        setConversationId(response.data.conversationId);
        
        // Navigate to the chat room
        navigation.navigate('HostChat', { screen: 'HostChatRoom' });
      }
    } catch (error) {
      console.error('Error initiating chat:', error);
      Alert.alert('Error', 'Could not start chat. Please try again later.');
    }
  };

  // Handle phone number press
  const handlePhonePress = () => {
    if (service.phoneNumber) {
      Linking.openURL(`tel:${service.phoneNumber}`);
    }
  };

  // Handle email press
  const handleEmailPress = () => {
    if (service.email) {
      Linking.openURL(`mailto:${service.email}`);
    }
  };

  // Render "See contact" button if email or phone number is unavailable
  const renderContactButton = () => {
    if (!service.email || !service.phoneNumber) {
      return (
        <TouchableOpacity
          style={styles.seeContactButton}
          onPress={() => {
            paymentMutation.mutate(service.professional.uuid)
          }}
        >
          <Text style={styles.seeContactButtonText}>{t('services.details.seeContact')}</Text>
        </TouchableOpacity>
      );
    }
    return null;
  };

  // Render payment modal
  const renderPaymentModal = () => {
    return (
      <SafeAreaView style={styles.paymentModalContainer}>
        <Modal
          visible={isPaymentModalVisible}
          animationType="slide"
          transparent={false}
          onRequestClose={cancelPayment}
        >
          
          
          {/* Header with Close Button */}
          <View style={styles.webviewHeader}>
            <TouchableOpacity onPress={cancelPayment} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.webviewTitle}>{t('services.details.payment.completePayment')}</Text>
            <View style={styles.placeholder} />
          </View>

          {/* WebView Container */}
          <View style={styles.webviewContainer}>
            {isWebViewLoading && (
              <View style={styles.loadingOverlay}>
                <ActivityIndicator size="large" color="#3498DB" />
                <Text style={styles.loadingText}>{t('services.details.payment.loadingPayment')}</Text>
              </View>
            )}
            <WebView
              source={{ uri: paymentUrl || '' }}
              style={styles.webview}
              onNavigationStateChange={(navState) => {
                if (navState.url.includes('success')) {
                  handlePaymentComplete();
                }
              }}
              onLoadStart={() => setIsWebViewLoading(true)}
              onLoadEnd={() => setIsWebViewLoading(false)}
              onError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView error:', nativeEvent);
                setIsWebViewLoading(false);
              }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              scalesPageToFit={true}
              bounces={false}
            />
          </View>
        </Modal>
      </SafeAreaView>
    );
  };

  // Render success modal
  const renderSuccessModal = () => {
    return (
      <View style={styles.centeredView}>
        <Modal
          visible={isSuccessModalVisible}
          animationType="fade"
          transparent={true}
          onRequestClose={() => setIsSuccessModalVisible(false)}
        >
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <View style={styles.modalView}>
              <TouchableOpacity onPress={() => setIsSuccessModalVisible(false)} style={[styles.closeButton, { backgroundColor: 'transparent', position: 'absolute', top: 10, right: 10 }]}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
              <LottieView
                source={require('../../../assets/animations/success-animation.json')}
                autoPlay
                loop={false}
                style={styles.lottieAnimation}
              />
              <Text style={styles.titleText}>{t('services.details.payment.successful')}</Text>
              <Text style={styles.subtitleText}>{t('services.details.payment.viewContactDetails')}</Text>
            </View>
          </View>
        </Modal>
      </View>
    );
  };


  // Render success modal
  const renderImageModal = ({ item }) => {
    console.log('Rendering image item:', item);
    return (
      <SafeAreaView style={styles.paymentModalContainer}>
        <Modal
          visible={imageModalVisible}
          animationType="slide"
          transparent={false}
          onRequestClose={() => setImageModalVisible(false)}
        >
          
          
          {/* Header with Close Button */}
          <View style={styles.webviewHeader}>
            <TouchableOpacity onPress={cancelPayment} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.webviewTitle}>{item.description}</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Container */}
          <View style={styles.webviewContainer}>
          <Image
              source={{ uri: item.url }}
            />
          </View>
        </Modal>
      </SafeAreaView>
    );
  };

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [
      { translateY: interpolate(headerOpacity.value, [0, 1], [-20, 0]) }
    ]
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
    transform: [
      { translateY: interpolate(contentOpacity.value, [0, 1], [20, 0]) }
    ]
  }));

  const mapAnimatedStyle = useAnimatedStyle(() => ({
    opacity: mapOpacity.value
  }));

  const toolsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: toolsScale.value }]
  }));

  // Render star rating
  const renderRating = (rating: number, maxRating: number = 5) => {
    return (
      <View style={styles.ratingContainer}>
        {[...Array(maxRating)].map((_, index) => (
          <Icon
            key={`star-${index}`}
            name="star"
            size={18}
            color={index < rating ? '#FFC107' : '#E0E0E0'}
          />
        ))}
        <Text style={styles.ratingCount}>({service.ratings.count})</Text>
      </View>
    );
  };

  // Handle back button press
  const handleBackPress = () => {
    headerOpacity.value = withTiming(0, { duration: 300 });
    contentOpacity.value = withTiming(0, { duration: 300 });
    setTimeout(() => navigation.goBack(), 300);
  };

  // Mutation to submit rating
  const ratingMutation = useMutation({
    mutationFn: async (data: { value: string; feedback: string }) => {
      const response = await httpClient.post(`/offerings/rate/${serviceUuid}`, data);
      return response.data;
    },
    onSuccess: () => {
      // Animate success and refresh data
      successOpacity.value = withTiming(1, { duration: 300 });
      setIsRatingSubmitting(false);
      setIsRatingSuccess(true);
      
      // Reset after showing success animation
      setTimeout(() => {
        successOpacity.value = withTiming(0, { duration: 300 });
        setIsRatingModalVisible(false);
        setUserRating(0);
        setRatingFeedback('');
        setIsRatingSuccess(false);
        
        // Refresh the service data to get updated ratings
        queryClient.invalidateQueries({ queryKey: ['offering', serviceUuid] });
        refetch();
      }, 2000);
    },
    onError: (error) => {
      console.error("Rating error:", error);
      setIsRatingSubmitting(false);
      Alert.alert(
        t('services.details.rating.errorTitle'),
        t('services.details.rating.errorMessage')
      );
    },
  });

  // Handle opening the rating modal
  const handleRateService = () => {
    setIsRatingModalVisible(true);
    ratingModalTranslateY.value = withSpring(0, { damping: 15, stiffness: 100 });
    ratingModalHeight.value = withSpring(1, { damping: 15, stiffness: 100 });
  };
  
  // Handle rating submission
  const handleSubmitRating = () => {
    if (userRating === 0) {
      Alert.alert(
        t('services.details.rating.selectRatingTitle'),
        t('services.details.rating.selectRatingMessage')
      );
      return;
    }
    
    setIsRatingSubmitting(true);
    ratingMutation.mutate({
      value: userRating.toString(),
      feedback: ratingFeedback
    });
  };
  
  // Animated style for the success animation
  const successAnimatedStyle = useAnimatedStyle(() => ({
    opacity: successOpacity.value,
    transform: [{ scale: interpolate(successOpacity.value, [0, 1], [0.8, 1]) }]
  }));

  // Handle star press in the rating modal
  const handleStarPress = (rating: number) => {
    setUserRating(rating);
    // Animate the rating scale
    ratingScale.value = withSequence(
      withTiming(1.2, { duration: 150 }),
      withTiming(1, { duration: 150 })
    );
  };
  
  // Render the rating stars selector
  const renderRatingSelector = () => {
    return (
      <View style={styles.ratingStarsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity 
            key={`rating-star-${star}`}
            onPress={() => handleStarPress(star)}
            style={styles.ratingStar}
          >
            <Icon
              name="star"
              size={32}
              color={star <= userRating ? '#FFC107' : '#E0E0E0'}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Animated style for the rating modal
  const ratingModalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateY: ratingModalTranslateY.value },
      { scale: ratingModalHeight.value }
    ],
    opacity: ratingModalHeight.value
  }));

  // Handle closing the rating modal
  const handleCloseRatingModal = () => {
    ratingModalTranslateY.value = withSpring(1000, { damping: 15, stiffness: 100 });
    ratingModalHeight.value = withSpring(0, { damping: 15, stiffness: 100 });
    setTimeout(() => {
      setIsRatingModalVisible(false);
      setUserRating(0);
      setRatingFeedback('');
    }, 300);
  };
  
  // Render the rating modal
  const renderRatingModal = () => {
    if (!isRatingModalVisible) return null;

    return (
      <Animated.View style={[styles.ratingModalContainer, ratingModalAnimatedStyle]}>
            {isRatingSuccess ? (
              <View style={styles.ratingSuccessContainer}>
                <LottieView
                  source={require('../../../assets/animations/success-animation.json')}
                  autoPlay
                  loop={false}
                  style={styles.lottieAnimation}
                />
                <Text style={styles.ratingSuccessText}>
                  {t('services.details.rating.thankYou')}
                </Text>
              </View>
            ) : (
              <>
                <TouchableOpacity 
                  style={styles.ratingCloseButton}
              onPress={handleCloseRatingModal}
                >
                  <Text style={styles.ratingCloseButtonText}>✕</Text>
                </TouchableOpacity>
                
                <Text style={styles.ratingModalTitle}>
                  {t('services.details.rating.title')}
                </Text>
                
                <Text style={styles.ratingModalSubtitle}>
                  {t('services.details.rating.subtitle')}
                </Text>
                
                <Animated.View 
                  style={[{ transform: [{ scale: ratingScale }] }]}
                >
                  {renderRatingSelector()}
                </Animated.View>
                
                <TextInput
                  style={styles.ratingFeedbackInput}
                  placeholder={t('services.details.rating.feedbackPlaceholder')}
                  value={ratingFeedback}
                  onChangeText={setRatingFeedback}
                  multiline
                  maxLength={200}
                />
                
                <TouchableOpacity
                  style={[
                    styles.ratingSubmitButton,
                    userRating === 0 ? styles.ratingSubmitButtonDisabled : {}
                  ]}
                  onPress={handleSubmitRating}
                  disabled={isRatingSubmitting}
                >
                  {isRatingSubmitting ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <Text style={styles.ratingSubmitButtonText}>
                      {t('services.details.rating.submit')}
                    </Text>
                  )}
                </TouchableOpacity>
              </>
            )}
          </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3498DB" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.container}>
        <Text>{t('services.details.error.errorMessage', { message: error.message })}</Text>
      </View>
    );
  }

  if (!isLoading && !isError && !service) {
    return (
      <View style={styles.container}>
        <Text>{t('services.details.serviceNotFound')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>

      
      {/* Header with Cover Image and Back Button */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity style={[styles.backButton, { top: insets.top + 16 }]} onPress={handleBackPress}>
          <Icon name="arrow-left" size={24} color="#000" />
        </TouchableOpacity>
        <Image 
          source={{ uri: service?.coverImage }} 
          style={styles.headerImage}
          resizeMode="cover"
        />
      </Animated.View>
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: insets.bottom + 20 }}
      >
        {/* Title Section */}
        <Animated.View style={[styles.titleSection, headerAnimatedStyle]}>
          <View style={styles.titleRow}>
            <Text style={styles.title}>{service.title}</Text>
          </View>
          
          <View style={styles.serviceMetaRow}>
            <View style={styles.priceTag}>
              <Text style={styles.priceText}>{service.price} € / h</Text>
            </View>
            
            <View style={styles.categoryPill}>
              <Icon name="tag" size={14} color="#757575" />
              <Text style={styles.categoryText}>{service.category}</Text>
            </View>
          </View>
          
          <View style={styles.locationRow}>
              <Icon name="map-pin" size={14} color="#2196F3" />
            <Text style={styles.locationText}>{service.address}</Text>
            </View>
          
          <View style={styles.ratingSection}>
            <View style={styles.ratingDisplay}>
              {renderRating(service.ratings.average)}
              <Text style={styles.ratingLabel}>
                {service.ratings.count > 0 
                  ? t('services.details.ratingWithCount', { count: service.ratings.count }) 
                  : t('services.details.noRatings')}
              </Text>
            </View>
            
            {(service.phoneNumber || service.email) && (
              <TouchableOpacity 
                style={styles.rateButton}
                onPress={handleRateService}
              >
                <Text style={styles.rateButtonText}>
                  {t('services.details.rating.rateService')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
          
          <Text style={styles.descriptionText}>{service.description}</Text>
        </Animated.View>

        {/* Rating Modal */}
        {renderRatingModal()}
        
        {/* Information Section */}
        <Animated.View style={[
          styles.section, 
          contentAnimatedStyle,
          isRatingModalVisible && styles.sectionHidden
        ]}>
          <Text style={styles.sectionTitle}>{t('services.details.information')}</Text>
          <View style={styles.infoContainer}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.email')}</Text>
              {service.email ? (
                <TouchableOpacity onPress={handleEmailPress} style={styles.contactLinkContainer}>
                  <Text style={styles.contactLink}>{service.email}</Text>
                  <Icon name="mail" size={16} color="#3498DB" style={styles.contactIcon} />
                </TouchableOpacity>
              ) : renderContactButton()}
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.phone')}</Text>
              {service.phoneNumber ? (
                <TouchableOpacity onPress={handlePhonePress} style={styles.contactLinkContainer}>
                  <Text style={styles.contactLink}>{service.phoneNumber}</Text>
                  <Icon name="phone" size={16} color="#3498DB" style={styles.contactIcon} />
                </TouchableOpacity>
              ) : renderContactButton()}
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.name')}</Text>
              <Text style={styles.infoValue}>{service.professional.displayName}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.company')}</Text>
              <Text style={styles.infoValue}>{service.companyName || '-'}</Text>
            </View>
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t('services.details.gender')}</Text>
              <Text style={styles.infoValue}>{service.professional.gender || '-'}</Text>
            </View>
            <View style={styles.separator} />
            
            {(service.phoneNumber || service.email) && (
              <View style={styles.contactButtonContainer}>
                <TouchableOpacity
                  style={styles.contactButton}
                  onPress={handleContactPress}
                >
                  <Icon name="message-circle" size={18} color="#fff" style={styles.contactButtonIcon} />
                  <Text style={styles.contactButtonText}>{t('services.details.chatWithPro')}</Text>
                </TouchableOpacity>
            </View>
            )}
          </View>
        </Animated.View>
        
        {/* Media Section */}
        {service?.media?.length > 0 && (
          <Animated.View style={[styles.section, contentAnimatedStyle]}>
            <Text style={styles.sectionTitle}>{t('services.details.media')}</Text>
            <FlatList
              data={service?.media}
              renderItem={renderMediaItem}
              horizontal
              showsHorizontalScrollIndicator={false}
            />
          </Animated.View>
        )}

        {/* Inline Player for Audio/Video */}
        {inlineMedia && (
          <InlinePlayer
            media={inlineMedia}
            onClose={() => setInlineMedia(null)}
          />
        )}
        
        {/* Location Section */}
        {service.latitude && service.longitude && (
          <Animated.View style={[styles.section, contentAnimatedStyle]}>
            <Text style={styles.sectionTitle}>{t('services.details.location')}</Text>
            <Text style={styles.areaDescription}>{service.address}</Text>
            
            <Animated.View style={[styles.mapContainer, mapAnimatedStyle]}>
              <MapView
                style={styles.map}
                provider={PROVIDER_GOOGLE}
                initialRegion={{
                  latitude: parseFloat(service.latitude),
                  longitude: parseFloat(service.longitude),
                  latitudeDelta: 0.06,
                  longitudeDelta: 0.06,
                }}
                provider="google"
              >
                <Marker 
                  coordinate={{
                    latitude: parseFloat(service.latitude),
                    longitude: parseFloat(service.longitude),
                  }}
                />
              </MapView>
            </Animated.View>
          </Animated.View>
        )}
      </ScrollView>

      {/* Payment Modal */}
      {renderPaymentModal()}

      {/* Success Modal */}
      {renderSuccessModal()}

      {/* Modal for PDF Media */}
      <SafeAreaView style={styles.modalContainer}>
        <Modal
          visible={mediaModalVisible}
          transparent={false}
          animationType="slide"
          onRequestClose={closeMediaModal}
          style={{ width: '100%', height: '100%' }}
        >
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={closeMediaModal} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>{pdfMedia?.title}</Text>
            <View style={styles.placeholder} />
          </View>
          <View style={styles.mediaContentContainer}>
            <Text style={styles.modalTitle}>{pdfMedia?.title}</Text>
            {renderMediaContent()}
          </View>
        </Modal>
      </SafeAreaView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    height: 200,
    marginBottom: 8,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerImage: {
    width: '100%',
    height: '100%',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  titleSection: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  titleRow: {
    marginBottom: 12,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#212121',
  },
  serviceMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  priceTag: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 10,
  },
  priceText: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  categoryPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  categoryText: {
    fontSize: 13,
    color: '#757575',
    marginLeft: 4,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
    flex: 1,
  },
  ratingSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  ratingDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 6,
  },
  ratingCount: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 2,
  },
  ratingLabel: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
  },
  rateButton: {
    backgroundColor: '#FFE082',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  rateButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#E65100',
  },
  descriptionText: {
    fontSize: 14,
    color: '#757575',
    lineHeight: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 12,
  },
  infoContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  infoLabel: {
    fontSize: 15,
    color: '#757575',
  },
  infoValue: {
    fontSize: 15,
    color: '#212121',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#EEEEEE',
    marginHorizontal: 12,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  amenityTag: {
    backgroundColor: '#F5F5F5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  amenityText: {
    fontSize: 14,
    color: '#616161',
  },
  achievementsContainer: {
    paddingVertical: 8,
  },
  achievementCard: {
    width: 140,
    height: 100,
    borderRadius: 8,
    marginRight: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  achievementImage: {
    width: '100%',
    height: '100%',
  },
  areaDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 12,
  },
  mapContainer: {
    height: 180,
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 8,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  contactButtonContainer: {
    marginTop: 16,
    alignItems: 'center',
    width: '100%',
  },
  contactButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
    width: '100%',
  },
  contactButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  seeContactButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  seeContactButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  paymentModalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  webviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#555',
  },
  webviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 30,
  },
  webviewContainer: {
    flex: 1, // Ensure the WebView container takes up the remaining space
  },
  webview: {
    flex: 1, // Ensure the WebView fills its container
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject, // Cover the entire WebView container
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#3498DB',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black backdrop
  },
  modalView: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  lottieAnimation: {
    width: 150, // Set a fixed size for the Lottie animation
    height: 150,
  },
  titleText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 25,
    textAlign: 'center',
  },
  mediaModalContent: {
    width: '100%',
    height: '100%'
  },
  // Modal Styles for PDF
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
    position: 'absolute',
    // top: 0,
    // left: 0,
    // bottom: 0,
    // right: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  modalTitle: { fontSize: 18, fontWeight: '600', color: '#333', flex: 1, textAlign: 'center' },
  mediaContentContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  mediaContainer: { flexDirection: 'row', marginBottom: 16 },
  mediaItem: { alignItems: 'center', marginRight: 16 },
  mediaIconContainer: { width: 64, height: 64, borderRadius: 8, backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center', marginBottom: 8 },
  mediaIcon: { fontSize: 24 },
  mediaText: { fontSize: 12, color: '#333', textAlign: 'center' },
  contactLinkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactLink: {
    fontSize: 16,
    color: '#3498DB',
    textDecorationLine: 'underline',
    marginRight: 8,
  },
  contactIcon: {
    marginLeft: 4,
  },
  contactButtonIcon: {
    marginRight: 8,
  },
  ratingModalContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  ratingCloseButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  ratingCloseButtonText: {
    fontSize: 22,
    color: '#757575',
  },
  ratingModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
    marginTop: 10,
    textAlign: 'center',
  },
  ratingModalSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 20,
    textAlign: 'center',
  },
  ratingStarsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  ratingStar: {
    marginHorizontal: 8,
  },
  ratingFeedbackInput: {
    width: '100%',
    height: 100,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    textAlignVertical: 'top',
    fontSize: 16,
    marginBottom: 20,
  },
  ratingSubmitButton: {
    backgroundColor: '#2196F3',
    width: '100%',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  ratingSubmitButtonDisabled: {
    backgroundColor: '#BDBDBD',
  },
  ratingSubmitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  ratingSuccessContainer: {
    alignItems: 'center',
    padding: 20,
  },
  ratingSuccessText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#388E3C',
    textAlign: 'center',
    marginTop: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionHidden: {
    opacity: 0,
    transform: [{ translateY: 100 }],
  },
});

export default ServiceDetailsScreen;
