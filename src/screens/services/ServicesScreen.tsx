import React, { useC<PERSON>back, useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useQuery } from '@tanstack/react-query';
import { httpClient } from '@/utils/http';
import { useTranslation } from 'react-i18next';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import * as Location from 'expo-location';


const { width } = Dimensions.get('window');

interface CategoryButtonProps {
  icon: string;
  label: string;
  isSelected: boolean;
}

interface ServiceData {
  uuid: string;
  title: string;
  description: string;
  price: number;
  companyName: string;
  address: string;
  longitude: string;
  latitude: string;
  category: string;
  coverImage: string;
  media: Array<{
    url: string;
    type: string;
    description: string;
  }>;
  ratings: {
    average: number;
    count: number;
  };
  professional: {
    uuid: string;
    displayName: string;
    gender: string | null;
    avatar: string;
    phoneNumber: string;
    email: string;
  };
}

// Function to calculate distance between two coordinates in kilometers
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  // Return distance with 1 decimal precision
  return Math.round(R * c * 10) / 10;
};

const CategoryButton = ({ icon, label, isSelected }: CategoryButtonProps) => {
  return (
    <TouchableOpacity style={styles.categoryButton}>
      <View style={styles.categoryIconContainer}>
        {/* Icon would be imported from your assets */}
        <Text style={styles.categoryIcon}>{icon}</Text>
      </View>
      <Text style={styles.categoryLabel}>{label}</Text>
    </TouchableOpacity>
  );
};

interface ServiceCardProps {
  service: ServiceData;
  userLocation: Location.LocationObject | null;
}

const ServiceCard = ({ service, userLocation }: ServiceCardProps) => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const { t } = useTranslation();
  const { title, description, price, coverImage, ratings, latitude, longitude } = service;

  // Calculate distance if user location is available
  const distance = userLocation ?
    calculateDistance(
      userLocation.coords.latitude,
      userLocation.coords.longitude,
      parseFloat(latitude),
      parseFloat(longitude)
    ) :
    null;

  // Format description to limit length
  const formattedDescription = description.length > 100
    ? `${description.substring(0, 97)}...`
    : description;

  return (
    <View style={styles.serviceCard}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => navigation.navigate('ServiceDetails', { serviceUuid: service.uuid })}
      >
        <View style={styles.serviceCardContent}>
          <Image
            source={{ uri: coverImage }}
            style={styles.serviceImage}
            resizeMode="cover"
          />

          <View style={styles.serviceInfo}>
            <View style={styles.serviceHeader}>
              <Text style={styles.serviceTitle} numberOfLines={1} ellipsizeMode="tail">
                {title}
              </Text>
              {distance !== null && (
                <View style={styles.distanceContainer}>
                  <MaterialIcons name="location-on" size={14} color="#888" style={styles.distanceIcon} />
                  <Text style={styles.distanceText}>
                    {t('services.serviceCard.distance', { distance })}
                  </Text>
                </View>
              )}
            </View>

            <Text style={styles.serviceDescription} numberOfLines={2}>
              {formattedDescription}
            </Text>

            <View style={styles.serviceFooter}>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Text key={star} style={styles.starIcon}>
                    {star <= ratings.average ? '★' : '☆'}
                  </Text>
                ))}
                <Text style={styles.reviewCount}>
                  ( {ratings.count === 1
                    ? t('services.serviceCard.reviews_one', { count: ratings.count })
                    : t('services.serviceCard.reviews_other', { count: ratings.count })} )
                </Text>
              </View>

              <LinearGradient
                colors={['#3498DB', '#D1987C']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.priceContainer}
              >
                <Text style={styles.priceText}>{price} €/h</Text>
              </LinearGradient>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

// Define available categories
const CATEGORIES = [
  'All',
  'Cleaning Service',
  'Plumbing',
  'Electrical',
  'Gardening',
  'Painting',
  'Carpentry',
  'Moving',
  'Appliance Repair',
  'Other'
];



// Define filter interface
interface Filters {
  searchQuery: string;
  selectedCategory: string;
  minPrice: number;
  maxPrice: number;
  minRating: number;
  maxDistance: number | null;
  sortBy: 'price' | 'rating' | 'distance' | null;
  sortOrder: 'asc' | 'desc';
}

const ServicesScreen = () => {
  const { t } = useTranslation();
  const [refreshing, setRefreshing] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [filterModalVisible, setFilterModalVisible] = useState(false);

  // Filter states
  const [filters, setFilters] = useState<Filters>({
    searchQuery: '',
    selectedCategory: 'All',
    minPrice: 0,
    maxPrice: 1000,
    minRating: 0,
    maxDistance: null,
    sortBy: null,
    sortOrder: 'asc'
  });

  // Temporary filters for the modal
  const [tempFilters, setTempFilters] = useState<Filters>({...filters});

  // Track if any filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      filters.searchQuery !== '' ||
      filters.selectedCategory !== 'All' ||
      filters.minPrice > 0 ||
      filters.maxPrice < 1000 ||
      filters.minRating > 0 ||
      filters.maxDistance !== null ||
      filters.sortBy !== null
    );
  }, [filters]);

  // Get user location
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access location was denied');
        return;
      }

      try {
        const location = await Location.getCurrentPositionAsync({ accuracy: Location.Accuracy.Balanced });
        setUserLocation(location);
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['offering', 'get-all'],
    queryFn: async () => {
      const response = await httpClient.get<{ data: ServiceData[] }>('/offerings/getall');
      console.log("Services retrieved: ", response.data);
      return response.data;
    }
  });

  // Apply filters to the data
  const filteredData = useMemo(() => {
    if (!data) return [];

    return data.filter(service => {
      // Search query filter
      if (filters.searchQuery && !service.title.toLowerCase().includes(filters.searchQuery.toLowerCase()) &&
          !service.description.toLowerCase().includes(filters.searchQuery.toLowerCase()) &&
          !service.companyName.toLowerCase().includes(filters.searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.selectedCategory !== 'All' && service.category !== filters.selectedCategory) {
        return false;
      }

      // Price filter
      if (service.price < filters.minPrice || service.price > filters.maxPrice) {
        return false;
      }

      // Rating filter
      if (service.ratings.average < filters.minRating) {
        return false;
      }

      // Distance filter
      if (filters.maxDistance !== null && userLocation) {
        const distance = calculateDistance(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          parseFloat(service.latitude),
          parseFloat(service.longitude)
        );
        if (distance > filters.maxDistance) {
          return false;
        }
      }

      return true;
    });
  }, [data, filters, userLocation]);

  // Sort the filtered data
  const sortedData = useMemo(() => {
    if (!filteredData) return [];

    return [...filteredData].sort((a, b) => {
      if (filters.sortBy === 'price') {
        return filters.sortOrder === 'asc' ? a.price - b.price : b.price - a.price;
      }

      if (filters.sortBy === 'rating') {
        return filters.sortOrder === 'asc'
          ? a.ratings.average - b.ratings.average
          : b.ratings.average - a.ratings.average;
      }

      if (filters.sortBy === 'distance' && userLocation) {
        const distanceA = calculateDistance(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          parseFloat(a.latitude),
          parseFloat(a.longitude)
        );

        const distanceB = calculateDistance(
          userLocation.coords.latitude,
          userLocation.coords.longitude,
          parseFloat(b.latitude),
          parseFloat(b.longitude)
        );

        return filters.sortOrder === 'asc' ? distanceA - distanceB : distanceB - distanceA;
      }

      return 0;
    });
  }, [filteredData, filters.sortBy, filters.sortOrder, userLocation]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Refresh location when pulling to refresh
    Location.getCurrentPositionAsync({ accuracy: Location.Accuracy.Balanced })
      .then(location => setUserLocation(location))
      .catch(error => console.error('Error refreshing location:', error));

    refetch().finally(() => setRefreshing(false));
  }, [refetch]);

  useFocusEffect(
    React.useCallback(() => {
      refetch();
      // Set status bar style when screen comes into focus
      StatusBar.setBarStyle('light-content', true);
    }, [])
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4682B4" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{t('services.error')}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text>{t('services.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Animated.View
        style={styles.emptyContainer}
        entering={FadeIn}
        exiting={FadeOut}
      >
        <Text style={styles.emptyText}>{t('services.noListings')}</Text>
      </Animated.View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search-outline" size={20} color="#888" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder={t('services.search')}
            placeholderTextColor="#AAA"
            value={filters.searchQuery}
            onChangeText={(text) => setFilters(prev => ({...prev, searchQuery: text}))}
          />
          {filters.searchQuery ? (
            <TouchableOpacity
              onPress={() => setFilters(prev => ({...prev, searchQuery: ''}))}
              style={styles.clearSearchButton}
            >
              <Ionicons name="close-circle" size={20} color="#888" />
            </TouchableOpacity>
          ) : null}
        </View>

        <TouchableOpacity
          style={[styles.filterButton, hasActiveFilters && styles.filterButtonActive]}
          onPress={() => {
            setTempFilters({...filters});
            setFilterModalVisible(true);
          }}
        >
          <Ionicons
            name="options-outline"
            size={22}
            color={hasActiveFilters ? "#fff" : "#333"}
          />
        </TouchableOpacity>
      </View>

      {/* Filter chips - show active filters */}
      {hasActiveFilters && (
        <View style={styles.filterChipsWrapper}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterChipsContent}
            bounces={false}
          >
            {filters.selectedCategory !== 'All' && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {filters.selectedCategory}
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, selectedCategory: 'All'}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            {filters.minPrice > 0 && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {t('services.min')}: {filters.minPrice}€
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, minPrice: 0}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            {filters.maxPrice < 1000 && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {t('services.max')}: {filters.maxPrice}€
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, maxPrice: 1000}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            {filters.minRating > 0 && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {filters.minRating}+ ★
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, minRating: 0}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            {filters.maxDistance !== null && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {filters.maxDistance} km
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, maxDistance: null}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            {filters.sortBy && (
              <View style={styles.filterChip}>
                <Text style={styles.filterChipText}>
                  {filters.sortBy === 'price' ? t('services.price') :
                   filters.sortBy === 'rating' ? t('services.rating') : t('services.distance')}
                  {filters.sortOrder === 'asc' ? ' ▲' : ' ▼'}
                </Text>
                <TouchableOpacity
                  onPress={() => setFilters(prev => ({...prev, sortBy: null}))}
                  style={styles.filterChipClose}
                >
                  <Ionicons name="close" size={12} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            <TouchableOpacity
              style={styles.clearAllChip}
              onPress={() => setFilters({
                searchQuery: '',
                selectedCategory: 'All',
                minPrice: 0,
                maxPrice: 1000,
                minRating: 0,
                maxDistance: null,
                sortBy: null,
                sortOrder: 'asc'
              })}
            >
              <Text style={styles.clearAllChipText}>{t('services.clearAll')}</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      )}

      {/* <View style={styles.categoryHeader}>
        <Text style={styles.categoryTitle}>{t('services.category')}</Text>
        <TouchableOpacity>
          <Text style={styles.viewAllText}>{t('services.viewAll')}</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryScrollView}
        contentContainerStyle={styles.categoryScrollContent}
      >
        {categoryData.map((category, index) => (
          <CategoryButton
            key={index}
            icon={category.icon}
            label={category.label}
            isSelected={category.isSelected}
          />
        ))}
      </ScrollView> */}

      <FlatList
        data={sortedData}
        keyExtractor={(item) => item.uuid}
        showsVerticalScrollIndicator={false}
        style={styles.servicesScrollView}
        contentContainerStyle={styles.servicesScrollContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        renderItem={({ item }) => (
          <ServiceCard
            service={item}
            userLocation={userLocation}
          />
        )}
        ListEmptyComponent={
          <View style={styles.emptyResultsContainer}>
            <Text style={styles.emptyResultsText}>
              {hasActiveFilters
                ? t('services.noFilterResults')
                : t('services.noListings')}
            </Text>
            {hasActiveFilters && (
              <TouchableOpacity
                style={styles.clearFiltersButton}
                onPress={() => setFilters({
                  searchQuery: '',
                  selectedCategory: 'All',
                  minPrice: 0,
                  maxPrice: 1000,
                  minRating: 0,
                  maxDistance: null,
                  sortBy: null,
                  sortOrder: 'asc'
                })}
              >
                <Text style={styles.clearFiltersButtonText}>
                  {t('services.clearFilters')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />

      {/* Filter Modal */}
      <Modal
        visible={filterModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('services.filters')}</Text>
              <TouchableOpacity
                onPress={() => setFilterModalVisible(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {/* Category Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('services.category')}</Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.categoryScrollView}
                >
                  {CATEGORIES.map((category) => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.categoryFilterButton,
                        tempFilters.selectedCategory === category && styles.categoryFilterButtonActive
                      ]}
                      onPress={() => setTempFilters(prev => ({...prev, selectedCategory: category}))}
                    >
                      <Text
                        style={[
                          styles.categoryFilterButtonText,
                          tempFilters.selectedCategory === category && styles.categoryFilterButtonTextActive
                        ]}
                      >
                        {category}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Price Range Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('services.priceRange')}</Text>
                <View style={styles.priceInputsContainer}>
                  <View style={styles.priceInputWrapper}>
                    <Text style={styles.priceInputLabel}>{t('services.min')}</Text>
                    <TextInput
                      style={styles.priceInput}
                      value={tempFilters.minPrice.toString()}
                      onChangeText={(text) => {
                        const value = parseInt(text) || 0;
                        setTempFilters(prev => ({...prev, minPrice: value}));
                      }}
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={styles.priceInputDivider} />
                  <View style={styles.priceInputWrapper}>
                    <Text style={styles.priceInputLabel}>{t('services.max')}</Text>
                    <TextInput
                      style={styles.priceInput}
                      value={tempFilters.maxPrice.toString()}
                      onChangeText={(text) => {
                        const value = parseInt(text) || 0;
                        setTempFilters(prev => ({...prev, maxPrice: value}));
                      }}
                      keyboardType="numeric"
                    />
                  </View>
                </View>
              </View>

              {/* Rating Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('services.minRating')}</Text>
                <View style={styles.ratingButtonsContainer}>
                  {[0, 1, 2, 3, 4, 5].map((rating) => (
                    <TouchableOpacity
                      key={rating}
                      style={[
                        styles.ratingButton,
                        tempFilters.minRating === rating && styles.ratingButtonActive
                      ]}
                      onPress={() => setTempFilters(prev => ({...prev, minRating: rating}))}
                    >
                      <Text style={styles.ratingButtonText}>
                        {rating === 0 ? t('services.any') : `${rating}+`}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Distance Filter */}
              {userLocation && (
                <View style={styles.filterSection}>
                  <Text style={styles.filterSectionTitle}>{t('services.maxDistance')}</Text>
                  <View style={styles.distanceButtonsContainer}>
                    {[null, 5, 10, 25, 50].map((distance) => (
                      <TouchableOpacity
                        key={distance === null ? 'any' : distance}
                        style={[
                          styles.distanceButton,
                          tempFilters.maxDistance === distance && styles.distanceButtonActive
                        ]}
                        onPress={() => setTempFilters(prev => ({...prev, maxDistance: distance}))}
                      >
                        <Text style={styles.distanceButtonText}>
                          {distance === null ? t('services.any') : `${distance} km`}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

              {/* Sort By Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>{t('services.sortBy')}</Text>
                <View style={styles.sortButtonsContainer}>
                  <TouchableOpacity
                    style={[
                      styles.sortButton,
                      tempFilters.sortBy === 'price' && styles.sortButtonActive
                    ]}
                    onPress={() => setTempFilters(prev => ({
                      ...prev,
                      sortBy: 'price',
                      sortOrder: prev.sortBy === 'price' && prev.sortOrder === 'asc' ? 'desc' : 'asc'
                    }))}
                  >
                    <Text style={styles.sortButtonText}>
                      {t('services.price')} {tempFilters.sortBy === 'price' ?
                        (tempFilters.sortOrder === 'asc' ? ' ▲' : ' ▼') : ''}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.sortButton,
                      tempFilters.sortBy === 'rating' && styles.sortButtonActive
                    ]}
                    onPress={() => setTempFilters(prev => ({
                      ...prev,
                      sortBy: 'rating',
                      sortOrder: prev.sortBy === 'rating' && prev.sortOrder === 'asc' ? 'desc' : 'asc'
                    }))}
                  >
                    <Text style={styles.sortButtonText}>
                      {t('services.rating')} {tempFilters.sortBy === 'rating' ?
                        (tempFilters.sortOrder === 'asc' ? ' ▲' : ' ▼') : ''}
                    </Text>
                  </TouchableOpacity>

                  {userLocation && (
                    <TouchableOpacity
                      style={[
                        styles.sortButton,
                        tempFilters.sortBy === 'distance' && styles.sortButtonActive
                      ]}
                      onPress={() => setTempFilters(prev => ({
                        ...prev,
                        sortBy: 'distance',
                        sortOrder: prev.sortBy === 'distance' && prev.sortOrder === 'asc' ? 'desc' : 'asc'
                      }))}
                    >
                      <Text style={styles.sortButtonText}>
                        {t('services.distance')} {tempFilters.sortBy === 'distance' ?
                          (tempFilters.sortOrder === 'asc' ? ' ▲' : ' ▼') : ''}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={() => setTempFilters({
                  searchQuery: '',
                  selectedCategory: 'All',
                  minPrice: 0,
                  maxPrice: 1000,
                  minRating: 0,
                  maxDistance: null,
                  sortBy: null,
                  sortOrder: 'asc'
                })}
              >
                <Text style={styles.resetButtonText}>{t('services.reset')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.applyButton}
                onPress={() => {
                  setFilters({...tempFilters});
                  setFilterModalVisible(false);
                }}
              >
                <Text style={styles.applyButtonText}>{t('services.apply')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 15,
    marginBottom: 5,
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    height: 50,
    backgroundColor: '#E8EEF4',
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginRight: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearSearchButton: {
    padding: 5,
  },
  filterButton: {
    width: 50,
    height: 50,
    backgroundColor: '#E8EEF4',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#4A90E2',
  },
  filterChipsWrapper: {
    paddingVertical: 4,
    marginBottom: 5,
  },
  filterChipsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8EEF4',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    marginRight: 6,
    height: 26,
  },
  filterChipText: {
    fontSize: 12,
    color: '#333',
  },
  filterChipClose: {
    padding: 1,
  },
  clearAllChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    height: 26,
  },
  clearAllChipText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  emptyResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyResultsText: {
    fontSize: 18,
    color: '#888',
    textAlign: 'center',
    marginBottom: 20,
  },
  clearFiltersButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: '#4A90E2',
    borderRadius: 6,
    height: 32,
  },
  clearFiltersButtonText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '500',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#eaeaea',
  },

  // Filter section styles
  filterSection: {
    marginBottom: 20,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },

  // Category filter styles
  categoryFilterButton: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E8EEF4',
    marginRight: 6,
    marginBottom: 6,
    height: 28,
  },
  categoryFilterButtonActive: {
    backgroundColor: '#4A90E2',
  },
  categoryFilterButtonText: {
    fontSize: 12,
    color: '#333',
  },
  categoryFilterButtonTextActive: {
    color: '#fff',
  },

  // Price filter styles
  priceInputsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceInputWrapper: {
    flex: 1,
  },
  priceInputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  priceInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    backgroundColor: '#f9f9f9',
  },
  priceInputDivider: {
    width: 20,
  },

  // Rating filter styles
  ratingButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  ratingButton: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E8EEF4',
    marginRight: 6,
    marginBottom: 6,
    height: 28,
  },
  ratingButtonActive: {
    backgroundColor: '#4A90E2',
  },
  ratingButtonText: {
    fontSize: 12,
    color: '#333',
  },

  // Distance filter styles
  distanceButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  distanceButton: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E8EEF4',
    marginRight: 6,
    marginBottom: 6,
    height: 28,
  },
  distanceButtonActive: {
    backgroundColor: '#4A90E2',
  },
  distanceButtonText: {
    fontSize: 12,
    color: '#333',
  },

  // Sort filter styles
  sortButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  sortButton: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E8EEF4',
    marginRight: 6,
    marginBottom: 6,
    height: 28,
  },
  sortButtonActive: {
    backgroundColor: '#4A90E2',
  },
  sortButtonText: {
    fontSize: 12,
    color: '#333',
  },

  // Footer button styles
  resetButton: {
    flex: 1,
    paddingVertical: 6,
    backgroundColor: '#E8EEF4',
    borderRadius: 6,
    alignItems: 'center',
    marginRight: 8,
    height: 32,
  },
  resetButtonText: {
    fontSize: 13,
    color: '#333',
  },
  applyButton: {
    flex: 1,
    paddingVertical: 6,
    backgroundColor: '#4A90E2',
    borderRadius: 6,
    alignItems: 'center',
    height: 32,
  },
  applyButtonText: {
    fontSize: 13,
    color: '#fff',
    fontWeight: '500',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  viewAllText: {
    fontSize: 14,
    color: '#888',
  },
  categoryScrollView: {
    marginBottom: 20,
  },
  categoryScrollContent: {
    paddingHorizontal: 15,
  },
  categoryButton: {
    alignItems: 'center',
    marginHorizontal: 5,
    width: 80,
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E8EEF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryIcon: {
    fontSize: 20,
  },
  categoryLabel: {
    fontSize: 14,
    color: '#333',
  },
  servicesScrollView: {
    flex: 1,
    marginTop: 5,
  },
  servicesScrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  serviceCard: {
    backgroundColor: '#FFF',
    borderRadius: 15,
    marginBottom: 15,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  serviceCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceImage: {
    width: width * 0.25,
    height: width * 0.25,
    minWidth: 90,
    minHeight: 90,
    maxWidth: 120,
    maxHeight: 120,
    borderRadius: 10,
    marginRight: 15,
  },
  serviceInfo: {
    flex: 1,
    minHeight: 90,
    justifyContent: 'space-between',
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E67E22',
    flex: 1,
    paddingRight: 8,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distanceIcon: {
    marginRight: 2,
  },
  distanceText: {
    fontSize: 12,
    color: '#888',
  },
  serviceDescription: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
    marginBottom: 10,
    flex: 1,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  starIcon: {
    fontSize: 16,
    color: '#FFD700',
    marginRight: 2,
  },
  reviewCount: {
    fontSize: 12,
    color: '#888',
    marginLeft: 3,
  },
  priceContainer: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 18,
  },
  priceText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: 'red',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#E8EEF4',
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#888',
    textAlign: 'center',
  },
});

export default ServicesScreen;