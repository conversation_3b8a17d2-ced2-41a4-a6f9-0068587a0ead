import { View, Text, StyleSheet, ScrollView, SafeAreaView } from 'react-native'
import React from 'react'
import NotificationTestPanel from '@/components/NotificationTestPanel'
import { useTranslation } from 'react-i18next'

const SettingsScreen = () => {
  const { t } = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{t('common.settings_screen.title', 'Settings')}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('common.settings_screen.notifications', 'Notifications')}</Text>
          <NotificationTestPanel />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#333',
  },
  section: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#555',
    marginHorizontal: 16,
    marginBottom: 8,
  },
});

export default SettingsScreen