import { AppConstants } from '@/constants/default';
import { storage } from '@/utils/storage';
import { create } from 'zustand';

interface AuthState {
    role: string;
    setRole: (role: string) => void;

    token: string;
    setToken: (token: string) => void;

    userContact: string;
    setUserContact: (contact: string) => void;

    loginType: string;
    setLoginType: (loginType: string) => void;

    onboardingCompleted: boolean;
    setOnboardingCompleted: (onboardingCompleted: boolean) => void;

    userInfo: string;
    setUserInfo: (userInfo: string) => void;

    userCode: string;
    setUserCode: (userCode: string) => void;

    reservation: string;
    setReservation: (reservation: string) => void;

    conversationId: string;
    setConversationId: (conversationId: string) => void;

    notifToken: string;
    setNotifToken: (notifToken: string) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
    role: storage.getString(AppConstants.USER_ROLE_KEY) || '', // Initialize from storage
    setRole: (role) => {
        storage.set(AppConstants.USER_ROLE_KEY, role); // Persist role
        set({ role });
    },
    token: storage.getString(AppConstants.TOKEN_KEY) || '', // Initialize from storage
    setToken: (token) => {
        storage.set(AppConstants.TOKEN_KEY, token); // Persist token
        set({ token });
    },

    userContact: '',
    setUserContact: (userContact) => set({ userContact }),

    loginType: '',
    setLoginType: (loginType) => set({ loginType }),

    onboardingCompleted: storage.getBoolean(AppConstants.ONBOARDING_COMPLETED_KEY) || false,
    setOnboardingCompleted: (onboardingCompleted) => {
        storage.set(AppConstants.ONBOARDING_COMPLETED_KEY, onboardingCompleted);
        set({ onboardingCompleted });
    },

    userInfo: '',
    setUserInfo: (userInfo) => {
        storage.set(AppConstants.USER_INFO_KEY, userInfo);
        set({ userInfo });
    },

    userCode: '',
    setUserCode: (userCode) => set({ userCode }),

    reservation: '',
    setReservation: (reservation) => set({ reservation }),

    conversationId: '',
    setConversationId: (conversationId) => set({ conversationId }),

    notifToken: '',
    setNotifToken: (notifToken) => set({ notifToken }),
}));