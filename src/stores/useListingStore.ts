import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { storage } from '@/utils/storage';

// Types for the listing data
interface Host {
  uuid: string;
  displayName: string;
  phoneNumber: string;
  email: string;
}

interface Media {
  url: string;
  type: string;
  description: string;
}

interface PointOfInterest {
  id: number;
  name: string;
  placeId: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface Listing {
  id: number;
  uuid: string;
  title: string;
  description: string;
  capacity: string;
  longitude: number;
  latitude: number;
  address: string;
  amenities: string[];
  host: Host;
  coverImage: string;
  media: Media[];
  'co-hosts': Host[]; // Array of Host objects
  pointsOfInterest: PointOfInterest[];
}

interface ListingState {
  listing: Listing | null;
  isLoading: boolean;
  isError: boolean;
  
  // Actions
  setListing: (listing: Listing | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsError: (isError: boolean) => void;
  
  // Reset state
  reset: () => void;
}

// Create the store with persistence using MMKV
const useListingStore = create<ListingState>()(
  persist(
    (set) => ({
      listing: null,
      isLoading: false,
      isError: false,
      
      setListing: (listing) => set({ listing }),
      setIsLoading: (isLoading) => set({ isLoading }),
      setIsError: (isError) => set({ isError }),
      
      reset: () => set({ 
        listing: null,
        isLoading: false, 
        isError: false 
      }),
    }),
    {
      name: 'listing-storage', // key in MMKV storage
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          const value = storage.getString(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: (name, value) => {
          storage.set(name, JSON.stringify(value));
        },
        removeItem: (name) => {
          storage.delete(name);
        },
      })),
    }
  )
);

export default useListingStore;