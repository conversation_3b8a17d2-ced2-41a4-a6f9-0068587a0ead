// Type definitions for our navigation structure
export type RootStackParamList = {
    RoleSelection: undefined;
    Authentication: undefined;
    Onboarding: undefined;
    UserTabs: undefined;
    OTP: undefined;
    ProAuthentication: undefined;
    ProOTP: undefined;
    HostTabs: undefined;
    RegisterProfile: { userInfo: any };
};

export type UserTabsParamList = {
    Accommodation: undefined;
    Activities: undefined;
    Chat: undefined;
    Profile: undefined;
};

export type ActivitiesStackParamList = {
    ActivityList: undefined;
    ActivityDetails: { id: string };
    ActivityMap: undefined;
};

export type ChatStackParamList = {
    ChatList: undefined;
    ChatRoom: { hostId: string };
};

export type HostTabsParamList = {
    HostAccommodation: undefined;
    HostProServices: undefined;
    HostChat: undefined;
};

export type ProTabsParamList = {
    ProServices: undefined;
    ProSubscription: undefined;
    ProChat: undefined;
}