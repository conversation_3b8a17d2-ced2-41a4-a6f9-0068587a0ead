export interface TranslationKeys {
    // Global translations
    phone: string,
    email: string,
    phone_prompt: string,
    email_prompt: string,
    send_otp: string,

    // Bookings translations
    bookings: {
      title: string,
      noBookings: string,
      loading: string,
      error: string,
      retry: string,
      addBooking: string,
      unknownClient: string,
      noContactInfo: string,
      noEmail: string,
      guests: string,
      dates: string
    },

    // Welcome screen translations
    welcome_title: string,
    welcome_message: string,
    guest_card_title: string,
    guest_card_message: string,
    host_card_title: string,
    host_card_message: string,
    pro_card_title: string,
    pro_card_message: string,

    // guest authentication screen translations
    guest_login_title: string,
    guest_login_message: string,
    guest_login_button: string,

    // OTP screen translations
    otp_title: string,
    otp_message: string,
    otp_button: string,

    // guest onboarding screen translations
    guest_onboarding_slide1_title: string,
    guest_onboarding_slide1_message: string,
    guest_onboarding_slide2_title: string,
    guest_onboarding_slide2_message: string,
    guest_onboarding_slide3_title: string,
    guest_onboarding_slide3_message: string,
    guest_onboarding_slide4_title: string,
    guest_onboarding_slide4_message: string,
    onboarding_next_button: string,
    onboarding_skip_button: string,
    onboarding_done_button: string,

    // Language selector translations
    language_selector: {
      title: string,
      button: string,
      languages: {
        en: string,
        fr: string
      }
    },

    host_accommodation_title: string,
    host_services_title: string,
    host_chat_title: string,

    // Pro authentication screen translations
    pro_authentication_title: string,

    // Points of Interest Section translations
    poi: {
      search_placeholder: string,
      no_suggestions: string,
      add_place: string,
      remove_place: string,
      location_permission_denied: string,
      location_error: string,
      loading: string,
      no_places_added: string,
      errors: {
        invalid_coordinates: string,
        invalid_place_data: string,
        missing_place_id: string,
        api_error: string,
        add_failed: string,
        select_failed: string,
        remove_failed: string,
        render_failed: string
      },
      fallbacks: {
        unknown_place: string,
        unknown_address: string,
        selected_place: string,
        unnamed_place: string
      }
    },

    // Photo Section translations
    photo: {
      title: string,
      add_photo: string,
      add_photo_placeholder: string
    },

    // Media Section translations
    media: {
      title: string,
      subtitle: string,
      description_placeholder: string,
      add_image: string,
      add_video: string,
      add_document: string,
      remove_media: string
    },

    // Welcome Screen translations
    welcome: {
      title: string,
      subtitle: string,
    guest: {
        title: string,
        description: string
      },
    host: {
        title: string,
        description: string
      },
    pro: {
        title: string,
        description: string
      }
    },

    // Location Section translations
    location: {
      title: string,
      subtitle: string,
      address_placeholder: string,
      map_placeholder: string,
      get_current_location: string,
      error: {
        geocoding_failed: string,
        location_permission_denied: string
      }
    },

    // General Information Section translations
    general_info: {
      title: string,
      title_placeholder: string,
      description_placeholder: string,
      capacity_placeholder: string
    },

    // Amenities Section translations
    amenities: {
      title: string,
      subtitle: string,
      items: {
        wifi: string,
        parking: string,
        pool: string,
        kitchen: string,
        ac: string,
        tv: string,
        elevator: string,
        gym: string,
        laundry: string,
        breakfast: string,
        workspace: string,
        security: string,
        smoking: string,
        pets: string,
        wheelchair: string
      }
    },

    // Housing Select translations
    housing_select: {
      title: string,
      placeholder: string,
      loading: string,
      error: string,
      retry: string,
      no_properties: string,
      capacity: string
    },

    // Subscription translations
    subscription: {
      button: {
        select_plan: string,
        proceed_checkout: string
      },
      plan: {
        duration: string,
        selected: string
      },
      success: {
        title: string,
        subtitle: string,
        continue: string
      }
    },

    // Points of Interest Preview translations
    poi_preview: {
      title: string,
      loading: string
    },

    // Chat Screen translations
    chat: {
      host: string;
      input_placeholder: string;
      search: string;
      error: {
        conversation_required: string;
        send_failed: string;
      };
      success: {
        message_sent: string;
      };
        title: string;
      unreadMessages_one: string;
      unreadMessages_other: string;
      clearSearch: string;
      emptyTitle: string;
      emptyText: string;
      noSearchResults: string;
      searchNoResults: string;
      retry: string;
      loading: string;
      justNow: string;
      secondsAgo: string;
      minutesAgo: string;
      hoursAgo: string;
      daysAgo: string;
      unknown: string;
      noMessages: string;
    },

    accommodation_screen: {
      title: string,
      your_listings: string,
      cohosted: string,
      no_listings: string,
      loading: string,
      error: string,
      capacity: string
    },

    accommodation_details_screen: {
      about_accommodation: string,
      medias: string,
      amenities: string,
      update: string,
      delete_listing: string,
      delete_confirmation: string,
      delete_success: string,
      delete_error: string,
      cancel: string,
      delete: string,
      continue: string,
      loading: string,
      error: string,
      invite_cohost: string,
      send_invitation: string,
      enter_email: string,
      email_required: string,
      invalid_email: string,
      invalid_email_message: string,
      invite_error: string,
      user_not_found_or_not_host: string,
      invite_success: string
    },

    accommodation_update_screen: {
      header_title: string,
      update_button: string,
      general_information: string,
      photo: string,
      location: string,
      location_subtitle: string,
      media: string,
      media_subtitle: string,
      amenities_services: string,
      amenities_subtitle: string,
      points_of_interest: string,
      poi_subtitle: string,
      address: string,
      latitude: string,
      longitude: string,
      use_current_location: string,
      media_description: string,
      photo_button: string,
      video_button: string,
      audio_button: string,
      pdf_button: string,
      place_name: string,
      add_place: string,
      added_places: string,
      update_listing: string,
      required_fields: string,
      success: string,
      error: string,
      success_message: string,
      error_message: string
    },

    // Services translations
    services: {
      title: string,
      search: string,
      noListings: string,
      loading: string,
      error: string,
      retry: string,
      category: string,
      viewAll: string,
      categories: {
        diy: string,
        gardening: string,
        housekeeping: string
      },
      serviceCard: {
        reviews_one: string,
        reviews_other: string,
        distance: string
      },
      details: {
        serviceNotFound: string,
        information: string,
        email: string,
        phone: string,
        name: string,
        company: string,
        gender: string,
        category: string,
        media: string,
        location: string,
        contact: string,
        seeContact: string,
        chatWithPro: string,
        payment: {
          completePayment: string,
          loadingPayment: string,
          successful: string,
          viewContactDetails: string
        },
        error: {
          errorMessage: string
        }
      },
      create_service: {
        basic_info: {
          title: string,
          service_title: {
            placeholder: string
          },
          price: {
            placeholder: string
          },
          description: {
            placeholder: string
          },
          address: {
            placeholder: string
          },
          location: {
            latitude: string,
            longitude: string
          }
        },
        media: {
          title: string,
          main_photo: {
            title: string,
            placeholder: string,
            add_button: string
          },
          add_media: {
            image: string,
            video: string,
            document: string
          },
          description_placeholder: string,
          cover_badge: string,
          set_cover: string,
          empty_state: {
            title: string,
            subtitle: string
          },
          processing: string
        }
      }
    },

    // Tab bar translations
    tab_bar: {
      listings: string,
      bookings: string,
      services: string,
      subscription: string,
      chat: string,
      profile: string,
      activities: string
    },

    // Invitation status translations
    invitation_status: {
      pending: string,
      accepted: string,
      rejected: string
    },

    // Invitation card translations
    invitation_card: {
      delete_title: string,
      delete_message: string,
      delete_error: string,
      invitations_title: string,
      no_invitations: string
    }
}