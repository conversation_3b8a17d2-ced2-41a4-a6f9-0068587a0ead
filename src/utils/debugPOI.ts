/**
 * Debug utility for POI issues
 * Run this to test POI functionality and identify crash causes
 */

import { problematicPOIs, edgeCasePOIs, validatePOI, sanitizePOI, createMockPlacePrediction } from './poiTestUtils';

/**
 * Debug function to test the specific POIs that cause crashes
 */
export const debugProblematicPOIs = () => {
  console.log('🔍 Debugging Problematic POIs...');
  
  // Test the specific restaurants mentioned by the user
  const cannesRestaurants = [
    {
      name: "Cote Croisette",
      address: "3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France",
      distance: "5 m"
    },
    {
      name: "Hop Sushi", 
      address: "3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France",
      distance: "5 m"
    }
  ];

  cannesRestaurants.forEach((restaurant, index) => {
    console.log(`\n--- Testing Restaurant ${index + 1}: ${restaurant.name} ---`);
    
    // Test different scenarios that might cause crashes
    const testScenarios = [
      // Scenario 1: Missing coordinates
      {
        name: restaurant.name,
        address: restaurant.address,
        // No coordinates - this might cause the crash
      },
      // Scenario 2: Invalid coordinates
      {
        name: restaurant.name,
        address: restaurant.address,
        latitude: NaN,
        longitude: undefined,
      },
      // Scenario 3: String coordinates (common API issue)
      {
        name: restaurant.name,
        address: restaurant.address,
        latitude: "43.5528" as any,
        longitude: "7.0174" as any,
      },
      // Scenario 4: Valid coordinates
      {
        name: restaurant.name,
        address: restaurant.address,
        latitude: 43.5528,
        longitude: 7.0174,
      }
    ];

    testScenarios.forEach((scenario, scenarioIndex) => {
      console.log(`\n  Scenario ${scenarioIndex + 1}:`, scenario);
      
      try {
        // Test validation
        const isValid = validatePOI(scenario);
        console.log(`  ✅ Validation: ${isValid ? 'PASS' : 'FAIL'}`);
        
        // Test sanitization
        const sanitized = sanitizePOI(scenario);
        console.log(`  ✅ Sanitization: ${sanitized ? 'PASS' : 'FAIL'}`, sanitized);
        
        // Test mock place prediction creation
        if (sanitized) {
          const mockPrediction = createMockPlacePrediction(sanitized);
          console.log(`  ✅ Mock Prediction: PASS`);
          
          // Test the structure that would be passed to handleAddSelectedPlace
          const testStructure = {
            placePrediction: mockPrediction.placePrediction,
            placeDetails: mockPrediction.placeDetails
          };
          
          // Simulate the problematic code paths
          const mainText = testStructure.placePrediction?.structuredFormat?.mainText?.text;
          const secondaryText = testStructure.placePrediction?.structuredFormat?.secondaryText?.text;
          const location = testStructure.placeDetails?.location || testStructure.placePrediction?.location;
          
          console.log(`  ✅ Structure Access: mainText="${mainText}", secondaryText="${secondaryText}", location=`, location);
          
          // Test coordinate validation
          if (location && typeof location.latitude === 'number' && typeof location.longitude === 'number') {
            console.log(`  ✅ Coordinates: Valid (${location.latitude}, ${location.longitude})`);
          } else {
            console.log(`  ⚠️  Coordinates: Invalid or missing`);
          }
        }
        
      } catch (error) {
        console.error(`  ❌ ERROR in scenario ${scenarioIndex + 1}:`, error);
      }
    });
  });
};

/**
 * Test the improved error handling in PointsOfInterestSection
 */
export const testImprovedErrorHandling = () => {
  console.log('\n🛡️  Testing Improved Error Handling...');
  
  // Test cases that previously caused crashes
  const crashTestCases = [
    null,
    undefined,
    {},
    { placePrediction: null },
    { placePrediction: {} },
    { 
      placePrediction: {
        structuredFormat: null
      }
    },
    {
      placePrediction: {
        structuredFormat: {
          mainText: null,
          secondaryText: null
        }
      }
    },
    {
      placePrediction: {
        structuredFormat: {
          mainText: { text: "Cote Croisette" },
          secondaryText: { text: "3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France" }
        },
        placeId: "test-place-id"
      },
      placeDetails: {
        location: null // This might cause the crash
      }
    }
  ];

  crashTestCases.forEach((testCase, index) => {
    console.log(`\n--- Crash Test Case ${index + 1} ---`);
    console.log('Input:', testCase);
    
    try {
      // Simulate the improved handleAddSelectedPlace logic
      if (!testCase) {
        console.log('✅ Early return for null/undefined');
        return;
      }

      const placePrediction = (testCase as any).placePrediction;
      if (!placePrediction) {
        console.log('✅ Early return for missing placePrediction');
        return;
      }

      const mainText = placePrediction.structuredFormat?.mainText?.text || 
                      placePrediction.displayName?.text || 
                      'Unknown Place';

      const secondaryText = placePrediction.structuredFormat?.secondaryText?.text || 
                           placePrediction.formattedAddress || 
                           'Unknown Address';

      const location = (testCase as any).placeDetails?.location || 
                      placePrediction?.location || 
                      { latitude: 0, longitude: 0 };

      console.log(`✅ Safe extraction: "${mainText}", "${secondaryText}"`);
      console.log(`✅ Location fallback:`, location);

      // Test coordinate validation
      if (!location || typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {
        console.log('✅ Invalid coordinates detected - would show error alert');
        return;
      }

      console.log('✅ All validations passed - POI would be added successfully');
      
    } catch (error) {
      console.error('❌ Unexpected error (this should not happen with improved code):', error);
    }
  });
};

/**
 * Test suggestion rendering with problematic data
 */
export const testSuggestionRendering = () => {
  console.log('\n🎯 Testing Suggestion Rendering...');

  const problematicSuggestions = [
    // Missing structuredFormat
    {
      placePrediction: {
        placeId: 'test-1',
        // Missing structuredFormat
      }
    },
    // Missing mainText
    {
      placePrediction: {
        placeId: 'test-2',
        structuredFormat: {
          // Missing mainText
          secondaryText: { text: 'Some address' }
        }
      }
    },
    // Missing text property
    {
      placePrediction: {
        placeId: 'test-3',
        structuredFormat: {
          mainText: {}, // Missing text property
          secondaryText: { text: 'Some address' }
        }
      }
    },
    // Valid suggestion
    {
      placePrediction: {
        placeId: 'test-4',
        structuredFormat: {
          mainText: { text: 'Cote Croisette' },
          secondaryText: { text: '3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France' }
        }
      }
    }
  ];

  problematicSuggestions.forEach((suggestion, index) => {
    console.log(`\n--- Testing Suggestion ${index + 1} ---`);
    console.log('Input:', suggestion);

    try {
      const placePrediction = suggestion?.placePrediction;
      if (!placePrediction) {
        console.log('✅ Would skip: Invalid placePrediction');
        return;
      }

      const mainText = placePrediction.structuredFormat?.mainText?.text ||
                      placePrediction.displayName?.text ||
                      'Unknown place';

      const secondaryText = placePrediction.structuredFormat?.secondaryText?.text ||
                           placePrediction.formattedAddress ||
                           'Unknown address';

      console.log(`✅ Safe extraction: "${mainText}", "${secondaryText}"`);

    } catch (error) {
      console.error('❌ Error in suggestion processing:', error);
    }
  });
};

/**
 * Main debug function
 */
export const runPOIDebugTests = () => {
  console.log('🚀 Starting POI Debug Tests...\n');

  debugProblematicPOIs();
  testImprovedErrorHandling();
  testSuggestionRendering();

  console.log('\n✅ POI Debug Tests Complete!');
  console.log('\n📋 Summary:');
  console.log('- Added robust error handling to prevent crashes');
  console.log('- Added validation for all POI data structures');
  console.log('- Added fallbacks for missing or invalid data');
  console.log('- Added user-friendly error messages');
  console.log('- Fixed suggestion rendering with safe property access');
  console.log('\n🔧 Next steps:');
  console.log('1. Test the app with the problematic POIs');
  console.log('2. Check console logs for any remaining issues');
  console.log('3. Report back if crashes still occur');
};

// Export for use in development
if (__DEV__) {
  (global as any).debugPOI = {
    runTests: runPOIDebugTests,
    debugProblematic: debugProblematicPOIs,
    testErrorHandling: testImprovedErrorHandling
  };

  // Auto-run tests in development to verify fixes
  console.log('🔧 POI Debug Tools Available:');
  console.log('- debugPOI.runTests() - Run all tests');
  console.log('- debugPOI.debugProblematic() - Test problematic POIs');
  console.log('- debugPOI.testErrorHandling() - Test error handling');
}
