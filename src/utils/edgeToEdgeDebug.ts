import { Platform, Dimensions } from 'react-native';

export const EdgeToEdgeDebugInfo = {
  /**
   * Get debug information about the current edge-to-edge setup
   */
  getDebugInfo: () => {
    const { width, height } = Dimensions.get('window');
    const screen = Dimensions.get('screen');
    
    return {
      platform: Platform.OS,
      version: Platform.Version,
      window: { width, height },
      screen: screen,
      isEdgeToEdgeSupported: Platform.OS === 'android' && Platform.Version >= 23,
      hasNotch: screen.height !== height, // Simple notch detection
    };
  },

  /**
   * Log edge-to-edge debug information
   */
  logDebugInfo: () => {
    const info = EdgeToEdgeDebugInfo.getDebugInfo();
    console.log('🔍 Edge-to-Edge Debug Info:', JSON.stringify(info, null, 2));
  },

  /**
   * Check if the current setup should support edge-to-edge
   */
  shouldSupportEdgeToEdge: () => {
    return Platform.OS === 'android' && Platform.Version >= 23;
  }
};
