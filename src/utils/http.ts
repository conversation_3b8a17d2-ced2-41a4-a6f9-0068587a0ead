import axios, {
  AxiosInstance,
  AxiosError,
  AxiosRequestConfig,
  InternalAxiosRequestConfig
} from 'axios';
import axiosRetry from 'axios-retry';
import Constants from 'expo-constants';
import { AppConstants } from '@/constants/default';
import { storage } from '@/utils/storage';
import { Alert } from 'react-native';
import i18n from '@/i18n';

// Initialize translation
const t = i18n.t;

// Types
export interface ApiError extends Error {
  status?: number;
  code?: string;
  isNetworkError?: boolean;
  isTimeout?: boolean;
  response?: {
    data?: { message?: string };
    status?: number;
  };
}

interface RetryConfig {
  maxRetries: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
}

// Constants
const BASE_URL = Constants.expoConfig?.extra?.API_BASE_URL;
const DEFAULT_TIMEOUT = 10000; // 10 seconds

const RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  initialDelayMs: 1000,
  maxDelayMs: 10000,
  backoffFactor: 2,
};

// Circuit breaker state
let failureCount = 0;
const FAILURE_THRESHOLD = 5;
const RESET_TIMEOUT = 60000; // 1 minute

// Helper functions
const getJwtToken = (): string | null => {
  return storage.getString(AppConstants.TOKEN_KEY) || null;
};

const addJwtToken = (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
  //console.log('Request: ', config);
  const token = getJwtToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
};

const calculateDelay = (retryCount: number, config: RetryConfig): number => {
  const delay = config.initialDelayMs * Math.pow(config.backoffFactor, retryCount);
  const jitter = Math.random() * 100; // Add jitter to prevent thundering herd
  return Math.min(delay + jitter, config.maxDelayMs);
};

const isRetryableError = (error: AxiosError): boolean => {
  const status = error.response?.status;
  console.log("Status: ", status);
  return (
    error.code === 'ECONNABORTED' ||
    error.code === 'ETIMEDOUT' ||
    !status || // Network errors have no status
    status === 408 || // Request Timeout
    status === 429 || // Too Many Requests
    (status >= 500 && status <= 599) // Server errors
  );
};

// Create Axios instance
const createAxiosInstance = (): AxiosInstance => {
  console.log("Base URL: ", BASE_URL);
  const instance = axios.create({
    baseURL: BASE_URL,
    timeout: DEFAULT_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Encoding': 'gzip, deflate, br',
    },
  });

  // Request interceptor for JWT
  instance.interceptors.request.use(addJwtToken);

  // Use axios-retry to handle retry logic
  axiosRetry(instance, {
    retries: RETRY_CONFIG.maxRetries,
    retryDelay: (retryCount: number, error: AxiosError) => calculateDelay(retryCount, RETRY_CONFIG),
    // Do not retry on 401 errors or non-retryable errors.
    retryCondition: (error: AxiosError) => isRetryableError(error) && error.response?.status !== 401,
  });

  // Response interceptor for error handling and circuit breaker logic
  instance.interceptors.response.use(
    (response) => {
      //console.log('Response: ', response);
      // Reset failure count on successful request
      failureCount = 0;
      return response;
    },
    (error: AxiosError) => {
      console.log('Error: ', error.response);
      const errorMessage = (error.response?.data as { message?: string })?.message || t('common.an_error_occurred');
      Alert.alert(t('common.error_title'), errorMessage);

      // Handle 400 Duplicate ressource
      if (error.response?.status === 400) {
        if(error.response?.data?.messages?.[0]?.messageContent === "Reservation is already completed") {
          Alert.alert(t('common.error_title'), t('common.reservation_already_completed'));
        } else {
          Alert.alert(t('common.error_title'), (error.response?.data as { message?: string })?.message || t('common.an_error_occurred'));
        }
        
      }

      // Handle 401 Unauthorized without retrying
      if (error.response?.status === 401) {
        storage.delete(AppConstants.TOKEN_KEY);
      }

      // Increment failure count for circuit breaker logic
      failureCount++;
      if (failureCount >= FAILURE_THRESHOLD) {
        // Reset circuit breaker after a timeout period
        setTimeout(() => {
          failureCount = 0;
        }, RESET_TIMEOUT);
        return Promise.reject(new Error(t('common.circuit_breaker_open')));
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create singleton instance
const http = createAxiosInstance();

// Export HTTP methods with proper typing
export const httpClient = {
  async get<T>(url: string, config?: AxiosRequestConfig) {
    const response = await http.get<T>(url, config);
    return response.data;
  },

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    const response = await http.post<T>(url, data, config);
    console.log("Response: ", response);
    return response.data;
  },

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    const response = await http.put<T>(url, data, config);
    return response.data;
  },

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    const response = await http.patch<T>(url, data, config);
    return response.data;
  },

  async delete<T>(url: string, config?: AxiosRequestConfig) {
    const response = await http.delete<T>(url, config);
    return response.data;
  },

  // Utility methods for request cancellation
  createAbortController() {
    return new AbortController();
  },

  // Creates an abort signal that automatically times out after specified milliseconds
  createTimeoutSignal(timeoutMs: number): AbortSignal {
    // Use AbortSignal.timeout() if available (Node.js 17.3+)
    if (typeof AbortSignal.timeout === 'function') {
      return AbortSignal.timeout(timeoutMs);
    }

    // Fallback for older environments
    const controller = new AbortController();
    setTimeout(() => controller.abort(), timeoutMs);
    return controller.signal;
  },
};
