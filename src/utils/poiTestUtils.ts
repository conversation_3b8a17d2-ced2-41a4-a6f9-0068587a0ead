/**
 * Utility functions for testing POI functionality
 * Helps debug issues with specific POI data that causes crashes
 */

export interface TestPOI {
  name: string;
  address: string;
  latitude?: number;
  longitude?: number;
  placeId?: string;
  types?: string[];
  photo?: string;
}

// Test data based on the problematic POIs mentioned by the user
export const problematicPOIs: TestPOI[] = [
  {
    name: "Cote Croisette",
    address: "3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France",
    // These coordinates might be missing or invalid, causing the crash
    latitude: 43.5528,
    longitude: 7.0174,
    types: ["restaurant", "food", "establishment"]
  },
  {
    name: "Hop Sushi",
    address: "3 Rue Latour-Maubourg, Centre-ville de Cannes, 06400 Cannes, France", 
    // These coordinates might be missing or invalid, causing the crash
    latitude: 43.5528,
    longitude: 7.0174,
    types: ["restaurant", "food", "establishment", "meal_takeaway"]
  }
];

// Test cases that might cause crashes
export const edgeCasePOIs: TestPOI[] = [
  // POI with missing coordinates
  {
    name: "Test Restaurant",
    address: "Test Address",
    // Missing latitude/longitude
  },
  // POI with invalid coordinates
  {
    name: "Invalid Coords Restaurant",
    address: "Invalid Address",
    latitude: NaN,
    longitude: undefined as any,
  },
  // POI with null values
  {
    name: null as any,
    address: null as any,
    latitude: null as any,
    longitude: null as any,
  },
  // POI with special characters
  {
    name: "Café de l'Été & Restaurant",
    address: "123 Rue de l'Église, Côte d'Azur, France",
    latitude: 43.5528,
    longitude: 7.0174,
  }
];

/**
 * Validates POI data to prevent crashes
 */
export const validatePOI = (poi: any): boolean => {
  try {
    // Check if POI exists
    if (!poi) {
      console.warn('validatePOI: POI is null or undefined');
      return false;
    }

    // Check required fields
    if (!poi.name || typeof poi.name !== 'string') {
      console.warn('validatePOI: Invalid or missing name');
      return false;
    }

    if (!poi.address || typeof poi.address !== 'string') {
      console.warn('validatePOI: Invalid or missing address');
      return false;
    }

    // Check coordinates if they exist
    if (poi.latitude !== undefined && poi.latitude !== null) {
      if (typeof poi.latitude !== 'number' || isNaN(poi.latitude)) {
        console.warn('validatePOI: Invalid latitude');
        return false;
      }
    }

    if (poi.longitude !== undefined && poi.longitude !== null) {
      if (typeof poi.longitude !== 'number' || isNaN(poi.longitude)) {
        console.warn('validatePOI: Invalid longitude');
        return false;
      }
    }

    // Check types array if it exists
    if (poi.types && !Array.isArray(poi.types)) {
      console.warn('validatePOI: Types is not an array');
      return false;
    }

    return true;
  } catch (error) {
    console.error('validatePOI: Error during validation:', error);
    return false;
  }
};

/**
 * Safely processes POI data to prevent crashes
 */
export const sanitizePOI = (poi: any): TestPOI | null => {
  try {
    if (!validatePOI(poi)) {
      return null;
    }

    return {
      name: String(poi.name).trim(),
      address: String(poi.address).trim(),
      latitude: poi.latitude && !isNaN(Number(poi.latitude)) ? Number(poi.latitude) : undefined,
      longitude: poi.longitude && !isNaN(Number(poi.longitude)) ? Number(poi.longitude) : undefined,
      placeId: poi.placeId ? String(poi.placeId) : undefined,
      types: Array.isArray(poi.types) ? poi.types.filter(type => typeof type === 'string') : undefined,
      photo: poi.photo ? String(poi.photo) : undefined
    };
  } catch (error) {
    console.error('sanitizePOI: Error during sanitization:', error);
    return null;
  }
};

/**
 * Tests POI processing with problematic data
 */
export const testPOIProcessing = () => {
  console.log('=== Testing POI Processing ===');
  
  const allTestPOIs = [...problematicPOIs, ...edgeCasePOIs];
  
  allTestPOIs.forEach((poi, index) => {
    console.log(`\nTesting POI ${index + 1}:`, poi);
    
    const isValid = validatePOI(poi);
    console.log(`Valid: ${isValid}`);
    
    const sanitized = sanitizePOI(poi);
    console.log(`Sanitized:`, sanitized);
  });
  
  console.log('\n=== POI Processing Test Complete ===');
};

/**
 * Simulates the Google Places API response structure
 */
export const createMockPlacePrediction = (poi: TestPOI) => {
  return {
    placePrediction: {
      placeId: poi.placeId || `mock-${Date.now()}`,
      structuredFormat: {
        mainText: {
          text: poi.name
        },
        secondaryText: {
          text: poi.address
        }
      },
      types: poi.types || [],
      location: poi.latitude && poi.longitude ? {
        latitude: poi.latitude,
        longitude: poi.longitude
      } : undefined
    },
    placeDetails: poi.latitude && poi.longitude ? {
      location: {
        latitude: poi.latitude,
        longitude: poi.longitude
      },
      types: poi.types || [],
      photos: poi.photo ? [{ name: poi.photo }] : undefined
    } : undefined
  };
};
