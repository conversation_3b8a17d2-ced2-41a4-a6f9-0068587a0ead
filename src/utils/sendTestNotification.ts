import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export async function scheduleLocalNotification(
  title: string,
  body: string,
  data: Record<string, any> = {}
): Promise<string> {
  console.log('Scheduling local notification with:', { title, body, data });

  // Check notification permissions first
  const { status } = await Notifications.getPermissionsAsync();
  console.log(`Current notification permission status: ${status}`);

  if (status !== 'granted') {
    console.log('Requesting notification permissions...');
    const { status: newStatus } = await Notifications.requestPermissionsAsync();
    console.log(`New notification permission status: ${newStatus}`);

    if (newStatus !== 'granted') {
      console.error('Notification permissions not granted');
      throw new Error('Notification permissions not granted');
    }
  }

  // For Android, ensure we have a notification channel
  if (Platform.OS === 'android') {
    try {
      // Create a test channel with MAX importance
      await Notifications.setNotificationChannelAsync('test-channel', {
        name: 'Test Channel',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
        sound: 'default',
      });
      console.log('Android test notification channel set up successfully');
    } catch (error) {
      console.error('Error setting up Android notification channel:', error);
    }
  }

  try {
    // Use a simpler approach for both platforms
    const identifier = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
        ...(Platform.OS === 'android' && {
          priority: Notifications.AndroidNotificationPriority.MAX,
        }),
      },
      trigger: Platform.OS === 'android' ? {
        type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
        seconds: 1,
        channelId: 'test-channel',
      } : null, // null means send immediately on iOS
    });

    console.log(`Notification scheduled with ID: ${identifier}`);

    // Get all scheduled notifications to verify
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    console.log(`Currently scheduled notifications: ${scheduledNotifications.length}`);

    // Get all presented notifications
    const presentedNotifications = await Notifications.getPresentedNotificationsAsync();
    console.log(`Currently presented notifications: ${presentedNotifications.length}`);

    return identifier;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    throw error;
  }
}

export async function cancelAllNotifications(): Promise<void> {
  await Notifications.cancelAllScheduledNotificationsAsync();
}
