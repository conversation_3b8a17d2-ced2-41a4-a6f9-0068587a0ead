import { storage } from '@utils/storage'; // Import MMKV storage instance
import { AppConstants } from '@constants/default'; // Import constants for keys
import { Platform, Linking } from 'react-native';
import { ref, uploadBytes, getDownloadURL, deleteObject, StorageReference } from 'firebase/storage';
import { firebaseStorage } from '../../firebaseConfig';


// Mark onboarding as completed
export const completeOnboarding = () => {
  try {
    storage.set(AppConstants.ONBOARDING_COMPLETED_KEY, true); // Store as a boolean
    console.log('Onboarding marked as completed');
  } catch (error) {
    console.error('Failed to save onboarding status', error);
  }
};

/**
 * Checks if onboarding is completed
 *
 * @returns The onboarding completion status.
 */
export const checkOnboardingStatus = () => {
  try {
    const completed = storage.getBoolean(AppConstants.ONBOARDING_COMPLETED_KEY); // Retrieve as a boolean
    console.log('Onboarding status:', completed);
    return completed ?? false; // Default to false if the key doesn't exist
  } catch (error) {
    console.error('Failed to check onboarding status', error);
    return false;
  }
};

/**
 * Normalizes a file URI for both iOS and Android.
 *
 * @param fileUri - The original file URI.
 * @returns The normalized file URI string.
 */
const normalizeFileUri = (fileUri: string): string => {
  if (Platform.OS === 'android' && !fileUri.startsWith('file://')) {
    return `file://${fileUri}`;
  }
  return fileUri;
};

/**
 * Custom error type for upload failures.
 */
class UploadError extends Error {
  stage: 'fetch' | 'blob' | 'upload' | 'downloadURL';
  originalError: unknown;
  
  constructor(stage: 'fetch' | 'blob' | 'upload' | 'downloadURL', message: string, originalError: unknown) {
    super(message);
    this.stage = stage;
    this.originalError = originalError;
    Object.setPrototypeOf(this, UploadError.prototype);
  }
}

/**
 * Uploads a local file to Firebase Storage and returns its download URL.
 *
 * @param fileUri - The local URI of the file to upload.
 * @returns A Promise that resolves with the download URL as a string.
 * @throws UploadError with stage info if something goes wrong.
 */
export const uploadFile = async (fileUri: string, folder: string): Promise<string> => {
  console.log('Uploading file:', fileUri);
  // Normalize the URI for Android if necessary
  const normalizedUri: string = normalizeFileUri(fileUri);
  console.log('Normalized URI:', normalizedUri);

  // Convert the file to a Blob
  let blob: Blob;
  try {
    console.log("Fetching file...");
    const response: Response = await fetch(normalizedUri);
    console.log("File fetched:", response);
    blob = await response.blob();
    console.log("File converted to Blob:", blob);
  } catch (error) {
    console.error("Error fetching or converting file to Blob:", error);
    throw new UploadError('fetch', 'Failed to fetch or convert file to Blob', error);
  }

  // Extract file name from the URI
  const fileName: string = normalizedUri.substring(normalizedUri.lastIndexOf('/') + 1);
  
  // Create a reference in Firebase Storage
  const storageRef: StorageReference = ref(firebaseStorage, `uploads/${folder}/${fileName}`);
  console.log("Storage reference created:", storageRef);
  // Upload the Blob
  try {
    await uploadBytes(storageRef, blob);
  } catch (error) {
    console.error("Error uploading file to Firebase Storage:", error);
    throw new UploadError('upload', 'Failed to upload file to Firebase Storage', error);
  }

  // Retrieve and return the download URL
  try {
    const downloadURL: string = await getDownloadURL(storageRef);
    console.log("File available at", downloadURL);
    return downloadURL;
  } catch (error) {
    console.error("Error retrieving download URL:", error);
    throw new UploadError('downloadURL', 'Failed to retrieve download URL', error);
  }
};

/**
 * Extracts the file path from a Firebase Storage download URL.
 * 
 * @param url - The full Firebase Storage download URL.
 * @returns The decoded file path within the storage bucket.
 * @throws Error if the URL format is invalid.
 */
const extractFilePathFromUrl = (url: string): string => {
  try {
    const encodedPath = url.split('/o/')[1].split('?')[0];
    return decodeURIComponent(encodedPath);
  } catch (error) {
    throw new Error('Invalid Firebase Storage URL.');
  }
};

/**
 * Deletes a file from Firebase Storage given its public download URL.
 * 
 * @param downloadUrl - The public URL of the file to delete.
 * @returns A Promise that resolves when the file is successfully deleted.
 */
export const deleteFileFromUrl = async (downloadUrl: string): Promise<void> => {
  console.log(`Deleting file at URL: ${downloadUrl}`);
  try {
    const filePath = extractFilePathFromUrl(downloadUrl);
    const fileRef: StorageReference = ref(firebaseStorage, filePath);
    await deleteObject(fileRef);
    console.log('File deleted successfully:', filePath);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw error;
  }
};


export const openInMaps = async (poi) => {
  const { latitude, longitude, name, address } = poi;
  
  try {
    const encodedName = encodeURIComponent(name);
    const encodedAddress = encodeURIComponent(address);

    let mapsUrl;
    if (Platform.OS === 'ios') {
      // iOS: Use Apple Maps
      mapsUrl = `maps:0,0?q=${encodedName}@${latitude},${longitude}`;
    } else {
      // Android: Use Google Maps
      mapsUrl = `geo:${latitude},${longitude}?q=${encodedName}, ${encodedAddress}`;
    }

    const canOpen = await Linking.canOpenURL(mapsUrl);
    
    if (canOpen) {
      await Linking.openURL(mapsUrl);
    } else {
      // Fallback to web maps if native app is not available
      const webMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      await Linking.openURL(webMapsUrl);
    }
  } catch (error) {
    console.error('Error opening maps:', error);
    // Optional: Show an error toast or alert to the user
  }
};